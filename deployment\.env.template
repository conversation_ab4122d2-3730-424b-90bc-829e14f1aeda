# Security
SECRET_KEY=your-secret-key-change-in-production
PRODUCTION_API_KEY=your-production-api-key

# API Configuration
API_KEY=demo_key_12345
LOG_LEVEL=INFO

# Database (if needed)
DATABASE_URL=postgresql://user:password@localhost/home_credit

# Model Configuration
MODEL_PATH=models/best_home_credit_model.pth
SCALER_PATH=models/feature_scaler.joblib
FEATURE_NAMES_PATH=models/feature_names.json

# Performance
MAX_WORKERS=4
BATCH_SIZE_LIMIT=100
REQUEST_TIMEOUT=30

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
