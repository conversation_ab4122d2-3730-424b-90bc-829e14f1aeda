version: '3.8'

services:
  # FastAPI Backend
  api:
    build: 
      context: ./api
      dockerfile: Dockerfile
    container_name: home_credit_api
    ports:
      - "8000:8000"
    environment:
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - PRODUCTION_API_KEY=${PRODUCTION_API_KEY:-prod_key_67890}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./models:/app/models:ro
      - ./logs:/app/logs
      - ../data:/app/data:ro
      - ../production_models:/app/production_models:ro
      - ../best_advanced_model.pth:/app/best_advanced_model.pth:ro
      - ../optimal_threshold_config.json:/app/optimal_threshold_config.json:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - home_credit_network

  # Streamlit Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: home_credit_frontend
    ports:
      - "8501:8501"
    environment:
      - API_BASE_URL=http://api:8000
      - API_KEY=${API_KEY:-demo_key_12345}
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - home_credit_network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: home_credit_redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - home_credit_network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: home_credit_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - frontend
    restart: unless-stopped
    networks:
      - home_credit_network

volumes:
  redis_data:

networks:
  home_credit_network:
    driver: bridge
