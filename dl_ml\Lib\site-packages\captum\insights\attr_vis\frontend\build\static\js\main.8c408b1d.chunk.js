(this.webpackJsonpfrontend=this.webpackJsonpfrontend||[]).push([[0],{1:function(e,a,t){e.exports={app:"App_app__1kX79",header:"App_header__3ZZ1n",header__name:"App_header__name__11V8z",header__nav:"App_header__nav__1d4-r",header__nav__item:"App_header__nav__item__1sl_l","header__nav__item--active":"App_header__nav__item--active__qHsJb","filter-panel":"App_filter-panel__1_W-D",viz__panel:"App_viz__panel__1DvPS","filter-panel__column":"App_filter-panel__column__tSar1","filter-panel__column__title":"App_filter-panel__column__title__1BI85","filter-panel__column__body":"App_filter-panel__column__body__2gifz","filter-panel__column--end":"App_filter-panel__column--end__11Fot",select:"App_select__lfdUS",input:"App_input__2NxBo","input--narrow":"App_input--narrow__3EP69",row:"App_row__1s1ax","row--padding":"App_row--padding__290gG",btn:"App_btn__34jjX","btn--large":"App_btn--large__1yJzq","btn--outline":"App_btn--outline__Gy9-J","btn--solid":"App_btn--solid__jJmRZ",viz:"App_viz__22ORP",loading:"App_loading__wCN4P",panel:"App_panel___hL33",panel__column__title:"App_panel__column__title__1ZKms","panel--loading":"App_panel--loading__2o16s","panel--center":"App_panel--center__3KnXW",panel__column:"App_panel__column__3x9QU","panel__column--stretch":"App_panel__column--stretch__2o3HO",gallery:"App_gallery__11BWP",gallery__item:"App_gallery__item__2XNr9",gallery__item__image:"App_gallery__item__image__3sU36",gallery__item__description:"App_gallery__item__description__1eCLM","bar-chart__group":"App_bar-chart__group__17Ybq","bar-chart__group__bar":"App_bar-chart__group__bar__1T_FD","bar-chart__group__title":"App_bar-chart__group__title__1gSfe","percentage-blue":"App_percentage-blue__2Y6MH","percentage-light-blue":"App_percentage-light-blue__2W41P","percentage-light-red":"App_percentage-light-red__189Ov","percentage-red":"App_percentage-red__3dSfE","percentage-gray":"App_percentage-gray__HnO9X","percentage-white":"App_percentage-white__3aG-5","text-feature-word":"App_text-feature-word__Spy0f",tooltip__label:"App_tooltip__label__37Y9h","general-feature__label-container":"App_general-feature__label-container__1wWDg","general-feature__label":"App_general-feature__label__Oz5fH","general-feature__percent":"App_general-feature__percent__2FWgI","general-feature__bar-container":"App_general-feature__bar-container__ZNGWt","general-feature__bar":"App_general-feature__bar__3s8ZU","general-feature__bar__positive":"App_general-feature__bar__positive__JbWh6","general-feature__bar__negative":"App_general-feature__bar__negative__Tju-a",spinner:"App_spinner__zg8k4","visualization-container":"App_visualization-container__bvo_s","model-number":"App_model-number__16G7T","model-number-spacer":"App_model-number-spacer__2hiDO","model-separator":"App_model-separator__bibw2"}},155:function(e,a,t){},156:function(e,a,t){"use strict";t.r(a);var n=t(0),l=t.n(n),r=t(45),c=t.n(r),i=(t(53),t(5)),s=t(6),_=t(7),o=t(8),d=t(1),m=t.n(d);var u=function(e){return Array.isArray(e)?e.join(" "):Object.keys(e).filter((function(a){return!!e[a]})).join(" ")};var p=function(){return l.a.createElement("header",{className:m.a.header},l.a.createElement("div",{className:m.a.header__name},"Captum Insights"),l.a.createElement("nav",{className:m.a.header__nav},l.a.createElement("ul",null,l.a.createElement("li",{className:u([m.a.header__nav__item,m.a["header__nav__item--active"]])},"Instance Attribution"))))};var g=function(){return l.a.createElement("div",{className:m.a.spinner})},h=t(4),v=t(18);function f(e){var a=e.limit[0],t=e.limit[1];return l.a.createElement("div",null,e.name,":",l.a.createElement("input",{className:u([m.a.input,m.a["input--narrow"]]),name:e.name,type:"number",value:e.value,min:a,max:t,onChange:e.handleInputChange}))}function b(e){var a=e.limit.map((function(e,a){return l.a.createElement("option",{value:e},e)}));return l.a.createElement("div",null,e.name,":",l.a.createElement("select",{className:m.a.select,name:e.name,value:e.value,onChange:e.handleInputChange},a))}function E(e){return l.a.createElement("div",null,e.name,":",l.a.createElement("input",{className:u([m.a.input,m.a["input--narrow"]]),name:e.name,type:"text",value:e.value,onChange:e.handleInputChange}))}var A=t(46),N=t.n(A);var y,C=function(e){return l.a.createElement(N.a,{tags:e.classes,autofocus:!1,suggestions:e.suggestedClasses,handleDelete:e.handleClassDelete,handleAddition:function(a){if("string"===typeof a.id)throw Error("Invalid tag id received from ReactTags");e.handleClassAdd({id:a.id,name:a.name})},minQueryLength:0,placeholder:"add new class..."})};!function(e){e.Number="number",e.Enum="enum",e.String="string",e.Boolean="boolean"}(y||(y={}));var x=function(e){var a=e.methods.map((function(e,a){return l.a.createElement("option",{key:a,value:e},e)})),t=null;if(e.selectedMethod in e.methodArguments){var n=e.methodArguments[e.selectedMethod];t=Object.keys(n).map((function(a,t){return function(a,t){switch(t.type){case y.Number:return l.a.createElement(f,{key:a,name:a,limit:t.limit,value:t.value,handleInputChange:e.handleArgumentChange});case y.Enum:return l.a.createElement(b,{key:a,name:a,limit:t.limit,value:t.value,handleInputChange:e.handleArgumentChange});case y.String:return l.a.createElement(E,{key:a,name:a,value:t.value,handleInputChange:e.handleArgumentChange});default:throw new Error("Unsupported config type: "+t.type)}}(a,n[a])}))}return l.a.createElement("form",{onSubmit:e.handleSubmit},l.a.createElement("div",{className:m.a["filter-panel"]},l.a.createElement("div",{className:m.a["filter-panel__column"]},l.a.createElement("div",{className:m.a["filter-panel__column__title"]},"Filter by Classes"),l.a.createElement("div",{className:m.a["filter-panel__column__body"]},l.a.createElement(C,{handleClassDelete:e.handleClassDelete,handleClassAdd:e.handleClassAdd,suggestedClasses:e.suggestedClasses,classes:e.classes}))),l.a.createElement("div",{className:m.a["filter-panel__column"]},l.a.createElement("div",{className:m.a["filter-panel__column__title"]},"Filter by Instances"),l.a.createElement("div",{className:m.a["filter-panel__column__body"]},"Prediction:"," ",l.a.createElement("select",{className:m.a.select,name:"prediction",onChange:e.handleInputChange,value:e.prediction},l.a.createElement("option",{value:"all"},"All"),l.a.createElement("option",{value:"correct"},"Correct"),l.a.createElement("option",{value:"incorrect"},"Incorrect")))),l.a.createElement("div",{className:m.a["filter-panel__column"]},l.a.createElement("div",{className:m.a["filter-panel__column__title"]},"Choose Attribution Method"),l.a.createElement("div",{className:m.a["filter-panel__column__body"]},"Attribution Method:"," ",l.a.createElement("select",{className:m.a.select,name:"selected_method",onChange:e.handleInputChange,value:e.selectedMethod},a))),l.a.createElement("div",{className:m.a["filter-panel__column"]},l.a.createElement("div",{className:m.a["filter-panel__column__title"]},"Attribution Method Arguments"),l.a.createElement("div",{className:m.a["filter-panel__column__body"]},t)),l.a.createElement("div",{className:u([m.a["filter-panel__column"],m.a["filter-panel__column--end"]])},l.a.createElement("button",{className:u([m.a.btn,m.a["btn--outline"],m.a["btn--large"]])},"Fetch"))))};function I(e){switch(e.type){case"checkbox":return e.checked;case"number":return parseInt(e.value);default:return e.value}}var k=function(e){Object(o.a)(t,e);var a=Object(_.a)(t);function t(e){var n;Object(i.a)(this,t),(n=a.call(this,e)).handleClassDelete=function(e){var a=n.state.classes.slice(0),t=a.splice(e,1),l=[].concat(Object(v.a)(n.state.suggested_classes),Object(v.a)(t));n.setState({classes:a,suggested_classes:l})},n.handleClassAdd=function(e){var a=[].concat(Object(v.a)(n.state.classes),[e]),t=n.state.suggested_classes.filter((function(a){return a.id!==e.id}));n.setState({classes:a,suggested_classes:t})},n.handleInputChange=function(e){var a=e.target,t=I(e.target),l=a.name;n.setState(Object(h.a)({},l,t))},n.handleArgumentChange=function(e){var a=e.target,t=a.name,l=I(a),r=n.state.method_arguments;r[n.state.selected_method][t].value=l,n.setState({method_arguments:r})},n.handleSubmit=function(e){var a=n.state.selected_method,t=n.state.method_arguments,l=a in t?t[a]:{},r={};Object.keys(l).forEach((function(e){r[e]=l[e].value}));var c={prediction:n.state.prediction,classes:n.state.classes.map((function(e){return e.name})),attribution_method:a,arguments:r};n.props.fetchData(c),e.preventDefault()};var l=e.config.classes.map((function(e,a){return{id:a,name:e}}));return n.state={prediction:"all",classes:[],suggested_classes:l,selected_method:e.config.selected_method,method_arguments:e.config.method_arguments},n}return Object(s.a)(t,[{key:"render",value:function(){return l.a.createElement(x,{prediction:this.state.prediction,classes:this.state.classes,suggestedClasses:this.state.suggested_classes,selectedMethod:this.state.selected_method,methodArguments:this.state.method_arguments,methods:this.props.config.methods,handleClassAdd:this.handleClassAdd,handleClassDelete:this.handleClassDelete,handleInputChange:this.handleInputChange,handleArgumentChange:this.handleArgumentChange,handleSubmit:this.handleSubmit})}}]),t}(l.a.Component);function j(e){var a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=[220,100,80],n=[10,100,67],l=null;l=e>0?t:n;var r=[0,40,a?100:90],c=Math.abs(.01*e);if(c<.02)return"hsl(".concat(r[0],", ").concat(r[1],"%, ").concat(r[2],"%)");var i=[l[0],(l[1]-r[1])*c+r[1],(l[2]-r[2])*c+r[2]];return"hsl(".concat(i[0],", ").concat(i[1],"%, ").concat(i[2],"%)")}var O=function(e){return l.a.createElement("div",{className:m.a.tooltip},l.a.createElement("div",{className:m.a.tooltip__label},e.label))},w=t(47);function S(e){return l.a.createElement(l.a.Fragment,null,e.hideHeaders&&l.a.createElement("div",{className:m.a.panel__column__title},e.data.name," (Image)"),l.a.createElement("div",{className:m.a.panel__column__body},l.a.createElement("div",{className:m.a["model-number-spacer"]}),l.a.createElement("div",{className:m.a.gallery},l.a.createElement("div",{className:m.a.gallery__item},l.a.createElement("div",{className:m.a.gallery__item__image},l.a.createElement("img",{src:"data:image/png;base64,"+e.data.base,alt:"original"})),l.a.createElement("div",{className:m.a.gallery__item__description},"Original")),l.a.createElement("div",{className:m.a.gallery__item},l.a.createElement("div",{className:m.a.gallery__item__image},l.a.createElement("img",{src:"data:image/png;base64,"+e.data.modified,alt:"attribution"})),l.a.createElement("div",{className:m.a.gallery__item__description},"Attribution Magnitude")))))}function T(e){var a=e.data.base.map((function(a,t){var n;return l.a.createElement(l.a.Fragment,null,l.a.createElement("span",{style:{backgroundColor:j(e.data.modified[t],!1)},className:m.a["text-feature-word"]},a,l.a.createElement(O,{label:null===(n=e.data.modified[t])||void 0===n?void 0:n.toFixed(3)}))," ")}));return l.a.createElement(l.a.Fragment,null,e.hideHeaders&&l.a.createElement("div",{className:m.a.panel__column__title},e.data.name," (Text)"),l.a.createElement("div",{className:m.a.panel__column__body},l.a.createElement("div",{className:m.a["model-number-spacer"]}),a))}function D(e){var a={labels:e.data.base,datasets:[{barPercentage:.5,data:e.data.modified,backgroundColor:function(e){return e.dataset&&e.dataset.data&&void 0!==e.datasetIndex?(e.dataset.data[e.dataIndex]||0)<0?"#d45c43":"#80aaff":"#d45c43"}}]};return l.a.createElement(w.Bar,{data:a,width:300,height:50,legend:{display:!1},options:{maintainAspectRatio:!1,scales:{xAxes:[{gridLines:{display:!1}}],yAxes:[{gridLines:{lineWidth:0,zeroLineWidth:1}}]}}})}var z=function(e){var a=e.data;switch(a.type){case"image":return l.a.createElement(S,{data:a,hideHeaders:e.hideHeaders});case"text":return l.a.createElement(T,{data:a,hideHeaders:e.hideHeaders});case"general":return l.a.createElement(D,{data:a});case"empty":return l.a.createElement(l.a.Fragment,null);default:throw new Error("Unsupported feature visualization type: "+a.type)}};var F=function(e){var a;return l.a.createElement("button",{onClick:function(a){a.preventDefault(),e.onTargetClick(e.labelIndex,e.inputIndex,e.modelIndex)},className:u((a={},Object(h.a)(a,m.a.btn,!0),Object(h.a)(a,m.a["btn--solid"],e.active),Object(h.a)(a,m.a["btn--outline"],!e.active),a))},e.children)};var M=function(e){return l.a.createElement(l.a.Fragment,null,e.feature_outputs.map((function(e){var a=100*e.contribution,t=a>10?a:a+10;return l.a.createElement("div",{className:m.a["bar-chart__group"]},l.a.createElement("div",{className:m.a["bar-chart__group__bar"],style:{height:t+"px",backgroundColor:j(a)}}),l.a.createElement("div",{className:m.a["bar-chart__group__title"]},e.name))})))},H=function(e){Object(o.a)(t,e);var a=Object(_.a)(t);function t(e){var n;return Object(i.a)(this,t),(n=a.call(this,e)).onTargetClick=function(e,a,t){n.setState({loading:!0}),n.props.onTargetClick(e,a,t,(function(){return n.setState({loading:!1})}))},n.state={loading:!1},n}return Object(s.a)(t,[{key:"render",value:function(){var e=this,a=this.props.data,t=0===this.props.data.model_index,n=a.feature_outputs.map((function(e){return l.a.createElement(z,{data:e,hideHeaders:t})}));return l.a.createElement(l.a.Fragment,null,this.state.loading&&l.a.createElement("div",{className:m.a.loading},l.a.createElement(g,null)),!t&&l.a.createElement("div",{className:m.a["model-separator"]}),l.a.createElement("div",{className:m.a["visualization-container"]},l.a.createElement("div",{className:m.a.panel__column},t&&l.a.createElement("div",{className:m.a.panel__column__title},"Predicted"),l.a.createElement("div",{className:m.a.panel__column__body},l.a.createElement("div",{className:m.a["model-number"]},"Model ",a.model_index+1),a.predicted.map((function(t){return l.a.createElement("div",{className:u([m.a.row,m.a["row--padding"]])},l.a.createElement(F,{onTargetClick:e.onTargetClick,labelIndex:t.index,inputIndex:e.props.instance,modelIndex:e.props.data.model_index,active:t.index===a.active_index},t.label," (",t.score.toFixed(3),")"))})))),l.a.createElement("div",{className:m.a.panel__column},t&&l.a.createElement("div",{className:m.a.panel__column__title},"Label"),l.a.createElement("div",{className:m.a.panel__column__body},l.a.createElement("div",{className:m.a["model-number-spacer"]}),l.a.createElement("div",{className:u([m.a.row,m.a["row--padding"]])},l.a.createElement(F,{onTargetClick:this.onTargetClick,labelIndex:a.actual.index,inputIndex:this.props.instance,modelIndex:this.props.data.model_index,active:a.actual.index===a.active_index},a.actual.label)))),l.a.createElement("div",{className:m.a.panel__column},t&&l.a.createElement("div",{className:m.a.panel__column__title},"Contribution"),l.a.createElement("div",{className:m.a.panel__column__body},l.a.createElement("div",{className:m.a["model-number-spacer"]}),l.a.createElement("div",{className:m.a["bar-chart"]},l.a.createElement(M,{feature_outputs:a.feature_outputs})))),l.a.createElement("div",{className:u([m.a.panel__column,m.a["panel__column--stretch"]])},n)))}}]),t}(l.a.Component);var P=function(e){var a;return l.a.createElement("div",{className:u((a={},Object(h.a)(a,m.a.panel,!0),Object(h.a)(a,m.a["panel--long"],!0),a))},e.data.map((function(a,t){return l.a.createElement(H,{data:a,instance:e.inputIndex,onTargetClick:e.onTargetClick,key:t})})))};t(155);function W(e){return e.loading?l.a.createElement("div",{className:"viz"},l.a.createElement("div",{className:u([m.a.panel,m.a["panel--center"]])},l.a.createElement(g,null))):e.data&&0!==e.data.length?l.a.createElement("div",{className:m.a.viz},e.data.map((function(a,t){return l.a.createElement(P,{data:a,key:t,inputIndex:t,onTargetClick:e.onTargetClick})}))):l.a.createElement("div",{className:m.a.viz},l.a.createElement("div",{className:m.a.panel},l.a.createElement("div",{className:m.a.panel__column},"Please press"," ",l.a.createElement("strong",{className:m.a["text-feature-word"]},"Fetch")," to start loading data.")))}var J=function(e){Object(o.a)(t,e);var a=Object(_.a)(t);function t(){return Object(i.a)(this,t),a.apply(this,arguments)}return Object(s.a)(t,[{key:"componentDidMount",value:function(){this.props.fetchInit()}},{key:"render",value:function(){return l.a.createElement("div",{className:m.a.app},l.a.createElement(p,null),l.a.createElement(k,{fetchData:this.props.fetchData,config:this.props.config,key:this.props.config.classes}),l.a.createElement(W,{data:this.props.data,loading:this.props.loading,onTargetClick:this.props.onTargetClick}))}}]),t}(l.a.Component),L=function(e){Object(o.a)(t,e);var a=Object(_.a)(t);function t(e){var n;return Object(i.a)(this,t),(n=a.call(this,e))._fetchInit=function(){fetch("init").then((function(e){return e.json()})).then((function(e){return n.setState({config:e})}))},n.fetchData=function(e){n.setState({loading:!0}),fetch("fetch",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}).then((function(e){return e.json()})).then((function(e){return n.setState({data:e,loading:!1})}))},n.onTargetClick=function(e,a,t,l){fetch("attribute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({labelIndex:e,inputIndex:a,modelIndex:t})}).then((function(e){return e.json()})).then((function(e){var r,c=null!==(r=n.state.data)&&void 0!==r?r:[];c[a][t]=e,n.setState({data:c}),l()}))},n.state={data:[],config:{classes:[],methods:[],method_arguments:{},selected_method:""},loading:!1},n._fetchInit(),n}return Object(s.a)(t,[{key:"render",value:function(){return l.a.createElement(J,{fetchData:this.fetchData,fetchInit:this._fetchInit,onTargetClick:this.onTargetClick,data:this.state.data,config:this.state.config,loading:this.state.loading})}}]),t}(l.a.Component);c.a.render(l.a.createElement(L,null),document.getElementById("root"))},48:function(e,a,t){e.exports=t(156)},53:function(e,a,t){}},[[48,1,2]]]);
//# sourceMappingURL=main.8c408b1d.chunk.js.map