{"version": 3, "sources": ["App.module.css", "utils/cx.ts", "components/Header.tsx", "components/Spinner.tsx", "components/Arguments.tsx", "components/ClassFilter.tsx", "models/insightsConfig.ts", "components/Filter.tsx", "components/FilterContainer.tsx", "utils/color.ts", "components/Tooltip.tsx", "components/Feature.tsx", "components/LabelButton.tsx", "components/Contributions.tsx", "components/Visualization.tsx", "components/VisualizationGroup.tsx", "App.tsx", "WebApp.tsx", "index.tsx"], "names": ["module", "exports", "cx", "obj", "Array", "isArray", "join", "Object", "keys", "filter", "k", "Header", "className", "styles", "header", "header__name", "header__nav", "header__nav__item", "Spinner", "spinner", "NumberArgument", "props", "min", "limit", "max", "name", "input", "type", "value", "onChange", "handleInputChange", "EnumArgument", "options", "map", "item", "key", "select", "StringArgument", "ClassFilter", "ArgumentType", "tags", "classes", "autofocus", "suggestions", "suggestedClasses", "handleDelete", "handleClassDelete", "handleAddition", "newTag", "id", "Error", "handleClassAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder", "Filter", "methods", "method_args_components", "<PERSON><PERSON><PERSON><PERSON>", "methodArguments", "method_arguments", "idx", "config", "Number", "handleArgumentChange", "Enum", "String", "createComponentFromConfig", "onSubmit", "handleSubmit", "prediction", "btn", "parseEventTargetValue", "target", "checked", "parseInt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classId", "state", "slice", "removed_class", "splice", "suggested_classes", "setState", "added_class", "t", "event", "selected_method", "method", "argument_config", "args", "for<PERSON>ach", "data", "attribution_method", "arguments", "fetchData", "preventDefault", "c", "this", "React", "Component", "calcHSLFromScore", "percentage", "zeroDefault", "blue_hsl", "red_hsl", "target_hsl", "default_hsl", "abs_percent", "Math", "abs", "color", "<PERSON><PERSON><PERSON>", "tooltip", "label", "ImageFeature", "hideHeaders", "gallery", "src", "base", "alt", "modified", "TextFeature", "color_words", "w", "i", "style", "backgroundColor", "toFixed", "GeneralFeature", "labels", "datasets", "barPercentage", "dataPoint", "dataset", "undefined", "datasetIndex", "dataIndex", "width", "height", "legend", "display", "maintainAspectRatio", "scales", "xAxes", "gridLines", "yAxes", "lineWidth", "zeroLineWidth", "Feature", "LabelButton", "onClick", "e", "onTargetClick", "labelIndex", "inputIndex", "modelIndex", "active", "children", "Contributions", "feature_outputs", "f", "contribution", "bar_height", "Visualization", "loading", "isFirstInGroup", "model_index", "features", "predicted", "p", "row", "index", "instance", "active_index", "score", "actual", "VisualizationGroupDisplay", "panel", "v", "Visualizations", "length", "viz", "vg", "VisualizationGroup", "AppBase", "fetchInit", "app", "WebApp", "_fetchInit", "fetch", "then", "r", "json", "filter_config", "headers", "body", "JSON", "stringify", "response", "callback", "ReactDOM", "render", "document", "getElementById"], "mappings": "uFACAA,EAAOC,QAAU,CAAC,IAAM,iBAAiB,OAAS,oBAAoB,aAAe,0BAA0B,YAAc,yBAAyB,kBAAoB,+BAA+B,4BAA4B,uCAAuC,eAAe,0BAA0B,WAAa,wBAAwB,uBAAuB,kCAAkC,8BAA8B,yCAAyC,6BAA6B,wCAAwC,4BAA4B,uCAAuC,OAAS,oBAAoB,MAAQ,mBAAmB,gBAAgB,2BAA2B,IAAM,iBAAiB,eAAe,0BAA0B,IAAM,iBAAiB,aAAa,wBAAwB,eAAe,0BAA0B,aAAa,wBAAwB,IAAM,iBAAiB,QAAU,qBAAqB,MAAQ,mBAAmB,qBAAuB,kCAAkC,iBAAiB,4BAA4B,gBAAgB,2BAA2B,cAAgB,2BAA2B,yBAAyB,oCAAoC,QAAU,qBAAqB,cAAgB,2BAA2B,qBAAuB,kCAAkC,2BAA6B,wCAAwC,mBAAmB,8BAA8B,wBAAwB,mCAAmC,0BAA0B,qCAAqC,kBAAkB,6BAA6B,wBAAwB,mCAAmC,uBAAuB,kCAAkC,iBAAiB,4BAA4B,kBAAkB,6BAA6B,mBAAmB,8BAA8B,oBAAoB,+BAA+B,eAAiB,4BAA4B,mCAAmC,8CAA8C,yBAAyB,oCAAoC,2BAA2B,sCAAsC,iCAAiC,4CAA4C,uBAAuB,kCAAkC,iCAAiC,4CAA4C,iCAAiC,4CAA4C,QAAU,qBAAqB,0BAA0B,qCAAqC,eAAe,0BAA0B,sBAAsB,iCAAiC,kBAAkB,+B,uJCS5oFC,MATf,SAAYC,GACV,OAAIC,MAAMC,QAAQF,GACTA,EAAIG,KAAK,KAEXC,OAAOC,KAAKL,GAChBM,QAAO,SAACC,GAAD,QAASP,EAAIO,MACpBJ,KAAK,MCiBKK,MApBf,WACE,OACE,4BAAQC,UAAWC,IAAOC,QACxB,yBAAKF,UAAWC,IAAOE,cAAvB,mBACA,yBAAKH,UAAWC,IAAOG,aACrB,4BACE,wBACEJ,UAAWV,EAAG,CACZW,IAAOI,kBACPJ,IAAO,gCAHX,4BCHKK,MAJf,WACE,OAAO,yBAAKN,UAAWC,IAAOM,W,eCOhC,SAASC,EAAeC,GACtB,IAAIC,EAAMD,EAAME,MAAM,GAClBC,EAAMH,EAAME,MAAM,GACtB,OACE,6BACGF,EAAMI,KADT,IAEE,2BACEb,UAAWV,EAAG,CAACW,IAAOa,MAAOb,IAAO,mBACpCY,KAAMJ,EAAMI,KACZE,KAAK,SACLC,MAAOP,EAAMO,MACbN,IAAKA,EACLE,IAAKA,EACLK,SAAUR,EAAMS,qBAMxB,SAASC,EAAaV,GACpB,IAAMW,EAAUX,EAAME,MAAMU,KAAI,SAACC,EAAMC,GAAP,OAC9B,4BAAQP,MAAOM,GAAOA,MAExB,OACE,6BACGb,EAAMI,KADT,IAEE,4BACEb,UAAWC,IAAOuB,OAClBX,KAAMJ,EAAMI,KACZG,MAAOP,EAAMO,MACbC,SAAUR,EAAMS,mBAEfE,IAMT,SAASK,EAAehB,GACtB,OACE,6BACGA,EAAMI,KADT,IAEE,2BACEb,UAAWV,EAAG,CAACW,IAAOa,MAAOb,IAAO,mBACpCY,KAAMJ,EAAMI,KACZE,KAAK,OACLC,MAAOP,EAAMO,MACbC,SAAUR,EAAMS,qB,qBCrBTQ,ICrCHC,EDqCGD,EA1Bf,SAAqBjB,GAanB,OACE,kBAAC,IAAD,CACEmB,KAAMnB,EAAMoB,QACZC,WAAW,EACXC,YAAatB,EAAMuB,iBACnBC,aAAcxB,EAAMyB,kBACpBC,eAlBmB,SAACC,GAKtB,GAAyB,kBAAdA,EAAOC,GAChB,MAAMC,MAAM,0CAEZ7B,EAAM8B,eAAe,CAAEF,GAAID,EAAOC,GAAIxB,KAAMuB,EAAOvB,QAWnD2B,eAAgB,EAChBC,YAAY,uB,SChCNd,K,gBAAAA,E,YAAAA,E,gBAAAA,E,mBAAAA,M,KC2JGe,MAhIf,SAAgBjC,GACd,IAoCMkC,EAAUlC,EAAMkC,QAAQtB,KAAI,SAACC,EAAMC,GAAP,OAChC,4BAAQA,IAAKA,EAAKP,MAAOM,GACtBA,MAGDsB,EAAyB,KAC7B,GAAInC,EAAMoC,kBAAkBpC,EAAMqC,gBAAiB,CACjD,IAAMC,EAAmBtC,EAAMqC,gBAAgBrC,EAAMoC,gBACrDD,EAAyBjD,OAAOC,KAAKmD,GAAkB1B,KAAI,SAACE,EAAKyB,GAAN,OA5C3B,SAACnC,EAAcoC,GAC/C,OAAQA,EAAOlC,MACb,KAAKY,EAAauB,OAChB,OACE,kBAAC1C,EAAD,CACEe,IAAKV,EACLA,KAAMA,EACNF,MAAOsC,EAAOtC,MACdK,MAAOiC,EAAOjC,MACdE,kBAAmBT,EAAM0C,uBAG/B,KAAKxB,EAAayB,KAChB,OACE,kBAACjC,EAAD,CACEI,IAAKV,EACLA,KAAMA,EACNF,MAAOsC,EAAOtC,MACdK,MAAOiC,EAAOjC,MACdE,kBAAmBT,EAAM0C,uBAG/B,KAAKxB,EAAa0B,OAChB,OACE,kBAAC5B,EAAD,CACEF,IAAKV,EACLA,KAAMA,EACNG,MAAOiC,EAAOjC,MACdE,kBAAmBT,EAAM0C,uBAG/B,QACE,MAAM,IAAIb,MAAM,4BAA8BW,EAAOlC,OAavDuC,CAA0B/B,EAAKwB,EAAiBxB,OAGpD,OACE,0BAAMgC,SAAU9C,EAAM+C,cACpB,yBAAKxD,UAAWC,IAAO,iBACrB,yBAAKD,UAAWC,IAAO,yBACrB,yBAAKD,UAAWC,IAAO,gCAAvB,qBAGA,yBAAKD,UAAWC,IAAO,+BACrB,kBAAC,EAAD,CACEiC,kBAAmBzB,EAAMyB,kBACzBK,eAAgB9B,EAAM8B,eACtBP,iBAAkBvB,EAAMuB,iBACxBH,QAASpB,EAAMoB,YAIrB,yBAAK7B,UAAWC,IAAO,yBACrB,yBAAKD,UAAWC,IAAO,gCAAvB,uBAGA,yBAAKD,UAAWC,IAAO,+BAAvB,cACc,IACZ,4BACED,UAAWC,IAAOuB,OAClBX,KAAK,aACLI,SAAUR,EAAMS,kBAChBF,MAAOP,EAAMgD,YAEb,4BAAQzC,MAAM,OAAd,OACA,4BAAQA,MAAM,WAAd,WACA,4BAAQA,MAAM,aAAd,gBAIN,yBAAKhB,UAAWC,IAAO,yBACrB,yBAAKD,UAAWC,IAAO,gCAAvB,6BAGA,yBAAKD,UAAWC,IAAO,+BAAvB,sBACsB,IACpB,4BACED,UAAWC,IAAOuB,OAClBX,KAAK,kBACLI,SAAUR,EAAMS,kBAChBF,MAAOP,EAAMoC,gBAEZF,KAIP,yBAAK3C,UAAWC,IAAO,yBACrB,yBAAKD,UAAWC,IAAO,gCAAvB,gCAGA,yBAAKD,UAAWC,IAAO,+BACpB2C,IAGL,yBACE5C,UAAWV,EAAG,CACZW,IAAO,wBACPA,IAAO,gCAGT,4BACED,UAAWV,EAAG,CACZW,IAAOyD,IACPzD,IAAO,gBACPA,IAAO,iBAJX,aCtIV,SAAS0D,EAAsBC,GAC7B,OAAQA,EAAO7C,MACb,IAAK,WACH,OAAQ6C,EAA4BC,QACtC,IAAK,SACH,OAAOC,SAASF,EAAO5C,OACzB,QACE,OAAO4C,EAAO5C,O,IA8GL+C,E,kDAzFb,WAAYtD,GAA8B,IAAD,uBACvC,cAAMA,IAcRyB,kBAAoB,SAAC8B,GACnB,IAAMnC,EAAU,EAAKoC,MAAMpC,QAAQqC,MAAM,GACnCC,EAAgBtC,EAAQuC,OAAOJ,EAAS,GACxCK,EAAiB,sBAClB,EAAKJ,MAAMI,mBADO,YAElBF,IAEL,EAAKG,SAAS,CAAEzC,UAASwC,uBAtBc,EAyBzC9B,eAAiB,SAACgC,GAChB,IAAM1C,EAAO,sBAAO,EAAKoC,MAAMpC,SAAlB,CAA2B0C,IAClCF,EAAoB,EAAKJ,MAAMI,kBAAkBxE,QACrD,SAAC2E,GAAD,OAAOA,EAAEnC,KAAOkC,EAAYlC,MAE9B,EAAKiC,SAAS,CAAEzC,UAASwC,uBA9Bc,EAiCzCnD,kBAAoB,SAACuD,GACnB,IAAMb,EAASa,EAAMb,OACf5C,EAAQ2C,EAAsBc,EAAMb,QACpC/C,EAAO+C,EAAO/C,KACpB,EAAKyD,SAAL,eACGzD,EAAOG,KAtC6B,EA0CzCmC,qBAAuB,SAACsB,GACtB,IAAMb,EAASa,EAAMb,OACf/C,EAAO+C,EAAO/C,KACdG,EAAQ2C,EAAsBC,GAC9Bb,EAAmB,EAAKkB,MAAMlB,iBACpCA,EAAiB,EAAKkB,MAAMS,iBAAiB7D,GAAMG,MAAQA,EAC3D,EAAKsD,SAAS,CAAEvB,sBAhDuB,EAmDzCS,aAAe,SAACiB,GACd,IAAME,EAAS,EAAKV,MAAMS,gBACpB3B,EAAmB,EAAKkB,MAAMlB,iBAC9B6B,EACJD,KAAU5B,EAAmBA,EAAiB4B,GAAU,GACpDE,EAAqD,GAC3DlF,OAAOC,KAAKgF,GAAiBE,SAAQ,SAAUvD,GAC7CsD,EAAKtD,GAAOqD,EAAgBrD,GAAKP,SAEnC,IAAM+D,EAAO,CACXtB,WAAY,EAAKQ,MAAMR,WACvB5B,QAAS,EAAKoC,MAAMpC,QAAQR,KAAI,SAAC2C,GAAD,OAAaA,EAAO,QACpDgB,mBAAoBL,EACpBM,UAAWJ,GAEb,EAAKpE,MAAMyE,UAAUH,GACrBN,EAAMU,kBAjEN,IAAMd,EAAoB5D,EAAMwC,OAAOpB,QAAQR,KAAI,SAAC+D,EAAGpB,GAAJ,MAAiB,CAClE3B,GAAI2B,EACJnD,KAAMuE,MAJ+B,OAMvC,EAAKnB,MAAQ,CACXR,WAAY,MACZ5B,QAAS,GACTwC,kBAAmBA,EACnBK,gBAAiBjE,EAAMwC,OAAOyB,gBAC9B3B,iBAAkBtC,EAAMwC,OAAOF,kBAXM,E,qDAuEvC,OACE,kBAAC,EAAD,CACEU,WAAY4B,KAAKpB,MAAMR,WACvB5B,QAASwD,KAAKpB,MAAMpC,QACpBG,iBAAkBqD,KAAKpB,MAAMI,kBAC7BxB,eAAgBwC,KAAKpB,MAAMS,gBAC3B5B,gBAAiBuC,KAAKpB,MAAMlB,iBAC5BJ,QAAS0C,KAAK5E,MAAMwC,OAAON,QAC3BJ,eAAgB8C,KAAK9C,eACrBL,kBAAmBmD,KAAKnD,kBACxBhB,kBAAmBmE,KAAKnE,kBACxBiC,qBAAsBkC,KAAKlC,qBAC3BK,aAAc6B,KAAK7B,mB,GAvFG8B,IAAMC,WC9BpC,SAASC,EAAiBC,GAA0C,IAAtBC,EAAqB,wDAC3DC,EAAW,CAAC,IAAK,IAAK,IACtBC,EAAU,CAAC,GAAI,IAAK,IAEtBC,EAAa,KAEfA,EADEJ,EAAa,EACFE,EAEAC,EAGf,IAAME,EAAc,CAAC,EAAG,GAAIJ,EAAc,IAAM,IAC1CK,EAAcC,KAAKC,IAAiB,IAAbR,GAC7B,GAAIM,EAAc,IAChB,MAAM,OAAN,OAAcD,EAAY,GAA1B,aAAiCA,EAAY,GAA7C,cAAqDA,EAAY,GAAjE,MAGF,IAAMI,EAAQ,CACZL,EAAW,IACVA,EAAW,GAAKC,EAAY,IAAMC,EAAcD,EAAY,IAC5DD,EAAW,GAAKC,EAAY,IAAMC,EAAcD,EAAY,IAE/D,MAAM,OAAN,OAAcI,EAAM,GAApB,aAA2BA,EAAM,GAAjC,cAAyCA,EAAM,GAA/C,MCVaC,MARf,SAAiB1F,GACf,OACE,yBAAKT,UAAWC,IAAOmG,SACrB,yBAAKpG,UAAWC,IAAM,gBAAqBQ,EAAM4F,S,QCYvD,SAASC,EAAa7F,GACpB,OACE,oCACGA,EAAM8F,aACL,yBAAKvG,UAAWC,IAAM,sBACnBQ,EAAMsE,KAAKlE,KADd,YAIF,yBAAKb,UAAWC,IAAM,qBACpB,yBAAKD,UAAWC,IAAO,yBACvB,yBAAKD,UAAWC,IAAOuG,SACrB,yBAAKxG,UAAWC,IAAM,eACpB,yBAAKD,UAAWC,IAAM,sBACpB,yBACEwG,IAAK,yBAA2BhG,EAAMsE,KAAK2B,KAC3CC,IAAI,cAGR,yBAAK3G,UAAWC,IAAM,4BAAtB,aAEF,yBAAKD,UAAWC,IAAM,eACpB,yBAAKD,UAAWC,IAAM,sBACpB,yBACEwG,IAAK,yBAA2BhG,EAAMsE,KAAK6B,SAC3CD,IAAI,iBAGR,yBAAK3G,UAAWC,IAAM,4BAAtB,6BAgBZ,SAAS4G,EAAYpG,GACnB,IAAMqG,EAAcrG,EAAMsE,KAAK2B,KAAKrF,KAAI,SAAC0F,EAAGC,GAAO,IAAD,EAChD,OACE,oCACE,0BACEC,MAAO,CACLC,gBAAiB1B,EAAiB/E,EAAMsE,KAAK6B,SAASI,IAAI,IAE5DhH,UAAWC,IAAO,sBAEjB8G,EACD,kBAAC,EAAD,CAASV,MAAK,UAAE5F,EAAMsE,KAAK6B,SAASI,UAAtB,aAAE,EAAwBG,QAAQ,MAC1C,QAId,OACE,oCACG1G,EAAM8F,aACL,yBAAKvG,UAAWC,IAAM,sBACnBQ,EAAMsE,KAAKlE,KADd,WAIF,yBAAKb,UAAWC,IAAM,qBACpB,yBAAKD,UAAWC,IAAO,yBACtB6G,IAYT,SAASM,EAAe3G,GACtB,IAAMsE,EAAO,CACXsC,OAAQ5G,EAAMsE,KAAK2B,KACnBY,SAAU,CACR,CACEC,cAAe,GACfxC,KAAMtE,EAAMsE,KAAK6B,SACjBM,gBAAiB,SAACM,GAChB,OAAKA,EAAUC,SAAYD,EAAUC,QAAQ1C,WAAmC2C,IAA3BF,EAAUG,cAGhDH,EAAUC,QAAQ1C,KAAKyC,EAAUI,YAAwB,GACxD,EAAI,UAAY,UAHvB,cASjB,OACE,kBAAC,MAAD,CACE7C,KAAMA,EACN8C,MAAO,IACPC,OAAQ,GACRC,OAAQ,CAAEC,SAAS,GACnB5G,QAAS,CACP6G,qBAAqB,EACrBC,OAAQ,CACNC,MAAO,CACL,CACEC,UAAW,CACTJ,SAAS,KAIfK,MAAO,CACL,CACED,UAAW,CACTE,UAAW,EACXC,cAAe,SA0BhBC,MAhBf,SAAiB/H,GACf,IAAMsE,EAAOtE,EAAMsE,KACnB,OAAQA,EAAKhE,MACX,IAAK,QACH,OAAO,kBAACuF,EAAD,CAAcvB,KAAMA,EAAMwB,YAAa9F,EAAM8F,cACtD,IAAK,OACH,OAAO,kBAACM,EAAD,CAAa9B,KAAMA,EAAMwB,YAAa9F,EAAM8F,cACrD,IAAK,UACH,OAAO,kBAACa,EAAD,CAAgBrC,KAAMA,IAC/B,IAAK,QACH,OAAO,qCACT,QACE,MAAM,IAAIzC,MAAM,2CAA6CyC,EAAKhE,QC3HzD0H,MApBf,SAAqBhI,GAAmD,IAAD,EAMrE,OACE,4BACEiI,QAPY,SAACC,GACfA,EAAExD,iBACF1E,EAAMmI,cAAcnI,EAAMoI,WAAYpI,EAAMqI,WAAYrI,EAAMsI,aAM5D/I,UAAWV,GAAE,mBACVW,IAAOyD,KAAM,GADH,cAEVzD,IAAO,cAAgBQ,EAAMuI,QAFnB,cAGV/I,IAAO,iBAAmBQ,EAAMuI,QAHtB,KAMZvI,EAAMwI,WCGEC,MAzBf,SAAuBzI,GACrB,OACE,oCACGA,EAAM0I,gBAAgB9H,KAAI,SAAC+H,GAG1B,IAAMC,EAAgC,IAAjBD,EAAEC,aACjBC,EAAaD,EAAe,GAAKA,EAAeA,EAAe,GACrE,OACE,yBAAKrJ,UAAWC,IAAO,qBACrB,yBACED,UAAWC,IAAO,yBAClBgH,MAAO,CACLa,OAAQwB,EAAa,KACrBpC,gBAAiB1B,EAAiB6D,MAGtC,yBAAKrJ,UAAWC,IAAO,4BAA6BmJ,EAAEvI,YCwGnD0I,E,kDAtGb,WAAY9I,GAA4B,IAAD,8BACrC,cAAMA,IAMRmI,cAAgB,SACdC,EACAC,EACAC,GAEA,EAAKzE,SAAS,CAAEkF,SAAS,IACzB,EAAK/I,MAAMmI,cAAcC,EAAYC,EAAYC,GAAY,kBAC3D,EAAKzE,SAAS,CAAEkF,SAAS,QAZ3B,EAAKvF,MAAQ,CACXuF,SAAS,GAH0B,E,qDAmB7B,IAAD,OACDzE,EAAOM,KAAK5E,MAAMsE,KAClB0E,EAAiD,IAAhCpE,KAAK5E,MAAMsE,KAAK2E,YACjCC,EAAW5E,EAAKoE,gBAAgB9H,KAAI,SAAC+H,GAAD,OACxC,kBAAC,EAAD,CAASrE,KAAMqE,EAAG7C,YAAakD,OAGjC,OACE,oCACGpE,KAAKpB,MAAMuF,SACV,yBAAKxJ,UAAWC,IAAOuJ,SACrB,kBAAC,EAAD,QAGFC,GAAkB,yBAAKzJ,UAAWC,IAAO,qBAC3C,yBAAKD,UAAWC,IAAO,4BACrB,yBAAKD,UAAWC,IAAM,eACnBwJ,GACC,yBAAKzJ,UAAWC,IAAM,sBAAtB,aAEF,yBAAKD,UAAWC,IAAM,qBACpB,yBAAKD,UAAWC,IAAO,iBAAvB,SACS8E,EAAK2E,YAAc,GAE3B3E,EAAK6E,UAAUvI,KAAI,SAACwI,GAAD,OAClB,yBAAK7J,UAAWV,EAAG,CAACW,IAAO6J,IAAK7J,IAAO,mBACrC,kBAAC,EAAD,CACE2I,cAAe,EAAKA,cACpBC,WAAYgB,EAAEE,MACdjB,WAAY,EAAKrI,MAAMuJ,SACvBjB,WAAY,EAAKtI,MAAMsE,KAAK2E,YAC5BV,OAAQa,EAAEE,QAAUhF,EAAKkF,cAExBJ,EAAExD,MAPL,KAOcwD,EAAEK,MAAM/C,QAAQ,GAP9B,WAaR,yBAAKnH,UAAWC,IAAM,eACnBwJ,GACC,yBAAKzJ,UAAWC,IAAM,sBAAtB,SAEF,yBAAKD,UAAWC,IAAM,qBACpB,yBAAKD,UAAWC,IAAO,yBACvB,yBAAKD,UAAWV,EAAG,CAACW,IAAO6J,IAAK7J,IAAO,mBACrC,kBAAC,EAAD,CACE2I,cAAevD,KAAKuD,cACpBC,WAAY9D,EAAKoF,OAAOJ,MACxBjB,WAAYzD,KAAK5E,MAAMuJ,SACvBjB,WAAY1D,KAAK5E,MAAMsE,KAAK2E,YAC5BV,OAAQjE,EAAKoF,OAAOJ,QAAUhF,EAAKkF,cAElClF,EAAKoF,OAAO9D,UAKrB,yBAAKrG,UAAWC,IAAM,eACnBwJ,GACC,yBAAKzJ,UAAWC,IAAM,sBAAtB,gBAEF,yBAAKD,UAAWC,IAAM,qBACpB,yBAAKD,UAAWC,IAAO,yBACvB,yBAAKD,UAAWC,IAAO,cACrB,kBAAC,EAAD,CAAekJ,gBAAiBpE,EAAKoE,qBAI3C,yBACEnJ,UAAWV,EAAG,CACZW,IAAM,cACNA,IAAO,6BAGR0J,S,GAlGerE,IAAMC,WCanB6E,MApBf,SAAmC3J,GAAwC,IAAD,EACxE,OACE,yBACET,UAAWV,GAAE,mBACVW,IAAOoK,OAAQ,GADL,cAEVpK,IAAO,gBAAiB,GAFd,KAKZQ,EAAMsE,KAAK1D,KAAI,SAACiJ,EAAGtD,GAAJ,OACd,kBAAC,EAAD,CACEjC,KAAMuF,EACNN,SAAUvJ,EAAMqI,WAChBF,cAAenI,EAAMmI,cACrBrH,IAAKyF,S,OCRf,SAASuD,EAAe9J,GACtB,OAAIA,EAAM+I,QAEN,yBAAKxJ,UAAU,OACb,yBAAKA,UAAWV,EAAG,CAACW,IAAOoK,MAAOpK,IAAO,oBACvC,kBAAC,EAAD,QAMHQ,EAAMsE,MAA8B,IAAtBtE,EAAMsE,KAAKyF,OAc5B,yBAAKxK,UAAWC,IAAOwK,KACpBhK,EAAMsE,KAAK1D,KAAI,SAACqJ,EAAI1D,GAAL,OACd,kBAAC2D,EAAD,CACE5F,KAAM2F,EACNnJ,IAAKyF,EACL8B,WAAY9B,EACZ4B,cAAenI,EAAMmI,oBAlBzB,yBAAK5I,UAAWC,IAAOwK,KACrB,yBAAKzK,UAAWC,IAAOoK,OACrB,yBAAKrK,UAAWC,IAAM,eAAtB,eACe,IACb,4BAAQD,UAAWC,IAAO,sBAA1B,SAFF,6B,IA6DK2K,E,kLAtBXvF,KAAK5E,MAAMoK,c,+BAIX,OACE,yBAAK7K,UAAWC,IAAO6K,KACrB,kBAAC,EAAD,MACA,kBAAC,EAAD,CACE5F,UAAWG,KAAK5E,MAAMyE,UACtBjC,OAAQoC,KAAK5E,MAAMwC,OACnB1B,IAAK8D,KAAK5E,MAAMwC,OAAOpB,UAEzB,kBAAC0I,EAAD,CACExF,KAAMM,KAAK5E,MAAMsE,KACjByE,QAASnE,KAAK5E,MAAM+I,QACpBZ,cAAevD,KAAK5E,MAAMmI,qB,GAjBdtD,IAAMC,WCSbwF,E,kDAtEb,WAAYtK,GAAY,IAAD,8BACrB,cAAMA,IAcRuK,WAAa,WACXC,MAAM,QACHC,MAAK,SAACC,GAAD,OAAOA,EAAEC,UACdF,MAAK,SAACC,GAAD,OAAO,EAAK7G,SAAS,CAAErB,OAAQkI,QAlBlB,EAqBvBjG,UAAY,SAACmG,GACX,EAAK/G,SAAS,CAAEkF,SAAS,IACzByB,MAAM,QAAS,CACbtG,OAAQ,OACR2G,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUJ,KAEpBH,MAAK,SAACQ,GAAD,OAAcA,EAASN,UAC5BF,MAAK,SAACQ,GAAD,OAAc,EAAKpH,SAAS,CAAES,KAAM2G,EAAUlC,SAAS,QA/B1C,EAkCvBZ,cAAgB,SACdC,EACAC,EACAC,EACA4C,GAEAV,MAAM,YAAa,CACjBtG,OAAQ,OACR2G,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAU,CAAE5C,aAAYC,aAAYC,iBAE9CmC,MAAK,SAACQ,GAAD,OAAcA,EAASN,UAC5BF,MAAK,SAACQ,GAAc,IAAD,EACZ3G,EAAI,UAAG,EAAKd,MAAMc,YAAd,QAAsB,GAChCA,EAAK+D,GAAYC,GAAc2C,EAC/B,EAAKpH,SAAS,CAAES,SAChB4G,QAlDJ,EAAK1H,MAAQ,CACXc,KAAM,GACN9B,OAAQ,CACNpB,QAAS,GACTc,QAAS,GACTI,iBAAkB,GAClB2B,gBAAiB,IAEnB8E,SAAS,GAEX,EAAKwB,aAZgB,E,qDAyDrB,OACE,kBAAC,EAAD,CACE9F,UAAWG,KAAKH,UAChB2F,UAAWxF,KAAK2F,WAChBpC,cAAevD,KAAKuD,cACpB7D,KAAMM,KAAKpB,MAAMc,KACjB9B,OAAQoC,KAAKpB,MAAMhB,OACnBuG,QAASnE,KAAKpB,MAAMuF,c,GAjEPlE,IAAMC,WCP3BqG,IAASC,OAAO,kBAAC,EAAD,MAAYC,SAASC,eAAe,U", "file": "static/js/main.8c408b1d.chunk.js", "sourcesContent": ["// extracted by mini-css-extract-plugin\nmodule.exports = {\"app\":\"App_app__1kX79\",\"header\":\"App_header__3ZZ1n\",\"header__name\":\"App_header__name__11V8z\",\"header__nav\":\"App_header__nav__1d4-r\",\"header__nav__item\":\"App_header__nav__item__1sl_l\",\"header__nav__item--active\":\"App_header__nav__item--active__qHsJb\",\"filter-panel\":\"App_filter-panel__1_W-D\",\"viz__panel\":\"App_viz__panel__1DvPS\",\"filter-panel__column\":\"App_filter-panel__column__tSar1\",\"filter-panel__column__title\":\"App_filter-panel__column__title__1BI85\",\"filter-panel__column__body\":\"App_filter-panel__column__body__2gifz\",\"filter-panel__column--end\":\"App_filter-panel__column--end__11Fot\",\"select\":\"App_select__lfdUS\",\"input\":\"App_input__2NxBo\",\"input--narrow\":\"App_input--narrow__3EP69\",\"row\":\"App_row__1s1ax\",\"row--padding\":\"App_row--padding__290gG\",\"btn\":\"App_btn__34jjX\",\"btn--large\":\"App_btn--large__1yJzq\",\"btn--outline\":\"App_btn--outline__Gy9-J\",\"btn--solid\":\"App_btn--solid__jJmRZ\",\"viz\":\"App_viz__22ORP\",\"loading\":\"App_loading__wCN4P\",\"panel\":\"App_panel___hL33\",\"panel__column__title\":\"App_panel__column__title__1ZKms\",\"panel--loading\":\"App_panel--loading__2o16s\",\"panel--center\":\"App_panel--center__3KnXW\",\"panel__column\":\"App_panel__column__3x9QU\",\"panel__column--stretch\":\"App_panel__column--stretch__2o3HO\",\"gallery\":\"App_gallery__11BWP\",\"gallery__item\":\"App_gallery__item__2XNr9\",\"gallery__item__image\":\"App_gallery__item__image__3sU36\",\"gallery__item__description\":\"App_gallery__item__description__1eCLM\",\"bar-chart__group\":\"App_bar-chart__group__17Ybq\",\"bar-chart__group__bar\":\"App_bar-chart__group__bar__1T_FD\",\"bar-chart__group__title\":\"App_bar-chart__group__title__1gSfe\",\"percentage-blue\":\"App_percentage-blue__2Y6MH\",\"percentage-light-blue\":\"App_percentage-light-blue__2W41P\",\"percentage-light-red\":\"App_percentage-light-red__189Ov\",\"percentage-red\":\"App_percentage-red__3dSfE\",\"percentage-gray\":\"App_percentage-gray__HnO9X\",\"percentage-white\":\"App_percentage-white__3aG-5\",\"text-feature-word\":\"App_text-feature-word__Spy0f\",\"tooltip__label\":\"App_tooltip__label__37Y9h\",\"general-feature__label-container\":\"App_general-feature__label-container__1wWDg\",\"general-feature__label\":\"App_general-feature__label__Oz5fH\",\"general-feature__percent\":\"App_general-feature__percent__2FWgI\",\"general-feature__bar-container\":\"App_general-feature__bar-container__ZNGWt\",\"general-feature__bar\":\"App_general-feature__bar__3s8ZU\",\"general-feature__bar__positive\":\"App_general-feature__bar__positive__JbWh6\",\"general-feature__bar__negative\":\"App_general-feature__bar__negative__Tju-a\",\"spinner\":\"App_spinner__zg8k4\",\"visualization-container\":\"App_visualization-container__bvo_s\",\"model-number\":\"App_model-number__16G7T\",\"model-number-spacer\":\"App_model-number-spacer__2hiDO\",\"model-separator\":\"App_model-separator__bibw2\"};", "// helper method to convert an array or object into a valid classname\nfunction cx(obj: any) {\n  if (Array.isArray(obj)) {\n    return obj.join(\" \");\n  }\n  return Object.keys(obj)\n    .filter((k) => !!obj[k])\n    .join(\" \");\n}\n\nexport default cx;\n", "import React from \"react\";\nimport styles from \"../App.module.css\";\nimport cx from \"../utils/cx\";\n\nfunction Header() {\n  return (\n    <header className={styles.header}>\n      <div className={styles.header__name}>Captum Insights</div>\n      <nav className={styles.header__nav}>\n        <ul>\n          <li\n            className={cx([\n              styles.header__nav__item,\n              styles[\"header__nav__item--active\"],\n            ])}\n          >\n            Instance Attribution\n          </li>\n        </ul>\n      </nav>\n    </header>\n  );\n}\n\nexport default Header;\n", "import React from \"react\";\nimport styles from \"../App.module.css\";\n\nfunction Spinner() {\n  return <div className={styles.spinner} />;\n}\n\nexport default Spinner;\n", "import React from \"react\";\nimport styles from \"../App.module.css\";\nimport cx from \"../utils/cx\";\nimport { GenericArgumentConfig } from \"../models/insightsConfig\";\nimport { UserInputField } from \"../models/typeHelpers\";\n\ninterface ArgumentProps {\n  name: string;\n  handleInputChange: React.ChangeEventHandler<UserInputField>;\n}\n\nfunction NumberArgument(props: ArgumentProps & GenericArgumentConfig<number>) {\n  var min = props.limit[0];\n  var max = props.limit[1];\n  return (\n    <div>\n      {props.name}:\n      <input\n        className={cx([styles.input, styles[\"input--narrow\"]])}\n        name={props.name}\n        type=\"number\"\n        value={props.value}\n        min={min}\n        max={max}\n        onChange={props.handleInputChange}\n      />\n    </div>\n  );\n}\n\nfunction EnumArgument(props: ArgumentProps & GenericArgumentConfig<string>) {\n  const options = props.limit.map((item, key) => (\n    <option value={item}>{item}</option>\n  ));\n  return (\n    <div>\n      {props.name}:\n      <select\n        className={styles.select}\n        name={props.name}\n        value={props.value}\n        onChange={props.handleInputChange}\n      >\n        {options}\n      </select>\n    </div>\n  );\n}\n\nfunction StringArgument(props: ArgumentProps & { value: string }) {\n  return (\n    <div>\n      {props.name}:\n      <input\n        className={cx([styles.input, styles[\"input--narrow\"]])}\n        name={props.name}\n        type=\"text\"\n        value={props.value}\n        onChange={props.handleInputChange}\n      />\n    </div>\n  );\n}\n\nexport { StringArgument, EnumArgument, NumberArgument };\n", "import React from \"react\";\nimport ReactTags from \"react-tag-autocomplete\";\nimport { TagClass } from \"../models/filter\";\n\ninterface ClassFilterProps {\n  suggestedClasses: TagClass[];\n  classes: TagClass[];\n  handleClassDelete: (classId: number) => void;\n  handleClassAdd: (newClass: TagClass) => void;\n}\n\nfunction ClassFilter(props: ClassFilterProps) {\n  const handleAddition = (newTag: { id: number | string; name: string }) => {\n    /**\n     * Need this type check as we expect tagId to be number while the `react-tag-autocomplete` has\n     * id as number | string.\n     */\n    if (typeof newTag.id === \"string\") {\n      throw Error(\"Invalid tag id received from ReactTags\");\n    } else {\n      props.handleClassAdd({ id: newTag.id, name: newTag.name });\n    }\n  };\n\n  return (\n    <ReactTags\n      tags={props.classes}\n      autofocus={false}\n      suggestions={props.suggestedClasses}\n      handleDelete={props.handleClassDelete}\n      handleAddition={handleAddition}\n      minQueryLength={0}\n      placeholder=\"add new class...\"\n    />\n  );\n}\n\nexport default ClassFilter;\n", "export enum ArgumentType {\n  Number = \"number\",\n  Enum = \"enum\",\n  String = \"string\",\n  Boolean = \"boolean\",\n}\n\nexport type GenericArgumentConfig<T> = {\n  value: T;\n  limit: T[];\n};\n\nexport type ArgumentConfig =\n  | ({ type: ArgumentType.Number } & GenericArgumentConfig<number>)\n  | ({ type: ArgumentType.Enum } & GenericArgumentConfig<string>)\n  | ({ type: ArgumentType.String } & { value: string })\n  | ({ type: ArgumentType.Boolean } & { value: boolean });\n\nexport interface MethodsArguments {\n  [method_name: string]: {\n    [arg_name: string]: ArgumentConfig;\n  };\n}\n\nexport interface InsightsConfig {\n  classes: string[];\n  methods: string[];\n  method_arguments: MethodsArguments;\n  selected_method: string;\n}\n", "import React from \"react\";\nimport { StringArgument, EnumArgument, NumberArgument } from \"./Arguments\";\nimport cx from \"../utils/cx\";\nimport styles from \"../App.module.css\";\nimport ClassFilter from \"./ClassFilter\";\nimport {\n  MethodsArguments,\n  ArgumentConfig,\n  ArgumentType,\n} from \"../models/insightsConfig\";\nimport { TagClass } from \"../models/filter\";\nimport { UserInputField } from \"../models/typeHelpers\";\n\ninterface FilterProps {\n  prediction: string;\n  selectedMethod: string;\n  methodArguments: MethodsArguments;\n  suggestedClasses: TagClass[];\n  classes: TagClass[];\n  methods: string[];\n  handleInputChange: React.ChangeEventHandler<UserInputField>;\n  handleArgumentChange: React.ChangeEventHandler<UserInputField>;\n  handleSubmit: React.FormEventHandler<HTMLFormElement>;\n  handleClassAdd: (newClass: TagClass) => void;\n  handleClassDelete: (id: number) => void;\n}\n\nfunction Filter(props: FilterProps) {\n  const createComponentFromConfig = (name: string, config: ArgumentConfig) => {\n    switch (config.type) {\n      case ArgumentType.Number:\n        return (\n          <NumberArgument\n            key={name}\n            name={name}\n            limit={config.limit}\n            value={config.value}\n            handleInputChange={props.handleArgumentChange}\n          />\n        );\n      case ArgumentType.Enum:\n        return (\n          <EnumArgument\n            key={name}\n            name={name}\n            limit={config.limit}\n            value={config.value}\n            handleInputChange={props.handleArgumentChange}\n          />\n        );\n      case ArgumentType.String:\n        return (\n          <StringArgument\n            key={name}\n            name={name}\n            value={config.value}\n            handleInputChange={props.handleArgumentChange}\n          />\n        );\n      default:\n        throw new Error(\"Unsupported config type: \" + config.type);\n    }\n  };\n\n  const methods = props.methods.map((item, key) => (\n    <option key={key} value={item}>\n      {item}\n    </option>\n  ));\n  var method_args_components = null;\n  if (props.selectedMethod in props.methodArguments) {\n    const method_arguments = props.methodArguments[props.selectedMethod];\n    method_args_components = Object.keys(method_arguments).map((key, idx) =>\n      createComponentFromConfig(key, method_arguments[key])\n    );\n  }\n  return (\n    <form onSubmit={props.handleSubmit}>\n      <div className={styles[\"filter-panel\"]}>\n        <div className={styles[\"filter-panel__column\"]}>\n          <div className={styles[\"filter-panel__column__title\"]}>\n            Filter by Classes\n          </div>\n          <div className={styles[\"filter-panel__column__body\"]}>\n            <ClassFilter\n              handleClassDelete={props.handleClassDelete}\n              handleClassAdd={props.handleClassAdd}\n              suggestedClasses={props.suggestedClasses}\n              classes={props.classes}\n            />\n          </div>\n        </div>\n        <div className={styles[\"filter-panel__column\"]}>\n          <div className={styles[\"filter-panel__column__title\"]}>\n            Filter by Instances\n          </div>\n          <div className={styles[\"filter-panel__column__body\"]}>\n            Prediction:{\" \"}\n            <select\n              className={styles.select}\n              name=\"prediction\"\n              onChange={props.handleInputChange}\n              value={props.prediction}\n            >\n              <option value=\"all\">All</option>\n              <option value=\"correct\">Correct</option>\n              <option value=\"incorrect\">Incorrect</option>\n            </select>\n          </div>\n        </div>\n        <div className={styles[\"filter-panel__column\"]}>\n          <div className={styles[\"filter-panel__column__title\"]}>\n            Choose Attribution Method\n          </div>\n          <div className={styles[\"filter-panel__column__body\"]}>\n            Attribution Method:{\" \"}\n            <select\n              className={styles.select}\n              name=\"selected_method\"\n              onChange={props.handleInputChange}\n              value={props.selectedMethod}\n            >\n              {methods}\n            </select>\n          </div>\n        </div>\n        <div className={styles[\"filter-panel__column\"]}>\n          <div className={styles[\"filter-panel__column__title\"]}>\n            Attribution Method Arguments\n          </div>\n          <div className={styles[\"filter-panel__column__body\"]}>\n            {method_args_components}\n          </div>\n        </div>\n        <div\n          className={cx([\n            styles[\"filter-panel__column\"],\n            styles[\"filter-panel__column--end\"],\n          ])}\n        >\n          <button\n            className={cx([\n              styles.btn,\n              styles[\"btn--outline\"],\n              styles[\"btn--large\"],\n            ])}\n          >\n            Fetch\n          </button>\n        </div>\n      </div>\n    </form>\n  );\n}\n\nexport default Filter;\n", "import React from \"react\";\nimport Filter from \"./Filter\";\nimport { InsightsConfig, MethodsArguments } from \"../models/insightsConfig\";\nimport { TagClass, FilterConfig } from \"../models/filter\";\nimport { UserInputField } from \"../models/typeHelpers\";\n\nfunction parseEventTargetValue(target: UserInputField) {\n  switch (target.type) {\n    case \"checkbox\":\n      return (target as HTMLInputElement).checked;\n    case \"number\":\n      return parseInt(target.value);\n    default:\n      return target.value;\n  }\n}\n\ninterface FilterContainerProps {\n  config: InsightsConfig;\n  fetchData: (filter_config: FilterConfig) => void;\n}\n\ninterface FilterContainerState {\n  prediction: string;\n  classes: TagClass[];\n  suggested_classes: TagClass[];\n  selected_method: string;\n  method_arguments: MethodsArguments;\n}\n\nclass FilterContainer extends React.Component<\n  FilterContainerProps,\n  FilterContainerState\n> {\n  constructor(props: FilterContainerProps) {\n    super(props);\n    const suggested_classes = props.config.classes.map((c, classId) => ({\n      id: classId,\n      name: c,\n    }));\n    this.state = {\n      prediction: \"all\",\n      classes: [],\n      suggested_classes: suggested_classes,\n      selected_method: props.config.selected_method,\n      method_arguments: props.config.method_arguments,\n    };\n  }\n\n  handleClassDelete = (classId: number) => {\n    const classes = this.state.classes.slice(0);\n    const removed_class = classes.splice(classId, 1);\n    const suggested_classes = [\n      ...this.state.suggested_classes,\n      ...removed_class,\n    ];\n    this.setState({ classes, suggested_classes });\n  };\n\n  handleClassAdd = (added_class: TagClass) => {\n    const classes = [...this.state.classes, added_class];\n    const suggested_classes = this.state.suggested_classes.filter(\n      (t) => t.id !== added_class.id\n    );\n    this.setState({ classes, suggested_classes });\n  };\n\n  handleInputChange = (event: React.ChangeEvent<UserInputField>) => {\n    const target = event.target;\n    const value = parseEventTargetValue(event.target);\n    const name = target.name;\n    this.setState({\n      [name]: value,\n    } as any);\n  };\n\n  handleArgumentChange = (event: React.ChangeEvent<UserInputField>) => {\n    const target = event.target;\n    const name = target.name;\n    const value = parseEventTargetValue(target);\n    const method_arguments = this.state.method_arguments;\n    method_arguments[this.state.selected_method][name].value = value;\n    this.setState({ method_arguments });\n  };\n\n  handleSubmit = (event: React.FormEvent) => {\n    const method = this.state.selected_method;\n    const method_arguments = this.state.method_arguments;\n    const argument_config =\n      method in method_arguments ? method_arguments[method] : {};\n    const args: { [key: string]: string | boolean | number } = {};\n    Object.keys(argument_config).forEach(function (key) {\n      args[key] = argument_config[key].value;\n    });\n    const data = {\n      prediction: this.state.prediction,\n      classes: this.state.classes.map((classId) => classId[\"name\"]),\n      attribution_method: method,\n      arguments: args,\n    };\n    this.props.fetchData(data);\n    event.preventDefault();\n  };\n\n  render() {\n    return (\n      <Filter\n        prediction={this.state.prediction}\n        classes={this.state.classes}\n        suggestedClasses={this.state.suggested_classes}\n        selectedMethod={this.state.selected_method}\n        methodArguments={this.state.method_arguments}\n        methods={this.props.config.methods}\n        handleClassAdd={this.handleClassAdd}\n        handleClassDelete={this.handleClassDelete}\n        handleInputChange={this.handleInputChange}\n        handleArgumentChange={this.handleArgumentChange}\n        handleSubmit={this.handleSubmit}\n      />\n    );\n  }\n}\n\nexport default FilterContainer;\n", "function calcHSLFromScore(percentage: number, zeroDefault = false) {\n  const blue_hsl = [220, 100, 80];\n  const red_hsl = [10, 100, 67];\n\n  let target_hsl = null;\n  if (percentage > 0) {\n    target_hsl = blue_hsl;\n  } else {\n    target_hsl = red_hsl;\n  }\n\n  const default_hsl = [0, 40, zeroDefault ? 100 : 90];\n  const abs_percent = Math.abs(percentage * 0.01);\n  if (abs_percent < 0.02) {\n    return `hsl(${default_hsl[0]}, ${default_hsl[1]}%, ${default_hsl[2]}%)`;\n  }\n\n  const color = [\n    target_hsl[0],\n    (target_hsl[1] - default_hsl[1]) * abs_percent + default_hsl[1],\n    (target_hsl[2] - default_hsl[2]) * abs_percent + default_hsl[2],\n  ];\n  return `hsl(${color[0]}, ${color[1]}%, ${color[2]}%)`;\n}\n\nexport { calcHSLFromScore };\n", "import React from \"react\";\n\nimport styles from \"../App.module.css\";\n\nfunction Tooltip(props: { label: string }) {\n  return (\n    <div className={styles.tooltip}>\n      <div className={styles[\"tooltip__label\"]}>{props.label}</div>\n    </div>\n  );\n}\n\nexport default Tooltip;\n", "import { calcHSLFromScore } from \"../utils/color\";\nimport { DataPoint } from \"../utils/dataPoint\";\nimport React from \"react\";\nimport styles from \"../App.module.css\";\nimport Tooltip from \"./Tooltip\";\nimport { Bar } from \"react-chartjs-2\";\nimport { FeatureOutput } from \"../models/visualizationOutput\";\n\ninterface FeatureProps<T> {\n  data: T;\n  hideHeaders?: boolean;\n}\n\ntype ImageFeatureProps = FeatureProps<{\n  base: string;\n  modified: string;\n  name: string;\n}>;\n\nfunction ImageFeature(props: ImageFeatureProps) {\n  return (\n    <>\n      {props.hideHeaders && (\n        <div className={styles[\"panel__column__title\"]}>\n          {props.data.name} (Image)\n        </div>\n      )}\n      <div className={styles[\"panel__column__body\"]}>\n        <div className={styles[\"model-number-spacer\"]} />\n        <div className={styles.gallery}>\n          <div className={styles[\"gallery__item\"]}>\n            <div className={styles[\"gallery__item__image\"]}>\n              <img\n                src={\"data:image/png;base64,\" + props.data.base}\n                alt=\"original\"\n              />\n            </div>\n            <div className={styles[\"gallery__item__description\"]}>Original</div>\n          </div>\n          <div className={styles[\"gallery__item\"]}>\n            <div className={styles[\"gallery__item__image\"]}>\n              <img\n                src={\"data:image/png;base64,\" + props.data.modified}\n                alt=\"attribution\"\n              />\n            </div>\n            <div className={styles[\"gallery__item__description\"]}>\n              Attribution Magnitude\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n\ntype TextFeatureProps = FeatureProps<{\n  base: number[];\n  name: string;\n  modified: number[];\n}>;\n\nfunction TextFeature(props: TextFeatureProps) {\n  const color_words = props.data.base.map((w, i) => {\n    return (\n      <>\n        <span\n          style={{\n            backgroundColor: calcHSLFromScore(props.data.modified[i], false),\n          }}\n          className={styles[\"text-feature-word\"]}\n        >\n          {w}\n          <Tooltip label={props.data.modified[i]?.toFixed(3)} />\n        </span>{\" \"}\n      </>\n    );\n  });\n  return (\n    <>\n      {props.hideHeaders && (\n        <div className={styles[\"panel__column__title\"]}>\n          {props.data.name} (Text)\n        </div>\n      )}\n      <div className={styles[\"panel__column__body\"]}>\n        <div className={styles[\"model-number-spacer\"]} />\n        {color_words}\n      </div>\n    </>\n  );\n}\n\ntype GeneralFeatureProps = FeatureProps<{\n  base: number[];\n  modified: number[];\n  name: string;\n}>;\n\nfunction GeneralFeature(props: GeneralFeatureProps) {\n  const data = {\n    labels: props.data.base,\n    datasets: [\n      {\n        barPercentage: 0.5,\n        data: props.data.modified,\n        backgroundColor: (dataPoint: DataPoint) => {\n          if (!dataPoint.dataset || !dataPoint.dataset.data || dataPoint.datasetIndex === undefined) {\n            return \"#d45c43\"; // Default to red\n          }\n          const yValue = dataPoint.dataset.data[dataPoint.dataIndex as number] || 0;\n          return yValue < 0 ? \"#d45c43\" : \"#80aaff\"; // Red if negative, else blue\n        },\n      },\n    ],\n  };\n\n  return (\n    <Bar\n      data={data}\n      width={300}\n      height={50}\n      legend={{ display: false }}\n      options={{\n        maintainAspectRatio: false,\n        scales: {\n          xAxes: [\n            {\n              gridLines: {\n                display: false,\n              },\n            },\n          ],\n          yAxes: [\n            {\n              gridLines: {\n                lineWidth: 0,\n                zeroLineWidth: 1,\n              },\n            },\n          ],\n        },\n      }}\n    />\n  );\n}\n\nfunction Feature(props: { data: FeatureOutput; hideHeaders: boolean }) {\n  const data = props.data;\n  switch (data.type) {\n    case \"image\":\n      return <ImageFeature data={data} hideHeaders={props.hideHeaders} />;\n    case \"text\":\n      return <TextFeature data={data} hideHeaders={props.hideHeaders} />;\n    case \"general\":\n      return <GeneralFeature data={data} />;\n    case \"empty\":\n      return <></>;\n    default:\n      throw new Error(\"Unsupported feature visualization type: \" + data.type);\n  }\n}\n\nexport default Feature;\n", "import React from \"react\";\nimport cx from \"../utils/cx\";\nimport styles from \"../App.module.css\";\n\ninterface LabelButtonProps {\n  labelIndex: number;\n  inputIndex: number;\n  modelIndex: number;\n  active: boolean;\n  onTargetClick: (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number\n  ) => void;\n}\n\nfunction LabelButton(props: React.PropsWithChildren<LabelButtonProps>) {\n  const onClick = (e: React.MouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    props.onTargetClick(props.labelIndex, props.inputIndex, props.modelIndex);\n  };\n\n  return (\n    <button\n      onClick={onClick}\n      className={cx({\n        [styles.btn]: true,\n        [styles[\"btn--solid\"]]: props.active,\n        [styles[\"btn--outline\"]]: !props.active,\n      })}\n    >\n      {props.children}\n    </button>\n  );\n}\n\nexport default LabelButton;\n", "import React from \"react\";\nimport styles from \"../App.module.css\";\nimport { calcHSLFromScore } from \"../utils/color\";\nimport { FeatureOutput } from \"../models/visualizationOutput\";\n\ninterface ContributionsProps {\n  feature_outputs: FeatureOutput[];\n}\n\nfunction Contributions(props: ContributionsProps) {\n  return (\n    <>\n      {props.feature_outputs.map((f) => {\n        // pad bar height so features with 0 contribution can still be seen\n        // in graph\n        const contribution = f.contribution * 100;\n        const bar_height = contribution > 10 ? contribution : contribution + 10;\n        return (\n          <div className={styles[\"bar-chart__group\"]}>\n            <div\n              className={styles[\"bar-chart__group__bar\"]}\n              style={{\n                height: bar_height + \"px\",\n                backgroundColor: calcHSLFromScore(contribution),\n              }}\n            />\n            <div className={styles[\"bar-chart__group__title\"]}>{f.name}</div>\n          </div>\n        );\n      })}\n    </>\n  );\n}\n\nexport default Contributions;\n", "import React from \"react\";\nimport styles from \"../App.module.css\";\nimport cx from \"../utils/cx\";\nimport Feature from \"./Feature\";\nimport Spinner from \"./Spinner\";\nimport LabelButton from \"./LabelButton\";\nimport Contributions from \"./Contributions\";\nimport { VisualizationOutput } from \"../models/visualizationOutput\";\n\ninterface VisualizationProps {\n  data: VisualizationOutput;\n  instance: number;\n  onTargetClick: (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number,\n    callback: () => void\n  ) => void;\n}\n\ninterface VisualizationState {\n  loading: boolean;\n}\n\nclass Visualization extends React.Component<\n  VisualizationProps,\n  VisualizationState\n> {\n  constructor(props: VisualizationProps) {\n    super(props);\n    this.state = {\n      loading: false,\n    };\n  }\n\n  onTargetClick = (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number\n  ) => {\n    this.setState({ loading: true });\n    this.props.onTargetClick(labelIndex, inputIndex, modelIndex, () =>\n      this.setState({ loading: false })\n    );\n  };\n\n  //TODO: Refactor the visualization table as a <table> instead of columns, in order to have cleaner styling\n  render() {\n    const data = this.props.data;\n    const isFirstInGroup = this.props.data.model_index === 0;\n    const features = data.feature_outputs.map((f) => (\n      <Feature data={f} hideHeaders={isFirstInGroup} />\n    ));\n\n    return (\n      <>\n        {this.state.loading && (\n          <div className={styles.loading}>\n            <Spinner />\n          </div>\n        )}\n        {!isFirstInGroup && <div className={styles[\"model-separator\"]} />}\n        <div className={styles[\"visualization-container\"]}>\n          <div className={styles[\"panel__column\"]}>\n            {isFirstInGroup && (\n              <div className={styles[\"panel__column__title\"]}>Predicted</div>\n            )}\n            <div className={styles[\"panel__column__body\"]}>\n              <div className={styles[\"model-number\"]}>\n                Model {data.model_index + 1}\n              </div>\n              {data.predicted.map((p) => (\n                <div className={cx([styles.row, styles[\"row--padding\"]])}>\n                  <LabelButton\n                    onTargetClick={this.onTargetClick}\n                    labelIndex={p.index}\n                    inputIndex={this.props.instance}\n                    modelIndex={this.props.data.model_index}\n                    active={p.index === data.active_index}\n                  >\n                    {p.label} ({p.score.toFixed(3)})\n                  </LabelButton>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div className={styles[\"panel__column\"]}>\n            {isFirstInGroup && (\n              <div className={styles[\"panel__column__title\"]}>Label</div>\n            )}\n            <div className={styles[\"panel__column__body\"]}>\n              <div className={styles[\"model-number-spacer\"]} />\n              <div className={cx([styles.row, styles[\"row--padding\"]])}>\n                <LabelButton\n                  onTargetClick={this.onTargetClick}\n                  labelIndex={data.actual.index}\n                  inputIndex={this.props.instance}\n                  modelIndex={this.props.data.model_index}\n                  active={data.actual.index === data.active_index}\n                >\n                  {data.actual.label}\n                </LabelButton>\n              </div>\n            </div>\n          </div>\n          <div className={styles[\"panel__column\"]}>\n            {isFirstInGroup && (\n              <div className={styles[\"panel__column__title\"]}>Contribution</div>\n            )}\n            <div className={styles[\"panel__column__body\"]}>\n              <div className={styles[\"model-number-spacer\"]} />\n              <div className={styles[\"bar-chart\"]}>\n                <Contributions feature_outputs={data.feature_outputs} />\n              </div>\n            </div>\n          </div>\n          <div\n            className={cx([\n              styles[\"panel__column\"],\n              styles[\"panel__column--stretch\"],\n            ])}\n          >\n            {features}\n          </div>\n        </div>\n      </>\n    );\n  }\n}\n\nexport default Visualization;\n", "import React from \"react\";\nimport styles from \"../App.module.css\";\nimport cx from \"../utils/cx\";\nimport Visualization from \"../components/Visualization\";\nimport { VisualizationGroup } from \"../models/visualizationOutput\";\n\ninterface VisualizationGroupDisplayProps {\n  inputIndex: number;\n  data: VisualizationGroup;\n  onTargetClick: (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number,\n    callback: () => void\n  ) => void;\n}\n\nfunction VisualizationGroupDisplay(props: VisualizationGroupDisplayProps) {\n  return (\n    <div\n      className={cx({\n        [styles.panel]: true,\n        [styles[\"panel--long\"]]: true,\n      })}\n    >\n      {props.data.map((v, i) => (\n        <Visualization\n          data={v}\n          instance={props.inputIndex}\n          onTargetClick={props.onTargetClick}\n          key={i}\n        />\n      ))}\n    </div>\n  );\n}\n\nexport default VisualizationGroupDisplay;\n", "import React from \"react\";\nimport styles from \"./App.module.css\";\nimport Header from \"./components/Header\";\nimport cx from \"./utils/cx\";\nimport Spinner from \"./components/Spinner\";\nimport FilterContainer from \"./components/FilterContainer\";\nimport VisualizationGroupDisplay from \"./components/VisualizationGroup\";\nimport \"./App.css\";\nimport { VisualizationGroup } from \"./models/visualizationOutput\";\nimport { FilterConfig } from \"./models/filter\";\n\ninterface VisualizationsProps {\n  loading: boolean;\n  data: VisualizationGroup[];\n  onTargetClick: (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number,\n    callback: () => void\n  ) => void;\n}\n\nfunction Visualizations(props: VisualizationsProps) {\n  if (props.loading) {\n    return (\n      <div className=\"viz\">\n        <div className={cx([styles.panel, styles[\"panel--center\"]])}>\n          <Spinner />\n        </div>\n      </div>\n    );\n  }\n\n  if (!props.data || props.data.length === 0) {\n    return (\n      <div className={styles.viz}>\n        <div className={styles.panel}>\n          <div className={styles[\"panel__column\"]}>\n            Please press{\" \"}\n            <strong className={styles[\"text-feature-word\"]}>Fetch</strong> to\n            start loading data.\n          </div>\n        </div>\n      </div>\n    );\n  }\n  return (\n    <div className={styles.viz}>\n      {props.data.map((vg, i) => (\n        <VisualizationGroupDisplay\n          data={vg}\n          key={i}\n          inputIndex={i}\n          onTargetClick={props.onTargetClick}\n        />\n      ))}\n    </div>\n  );\n}\n\ninterface AppBaseProps {\n  fetchInit: () => void;\n  fetchData: (filter_config: FilterConfig) => void;\n  config: any;\n  data: VisualizationGroup[];\n  loading: boolean;\n  onTargetClick: (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number,\n    callback: () => void\n  ) => void;\n}\n\nclass AppBase extends React.Component<AppBaseProps> {\n  componentDidMount() {\n    this.props.fetchInit();\n  }\n\n  render() {\n    return (\n      <div className={styles.app}>\n        <Header />\n        <FilterContainer\n          fetchData={this.props.fetchData}\n          config={this.props.config}\n          key={this.props.config.classes}\n        />\n        <Visualizations\n          data={this.props.data}\n          loading={this.props.loading}\n          onTargetClick={this.props.onTargetClick}\n        />\n      </div>\n    );\n  }\n}\n\nexport default AppBase;\n", "import React from \"react\";\nimport AppBase from \"./App\";\nimport { FilterConfig } from \"./models/filter\";\nimport { VisualizationGroup } from \"./models/visualizationOutput\";\nimport { InsightsConfig } from \"./models/insightsConfig\";\n\ninterface WebAppState {\n  data: VisualizationGroup[];\n  config: InsightsConfig;\n  loading: boolean;\n}\n\nclass WebApp extends React.Component<{}, WebAppState> {\n  constructor(props: {}) {\n    super(props);\n    this.state = {\n      data: [],\n      config: {\n        classes: [],\n        methods: [],\n        method_arguments: {},\n        selected_method: \"\",\n      },\n      loading: false,\n    };\n    this._fetchInit();\n  }\n\n  _fetchInit = () => {\n    fetch(\"init\")\n      .then((r) => r.json())\n      .then((r) => this.setState({ config: r }));\n  };\n\n  fetchData = (filter_config: FilterConfig) => {\n    this.setState({ loading: true });\n    fetch(\"fetch\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(filter_config),\n    })\n      .then((response) => response.json())\n      .then((response) => this.setState({ data: response, loading: false }));\n  };\n\n  onTargetClick = (\n    labelIndex: number,\n    inputIndex: number,\n    modelIndex: number,\n    callback: () => void\n  ) => {\n    fetch(\"attribute\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ labelIndex, inputIndex, modelIndex }),\n    })\n      .then((response) => response.json())\n      .then((response) => {\n        const data = this.state.data ?? [];\n        data[inputIndex][modelIndex] = response;\n        this.setState({ data });\n        callback();\n      });\n  };\n\n  render() {\n    return (\n      <AppBase\n        fetchData={this.fetchData}\n        fetchInit={this._fetchInit}\n        onTargetClick={this.onTargetClick}\n        data={this.state.data}\n        config={this.state.config}\n        loading={this.state.loading}\n      />\n    );\n  }\n}\n\nexport default WebApp;\n", "import React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport \"./index.css\";\nimport WebApp from \"./WebApp\";\n\nReactDOM.render(<WebApp />, document.getElementById(\"root\"));\n"], "sourceRoot": ""}