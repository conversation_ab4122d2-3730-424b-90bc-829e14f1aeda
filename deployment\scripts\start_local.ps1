# Home Credit Model - Local Development Startup (PowerShell)

Write-Host "🚀 Starting Home Credit Model - Local Development (dl_ml environment)" -ForegroundColor Green

# Check if we're in the correct conda environment
$currentEnv = $env:CONDA_DEFAULT_ENV
if ($currentEnv -ne "dl_ml") {
    Write-Host "⚠️  Please activate the dl_ml environment first:" -ForegroundColor Yellow
    Write-Host "   conda activate dl_ml" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Running in dl_ml environment" -ForegroundColor Green

# Configure models
Write-Host "🔧 Configuring model paths..." -ForegroundColor Blue

# Create models directory
New-Item -ItemType Directory -Path "models" -Force | Out-Null
New-Item -ItemType Directory -Path "configs" -Force | Out-Null

# Check for existing model files
$productionModel = "..\production_models\home_credit_final_20250719_131630\best_model.pt"
$advancedModel = "..\best_advanced_model.pth"
$thresholdConfig = "..\optimal_threshold_config.json"

if (Test-Path $productionModel) {
    Write-Host "✅ Found production model" -ForegroundColor Green
    Copy-Item $productionModel "models\best_home_credit_model.pth" -Force
} else {
    Write-Host "⚠️  Production model not found" -ForegroundColor Yellow
}

if (Test-Path $advancedModel) {
    Write-Host "✅ Found advanced model" -ForegroundColor Green
    Copy-Item $advancedModel "models\best_advanced_model.pth" -Force
} else {
    Write-Host "⚠️  Advanced model not found" -ForegroundColor Yellow
}

if (Test-Path $thresholdConfig) {
    Write-Host "✅ Found threshold config" -ForegroundColor Green
    Copy-Item $thresholdConfig "configs\optimal_threshold_config.json" -Force
} else {
    Write-Host "⚠️  Threshold config not found" -ForegroundColor Yellow
}

# Create feature names file if it doesn't exist
if (-not (Test-Path "models\feature_names.json")) {
    Write-Host "📝 Creating default feature names..." -ForegroundColor Blue
    $featureNames = @(
        "EXT_SOURCE_2", "DAYS_BIRTH", "EXT_SOURCE_3", "DAYS_EMPLOYED", "AMT_CREDIT_SUM_DEBT",
        "AMT_CREDIT", "AMT_ANNUITY", "AMT_GOODS_PRICE", "REGION_POPULATION_RELATIVE",
        "DAYS_REGISTRATION", "DAYS_ID_PUBLISH", "AMT_INCOME_TOTAL", "CNT_CHILDREN",
        "REGION_RATING_CLIENT", "REGION_RATING_CLIENT_W_CITY", "HOUR_APPR_PROCESS_START"
    )
    $featureNames | ConvertTo-Json | Out-File "models\feature_names.json" -Encoding UTF8
}

# Set up environment file
if (-not (Test-Path ".env")) {
    Write-Host "📝 Creating environment file..." -ForegroundColor Blue
    Copy-Item ".env.dev" ".env" -Force
    Write-Host "✅ Created .env from .env.dev" -ForegroundColor Green
} else {
    Write-Host "✅ Environment file already exists" -ForegroundColor Green
}

# Install dependencies if needed
Write-Host "📦 Checking dependencies..." -ForegroundColor Blue

# Check FastAPI dependencies
try {
    python -c "import fastapi" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "📦 Installing API dependencies..." -ForegroundColor Blue
        pip install -r api\requirements.txt
    }
} catch {
    Write-Host "📦 Installing API dependencies..." -ForegroundColor Blue
    pip install -r api\requirements.txt
}

# Check Streamlit dependencies
try {
    python -c "import streamlit" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Blue
        pip install -r frontend\requirements.txt
    }
} catch {
    Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Blue
    pip install -r frontend\requirements.txt
}

Write-Host "✅ Dependencies ready" -ForegroundColor Green

# Start services
Write-Host "🚀 Starting services..." -ForegroundColor Green

# Start API in background
Write-Host "🔧 Starting FastAPI backend..." -ForegroundColor Blue
Set-Location api
$apiProcess = Start-Process python -ArgumentList "main.py" -PassThru -WindowStyle Hidden
Set-Location ..

# Wait for API to start
Start-Sleep -Seconds 3

# Start Streamlit frontend
Write-Host "🖥️  Starting Streamlit frontend..." -ForegroundColor Blue
Set-Location frontend
$frontendProcess = Start-Process streamlit -ArgumentList "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0" -PassThru -WindowStyle Hidden
Set-Location ..

Write-Host ""
Write-Host "✅ Services started successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Access URLs:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:8501" -ForegroundColor White
Write-Host "   API: http://localhost:8000" -ForegroundColor White
Write-Host "   API Docs: http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "📊 Process IDs:" -ForegroundColor Cyan
Write-Host "   API PID: $($apiProcess.Id)" -ForegroundColor White
Write-Host "   Frontend PID: $($frontendProcess.Id)" -ForegroundColor White
Write-Host ""
Write-Host "⏹️  To stop services, close this window or press Ctrl+C" -ForegroundColor Yellow

# Wait for user interrupt
try {
    while ($true) {
        Start-Sleep -Seconds 1
        if ($apiProcess.HasExited -or $frontendProcess.HasExited) {
            Write-Host "⚠️  One of the services has stopped" -ForegroundColor Yellow
            break
        }
    }
} finally {
    Write-Host "⏹️  Stopping services..." -ForegroundColor Yellow
    if (-not $apiProcess.HasExited) { $apiProcess.Kill() }
    if (-not $frontendProcess.HasExited) { $frontendProcess.Kill() }
}
