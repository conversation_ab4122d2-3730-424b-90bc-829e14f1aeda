import enum

class GatingAgreementRequestsReviewStatus(enum.Enum):
  GATING_AGREEMENT_REQUESTS_REVIEW_STATUS_UNSPECIFIED = 0
  GATING_AGREEMENT_REQUESTS_REVIEW_STATUS_PENDING = 1
  GATING_AGREEMENT_REQUESTS_REVIEW_STATUS_ACCEPTED = 2
  GATING_AGREEMENT_REQUESTS_REVIEW_STATUS_REJECTED = 3

class ListModelsOrderBy(enum.Enum):
  LIST_MODELS_ORDER_BY_UNSPECIFIED = 0
  LIST_MODELS_ORDER_BY_HOTNESS = 1
  LIST_MODELS_ORDER_BY_DOWNLOAD_COUNT = 2
  LIST_MODELS_ORDER_BY_VOTE_COUNT = 3
  LIST_MODELS_ORDER_BY_NOTEBOOK_COUNT = 4
  LIST_MODELS_ORDER_BY_PUBLISH_TIME = 5
  LIST_MODELS_ORDER_BY_CREATE_TIME = 6
  LIST_MODELS_ORDER_BY_UPDATE_TIME = 7
  LIST_MODELS_ORDER_BY_VIEW_TIME_DESC = 8

class ModelFramework(enum.Enum):
  MODEL_FRAMEWORK_UNSPECIFIED = 0
  MODEL_FRAMEWORK_TENSOR_FLOW_1 = 1
  MODEL_FRAMEWORK_TENSOR_FLOW_2 = 2
  MODEL_FRAMEWORK_TF_LITE = 3
  MODEL_FRAMEWORK_TF_JS = 4
  MODEL_FRAMEWORK_PY_TORCH = 5
  MODEL_FRAMEWORK_JAX = 6
  MODEL_FRAMEWORK_FLAX = 14
  MODEL_FRAMEWORK_PAX = 15
  MODEL_FRAMEWORK_MAX_TEXT = 17
  MODEL_FRAMEWORK_GEMMA_CPP = 18
  MODEL_FRAMEWORK_GGML = 19
  MODEL_FRAMEWORK_GGUF = 21
  MODEL_FRAMEWORK_CORAL = 7
  MODEL_FRAMEWORK_SCIKIT_LEARN = 8
  MODEL_FRAMEWORK_MXNET = 9
  MODEL_FRAMEWORK_ONNX = 10
  MODEL_FRAMEWORK_KERAS = 11
  MODEL_FRAMEWORK_TRANSFORMERS = 16
  MODEL_FRAMEWORK_API = 12
  MODEL_FRAMEWORK_OTHER = 13
  MODEL_FRAMEWORK_TENSOR_RT_LLM = 20
  MODEL_FRAMEWORK_TRITON = 22

class ModelInstanceType(enum.Enum):
  MODEL_INSTANCE_TYPE_UNSPECIFIED = 0
  MODEL_INSTANCE_TYPE_BASE_MODEL = 1
  MODEL_INSTANCE_TYPE_KAGGLE_VARIANT = 2
  MODEL_INSTANCE_TYPE_EXTERNAL_VARIANT = 3

class ModelVersionLinkType(enum.Enum):
  MODEL_VERSION_LINK_TYPE_UNSPECIFIED = 0
  MODEL_VERSION_LINK_TYPE_VERTEX_OPEN = 1
  MODEL_VERSION_LINK_TYPE_VERTEX_DEPLOY = 2

class GatingAgreementRequestsExpiryStatus(enum.Enum):
  GATING_AGREEMENT_REQUESTS_EXPIRY_STATUS_UNSPECIFIED = 0
  GATING_AGREEMENT_REQUESTS_EXPIRY_STATUS_NOT_EXPIRED = 1
  GATING_AGREEMENT_REQUESTS_EXPIRY_STATUS_IS_EXPIRED = 2

