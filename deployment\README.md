# Home Credit Default Risk - Production Deployment

🏦 **Professional deployment system for Home Credit default risk prediction model**

## 🎯 Model Performance
- **F1-Score**: 0.8876 (exceeds 0.88 target)
- **AUC**: 0.7713
- **Default Rate**: 8.4% (target: 8.1%)
- **Features**: 164 engineered features
- **Architecture**: [512, 256, 128, 64] + ReLU

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  FastAPI Backend │    │ Streamlit UI    │
│   Port: 80/443  │────│   Port: 8000     │────│   Port: 8501    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │  PyTorch Model  │
                       │  Redis Cache    │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Python 3.11+ (for local development)
- Your trained model files

### 1. Setup Environment
```bash
cd deployment
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Configure Environment
```bash
cp .env.template .env.dev
# Edit .env.dev with your settings
```

### 3. Copy Model Files
```bash
# Copy your trained model
cp ../production_models/home_credit_final_*/best_model.pt models/
cp ../optimal_threshold_config.json configs/
```

### 4. Deploy
```bash
./scripts/deploy_dev.sh
```

### 5. Access Services
- **Frontend**: http://localhost:8501
- **API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 📁 Directory Structure

```
deployment/
├── api/                    # FastAPI backend
│   ├── main.py            # Main API application
│   ├── Dockerfile         # API container
│   └── requirements.txt   # Python dependencies
├── frontend/              # Streamlit frontend
│   ├── app.py            # Main UI application
│   ├── Dockerfile        # Frontend container
│   └── requirements.txt  # Python dependencies
├── nginx/                # Reverse proxy
│   └── nginx.conf        # Nginx configuration
├── scripts/              # Deployment scripts
│   ├── setup.sh          # Environment setup
│   ├── deploy_dev.sh     # Development deployment
│   └── stop.sh           # Stop services
├── configs/              # Configuration files
├── models/               # Model files
├── logs/                 # Application logs
├── docker-compose.yml    # Service orchestration
└── README.md            # This file
```

## 🔧 Configuration

### Environment Variables
```bash
# Security
SECRET_KEY=your-secret-key
PRODUCTION_API_KEY=your-api-key

# API Settings
LOG_LEVEL=INFO
MAX_WORKERS=4
BATCH_SIZE_LIMIT=100

# Model Paths
MODEL_PATH=models/best_model.pt
SCALER_PATH=models/scaler.joblib
```

### API Authentication
The API uses Bearer token authentication:
```bash
curl -H "Authorization: Bearer demo_key_12345" \
     http://localhost:8000/health
```

## 📊 API Endpoints

### Health & Info
- `GET /health` - Service health check
- `GET /model/info` - Model performance metrics
- `GET /model/features` - Feature information

### Predictions
- `POST /predict` - Single application prediction
- `POST /predict/batch` - Batch predictions (max 100)

### Example Request
```json
{
  "days_birth": -12000,
  "days_employed": -2000,
  "amt_income_total": 150000,
  "code_gender": "M",
  "amt_credit": 500000,
  "ext_source_2": 0.6,
  "ext_source_3": 0.5
}
```

### Example Response
```json
{
  "default_probability": 0.15,
  "risk_prediction": "APPROVED",
  "confidence_score": 0.85,
  "risk_category": "MEDIUM",
  "model_version": "1.0.0",
  "f1_score": 0.8876,
  "processing_time_ms": 45.2
}
```

## 🖥️ Frontend Features

### Single Prediction
- Interactive form for application data
- Real-time risk assessment
- Feature importance visualization
- Detailed explanations

### Batch Processing
- CSV file upload (max 100 applications)
- Bulk predictions
- Results visualization
- Download processed results

### Analytics Dashboard
- Model performance metrics
- Feature importance charts
- Risk distribution analysis

## 🐳 Docker Commands

### Development
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild
docker-compose up --build -d
```

### Production
```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up -d
```

## 🔍 Monitoring

### Health Checks
```bash
# API health
curl http://localhost:8000/health

# Frontend health
curl http://localhost:8501/_stcore/health
```

### Logs
```bash
# View all logs
docker-compose logs

# API logs only
docker-compose logs api

# Follow logs
docker-compose logs -f
```

## 🛠️ Development

### Local Development
```bash
# API development
cd api
pip install -r requirements.txt
uvicorn main:app --reload

# Frontend development
cd frontend
pip install -r requirements.txt
streamlit run app.py
```

### Testing
```bash
# Run API tests
cd api
pytest

# Test API endpoints
curl -X POST http://localhost:8000/predict \
  -H "Authorization: Bearer demo_key_12345" \
  -H "Content-Type: application/json" \
  -d @sample_request.json
```

## 🔒 Security

### Production Checklist
- [ ] Change default API keys
- [ ] Enable HTTPS with SSL certificates
- [ ] Configure firewall rules
- [ ] Set up monitoring and alerting
- [ ] Regular security updates
- [ ] Backup model files

### SSL Configuration
```bash
# Generate self-signed certificate (development)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/private.key -out ssl/certificate.crt
```

## 📈 Performance

### Optimization Tips
- Use GPU for model inference
- Enable Redis caching
- Configure load balancing
- Monitor resource usage
- Optimize batch sizes

### Scaling
- Horizontal scaling with multiple API instances
- Load balancer configuration
- Database for prediction logging
- Monitoring with Prometheus/Grafana

## 🐛 Troubleshooting

### Common Issues

**Model not loading**
```bash
# Check model file exists
ls -la models/
# Check logs
docker-compose logs api
```

**API connection failed**
```bash
# Check service status
docker-compose ps
# Test health endpoint
curl http://localhost:8000/health
```

**Frontend not accessible**
```bash
# Check Streamlit logs
docker-compose logs frontend
# Verify port mapping
docker-compose ps
```

## 📞 Support

For issues and questions:
1. Check logs: `docker-compose logs`
2. Verify configuration files
3. Test with sample data
4. Review API documentation at `/docs`

---

**🏦 Home Credit Default Risk Prediction System**  
*Production-ready deployment with F1-Score 0.8876*
