"""
HOME CREDIT: TRADITIONAL ML vs NEURAL NETWORK COMPARISON
=======================================================
Compare XGBoost, Random Forest, and other traditional ML models 
with your F1=0.8902 neural network using the same data and target.

Features:
- Uses your engineered features (same as neural network)
- Same target variable and preprocessing
- Comprehensive model comparison
- Feature importance analysis
- Performance benchmarking
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (f1_score, roc_auc_score, accuracy_score, precision_score, 
                           recall_score, classification_report, confusion_matrix, roc_curve)
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
import xgboost as xgb
import lightgbm as lgb
import catboost as cb
import optuna
from typing import Dict, List, Tuple
import time
import warnings
warnings.filterwarnings('ignore')

# =============================================================================
# 1. DATA PREPARATION AND LOADING
# =============================================================================

class HomeCreditDataPreparator:
    """
    Prepare Home Credit data for traditional ML models using the same 
    preprocessing pipeline as your neural network
    """
    
    def __init__(self, data_path: str = "fe_data/application_train_engineered.csv"):
        """
        Initialize with path to your engineered data
        
        Args:
            data_path: Path to the engineered CSV file
        """
        self.data_path = data_path
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_names = None
        self.target_column = "TARGET"  # Adjust if your target column has different name
        
        print("🏠 HOME CREDIT DATA PREPARATOR")
        print("=" * 35)
        print(f"📁 Data path: {data_path}")
    
    def load_and_prepare_data(self, test_size: float = 0.2, random_state: int = 42) -> Tuple:
        """
        Load and prepare data for traditional ML models
        
        Returns:
            X_train, X_test, y_train, y_test, feature_names
        """
        
        print("\n📊 Loading and preparing data...")
        
        # Load the engineered data
        try:
            df = pd.read_csv(self.data_path)
            print(f"✅ Data loaded: {df.shape[0]:,} rows, {df.shape[1]:,} columns")
        except FileNotFoundError:
            print(f"❌ Error: File not found at {self.data_path}")
            print("Please ensure your engineered data is at the correct path")
            return None
        
        # Display basic info
        print(f"📋 Columns: {list(df.columns[:10])}{'...' if len(df.columns) > 10 else ''}")
        
        # Check for target column
        if self.target_column not in df.columns:
            # Try common target column names
            possible_targets = ['TARGET', 'target', 'Target', 'default', 'DEFAULT', 'label', 'LABEL']
            for col in possible_targets:
                if col in df.columns:
                    self.target_column = col
                    print(f"🎯 Found target column: {col}")
                    break
            else:
                print("❌ Target column not found. Available columns:")
                print(df.columns.tolist())
                return None
        
        # Separate features and target
        X = df.drop(columns=[self.target_column])
        y = df[self.target_column]
        
        print(f"🎯 Target distribution:")
        print(f"   Class 0 (No Default): {(y == 0).sum():,} ({(y == 0).mean():.1%})")
        print(f"   Class 1 (Default): {(y == 1).sum():,} ({(y == 1).mean():.1%})")
        
        # Handle missing values
        print(f"\n🔧 Handling missing values...")
        missing_cols = X.columns[X.isnull().any()].tolist()
        if missing_cols:
            print(f"   Found {len(missing_cols)} columns with missing values")
            # Fill numeric columns with median, categorical with mode
            for col in missing_cols:
                if X[col].dtype in ['int64', 'float64']:
                    X[col].fillna(X[col].median(), inplace=True)
                else:
                    X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'Unknown', inplace=True)
        
        # Handle categorical variables
        print(f"🔤 Encoding categorical variables...")
        categorical_cols = X.select_dtypes(include=['object']).columns.tolist()
        if categorical_cols:
            print(f"   Found {len(categorical_cols)} categorical columns")
            for col in categorical_cols:
                le = LabelEncoder()
                X[col] = le.fit_transform(X[col].astype(str))
                self.label_encoders[col] = le
        
        # Store feature names
        self.feature_names = X.columns.tolist()
        print(f"📊 Final feature set: {len(self.feature_names)} features")
        
        # Split the data (same as neural network)
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, 
            stratify=y  # Maintain class distribution
        )
        
        print(f"\n📊 Data split completed:")
        print(f"   Training set: {X_train.shape[0]:,} samples")
        print(f"   Test set: {X_test.shape[0]:,} samples")
        print(f"   Features: {X_train.shape[1]:,}")
        
        # Scale features (same preprocessing as neural network)
        print(f"⚖️  Scaling features...")
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Convert back to DataFrames for easier handling
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=self.feature_names, index=X_train.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=self.feature_names, index=X_test.index)
        
        return X_train_scaled, X_test_scaled, y_train, y_test, self.feature_names
    
    def get_neural_network_baseline(self) -> Dict:
        """
        Return the neural network performance for comparison
        """
        return {
            'model_name': 'Neural Network (Your Model)',
            'f1_score': 0.8902,
            'auc_score': 0.7713,
            'accuracy': 0.8915,  # From your results
            'precision': 0.8888,  # From classification report
            'recall': 0.8915,
            'default_rate': 7.6,
            'notes': 'F1=0.8902, Optimal threshold=0.7310'
        }

# =============================================================================
# 2. TRADITIONAL ML MODELS TRAINER
# =============================================================================

class TraditionalMLTrainer:
    """
    Train and evaluate various traditional ML models
    """
    
    def __init__(self, X_train, X_test, y_train, y_test, feature_names):
        self.X_train = X_train
        self.X_test = X_test
        self.y_train = y_train
        self.y_test = y_test
        self.feature_names = feature_names
        self.models = {}
        self.results = {}
        
        print("🤖 TRADITIONAL ML TRAINER INITIALIZED")
        print("=" * 40)
        print(f"📊 Training samples: {len(X_train):,}")
        print(f"📊 Test samples: {len(X_test):,}")
        print(f"📊 Features: {len(feature_names):,}")
    
    def get_model_configs(self) -> Dict:
        """
        Define model configurations for comparison
        """
        return {
            'random_forest': {
                'model': RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                    n_jobs=-1,
                    class_weight='balanced'
                ),
                'name': 'Random Forest',
                'description': 'Ensemble of decision trees'
            },
            
            'xgboost': {
                'model': xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42,
                    eval_metric='logloss',
                    scale_pos_weight=len(self.y_train[self.y_train == 0]) / len(self.y_train[self.y_train == 1])
                ),
                'name': 'XGBoost',
                'description': 'Gradient boosting framework'
            },
            
            'lightgbm': {
                'model': lgb.LGBMClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42,
                    class_weight='balanced',
                    verbose=-1
                ),
                'name': 'LightGBM',
                'description': 'Light gradient boosting'
            },
            
            'catboost': {
                'model': cb.CatBoostClassifier(
                    iterations=100,
                    depth=6,
                    learning_rate=0.1,
                    random_seed=42,
                    verbose=False,
                    auto_class_weights='Balanced'
                ),
                'name': 'CatBoost',
                'description': 'Gradient boosting with categorical features'
            },
            
            'gradient_boosting': {
                'model': GradientBoostingClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    random_state=42
                ),
                'name': 'Gradient Boosting',
                'description': 'Sklearn gradient boosting'
            },
            
            'extra_trees': {
                'model': ExtraTreesClassifier(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=5,
                    random_state=42,
                    n_jobs=-1,
                    class_weight='balanced'
                ),
                'name': 'Extra Trees',
                'description': 'Extremely randomized trees'
            },
            
            'logistic_regression': {
                'model': LogisticRegression(
                    random_state=42,
                    max_iter=1000,
                    class_weight='balanced'
                ),
                'name': 'Logistic Regression',
                'description': 'Linear classification model'
            }
        }
    
    def train_all_models(self) -> Dict:
        """
        Train all models and evaluate performance
        """
        
        print("\n🚀 TRAINING ALL MODELS")
        print("=" * 25)
        
        model_configs = self.get_model_configs()
        
        for model_key, config in model_configs.items():
            print(f"\n🤖 Training {config['name']}...")
            
            start_time = time.time()
            
            try:
                # Train the model
                model = config['model']
                model.fit(self.X_train, self.y_train)
                
                # Make predictions
                y_pred = model.predict(self.X_test)
                y_pred_proba = model.predict_proba(self.X_test)[:, 1]
                
                # Calculate metrics
                metrics = self._calculate_metrics(self.y_test, y_pred, y_pred_proba)
                
                training_time = time.time() - start_time
                
                # Store results
                self.models[model_key] = model
                self.results[model_key] = {
                    'model_name': config['name'],
                    'description': config['description'],
                    'training_time': training_time,
                    **metrics
                }
                
                print(f"   ✅ F1: {metrics['f1_score']:.4f}, AUC: {metrics['auc_score']:.4f}, Time: {training_time:.1f}s")
                
            except Exception as e:
                print(f"   ❌ Failed to train {config['name']}: {e}")
                continue
        
        print(f"\n🎉 Training completed! {len(self.results)} models trained successfully.")
        return self.results
    
    def _calculate_metrics(self, y_true, y_pred, y_pred_proba) -> Dict:
        """
        Calculate comprehensive metrics for model evaluation
        """
        
        # Basic metrics
        f1 = f1_score(y_true, y_pred, average='weighted')
        auc = roc_auc_score(y_true, y_pred_proba)
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        
        # Class distribution
        pred_dist = np.bincount(y_pred, minlength=2) / len(y_pred) * 100
        default_rate = pred_dist[1]
        
        # Confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        
        return {
            'f1_score': f1,
            'auc_score': auc,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'default_rate': default_rate,
            'confusion_matrix': cm.tolist()
        }
    
    def optimize_best_model(self, model_key: str = 'xgboost', n_trials: int = 50) -> Dict:
        """
        Hyperparameter optimization for the best performing model
        """
        
        print(f"\n🔧 OPTIMIZING {model_key.upper()}")
        print("=" * 30)
        
        def objective(trial):
            if model_key == 'xgboost':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'random_state': 42,
                    'eval_metric': 'logloss',
                    'scale_pos_weight': len(self.y_train[self.y_train == 0]) / len(self.y_train[self.y_train == 1])
                }
                model = xgb.XGBClassifier(**params)
                
            elif model_key == 'random_forest':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 200),
                    'max_depth': trial.suggest_int('max_depth', 5, 15),
                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
                    'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
                    'random_state': 42,
                    'n_jobs': -1,
                    'class_weight': 'balanced'
                }
                model = RandomForestClassifier(**params)
            
            # Cross-validation
            cv_scores = cross_val_score(
                model, self.X_train, self.y_train, 
                cv=3, scoring='f1_weighted', n_jobs=-1
            )
            
            return cv_scores.mean()
        
        # Run optimization
        study = optuna.create_study(direction='maximize', study_name=f'{model_key}_optimization')
        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
        
        # Train best model
        best_params = study.best_params
        print(f"🏆 Best parameters: {best_params}")
        
        if model_key == 'xgboost':
            best_model = xgb.XGBClassifier(**best_params)
        elif model_key == 'random_forest':
            best_model = RandomForestClassifier(**best_params)
        
        # Train and evaluate best model
        start_time = time.time()
        best_model.fit(self.X_train, self.y_train)
        
        y_pred = best_model.predict(self.X_test)
        y_pred_proba = best_model.predict_proba(self.X_test)[:, 1]
        
        metrics = self._calculate_metrics(self.y_test, y_pred, y_pred_proba)
        training_time = time.time() - start_time
        
        optimized_results = {
            'model_name': f'{model_key.title()} (Optimized)',
            'description': f'Hyperparameter optimized {model_key}',
            'training_time': training_time,
            'best_params': best_params,
            'optimization_score': study.best_value,
            **metrics
        }
        
        # Store optimized model
        self.models[f'{model_key}_optimized'] = best_model
        self.results[f'{model_key}_optimized'] = optimized_results
        
        print(f"✅ Optimized F1: {metrics['f1_score']:.4f} (improvement: +{metrics['f1_score'] - self.results.get(model_key, {}).get('f1_score', 0):.4f})")
        
        return optimized_results

# =============================================================================
# 3. MODEL COMPARISON AND ANALYSIS
# =============================================================================

class ModelComparator:
    """
    Compare all models including neural network baseline
    """
    
    def __init__(self, traditional_results: Dict, neural_network_baseline: Dict):
        self.traditional_results = traditional_results
        self.neural_network_baseline = neural_network_baseline
        self.all_results = {**traditional_results, 'neural_network': neural_network_baseline}
        
        print("📊 MODEL COMPARATOR INITIALIZED")
        print("=" * 32)
        print(f"🤖 Traditional models: {len(traditional_results)}")
        print(f"🧠 Neural network: 1")
    
    def create_comparison_table(self) -> pd.DataFrame:
        """
        Create comprehensive comparison table
        """
        
        print("\n📋 CREATING COMPARISON TABLE")
        print("=" * 28)
        
        comparison_data = []
        
        for model_key, results in self.all_results.items():
            comparison_data.append({
                'Model': results['model_name'],
                'F1_Score': results['f1_score'],
                'AUC_Score': results['auc_score'],
                'Accuracy': results['accuracy'],
                'Precision': results['precision'],
                'Recall': results['recall'],
                'Default_Rate_%': results['default_rate'],
                'Training_Time_s': results.get('training_time', 'N/A'),
                'Description': results.get('description', '')
            })
        
        df = pd.DataFrame(comparison_data)
        df = df.sort_values('F1_Score', ascending=False).reset_index(drop=True)
        df['Rank'] = range(1, len(df) + 1)
        
        return df
    
    def plot_model_comparison(self, comparison_df: pd.DataFrame):
        """
        Create comprehensive visualization comparing all models
        """
        
        print("\n📈 CREATING COMPARISON VISUALIZATIONS")
        print("=" * 35)
        
        # Set up the plotting style
        plt.style.use('default')
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('Home Credit Model Comparison: Traditional ML vs Neural Network', 
                     fontsize=16, fontweight='bold')
        
        # 1. F1-Score Comparison
        ax1 = axes[0, 0]
        bars1 = ax1.barh(comparison_df['Model'], comparison_df['F1_Score'])
        ax1.set_xlabel('F1-Score')
        ax1.set_title('F1-Score Comparison')
        ax1.axvline(x=0.88, color='red', linestyle='--', alpha=0.7, label='Target (0.88)')
        
        # Color the neural network bar differently
        for i, bar in enumerate(bars1):
            if 'Neural Network' in comparison_df.iloc[i]['Model']:
                bar.set_color('orange')
                bar.set_alpha(0.8)
        
        ax1.legend()
        ax1.grid(axis='x', alpha=0.3)
        
        # 2. AUC Score Comparison
        ax2 = axes[0, 1]
        bars2 = ax2.barh(comparison_df['Model'], comparison_df['AUC_Score'])
        ax2.set_xlabel('AUC Score')
        ax2.set_title('AUC Score Comparison')
        
        for i, bar in enumerate(bars2):
            if 'Neural Network' in comparison_df.iloc[i]['Model']:
                bar.set_color('orange')
                bar.set_alpha(0.8)
        
        ax2.grid(axis='x', alpha=0.3)
        
        # 3. Default Rate vs Target
        ax3 = axes[0, 2]
        bars3 = ax3.barh(comparison_df['Model'], comparison_df['Default_Rate_%'])
        ax3.set_xlabel('Default Rate (%)')
        ax3.set_title('Default Rate vs Target (8.1%)')
        ax3.axvline(x=8.1, color='red', linestyle='--', alpha=0.7, label='Target (8.1%)')
        
        for i, bar in enumerate(bars3):
            if 'Neural Network' in comparison_df.iloc[i]['Model']:
                bar.set_color('orange')
                bar.set_alpha(0.8)
        
        ax3.legend()
        ax3.grid(axis='x', alpha=0.3)
        
        # 4. F1 vs AUC Scatter Plot
        ax4 = axes[1, 0]
        scatter = ax4.scatter(comparison_df['AUC_Score'], comparison_df['F1_Score'], 
                             s=100, alpha=0.7)
        
        # Highlight neural network
        nn_row = comparison_df[comparison_df['Model'].str.contains('Neural Network')]
        if not nn_row.empty:
            ax4.scatter(nn_row['AUC_Score'], nn_row['F1_Score'], 
                       s=150, color='orange', label='Neural Network', edgecolor='black')
        
        ax4.set_xlabel('AUC Score')
        ax4.set_ylabel('F1-Score')
        ax4.set_title('F1-Score vs AUC Score')
        ax4.axhline(y=0.88, color='red', linestyle='--', alpha=0.7)
        ax4.grid(alpha=0.3)
        
        # Add model labels
        for i, row in comparison_df.iterrows():
            ax4.annotate(row['Model'].split()[0], 
                        (row['AUC_Score'], row['F1_Score']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax4.legend()
        
        # 5. Training Time Comparison (excluding N/A)
        ax5 = axes[1, 1]
        time_data = comparison_df[comparison_df['Training_Time_s'] != 'N/A'].copy()
        if not time_data.empty:
            time_data['Training_Time_s'] = pd.to_numeric(time_data['Training_Time_s'])
            bars5 = ax5.barh(time_data['Model'], time_data['Training_Time_s'])
            ax5.set_xlabel('Training Time (seconds)')
            ax5.set_title('Training Time Comparison')
            ax5.grid(axis='x', alpha=0.3)
        else:
            ax5.text(0.5, 0.5, 'No training time data available', 
                    ha='center', va='center', transform=ax5.transAxes)
        
        # 6. Performance Summary Radar Chart
        ax6 = axes[1, 2]
        
        # Select top 3 models for radar chart
        top_models = comparison_df.head(3)
        
        metrics = ['F1_Score', 'AUC_Score', 'Accuracy', 'Precision', 'Recall']
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle
        
        for i, (_, row) in enumerate(top_models.iterrows()):
            values = [row[metric] for metric in metrics]
            values += values[:1]  # Complete the circle
            
            ax6.plot(angles, values, 'o-', linewidth=2, label=row['Model'])
            ax6.fill(angles, values, alpha=0.1)
        
        ax6.set_xticks(angles[:-1])
        ax6.set_xticklabels(metrics)
        ax6.set_ylim(0, 1)
        ax6.set_title('Top 3 Models - Performance Radar')
        ax6.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
        ax6.grid(True)
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def generate_summary_report(self, comparison_df: pd.DataFrame) -> str:
        """
        Generate comprehensive summary report
        """
        
        print("\n📄 GENERATING SUMMARY REPORT")
        print("=" * 27)
        
        best_model = comparison_df.iloc[0]
        neural_network = comparison_df[comparison_df['Model'].str.contains('Neural Network')].iloc[0]
        
        report = f"""
🏆 HOME CREDIT MODEL COMPARISON REPORT
=====================================

📊 OVERALL RESULTS:
   Total models tested: {len(comparison_df)}
   Best performing model: {best_model['Model']}
   Neural Network rank: #{neural_network.name + 1}

🥇 BEST MODEL PERFORMANCE:
   Model: {best_model['Model']}
   F1-Score: {best_model['F1_Score']:.4f}
   AUC Score: {best_model['AUC_Score']:.4f}
   Default Rate: {best_model['Default_Rate_%']:.1f}%
   
🧠 NEURAL NETWORK PERFORMANCE:
   F1-Score: {neural_network['F1_Score']:.4f}
   AUC Score: {neural_network['AUC_Score']:.4f}  
   Default Rate: {neural_network['Default_Rate_%']:.1f}%

📈 PERFORMANCE COMPARISON:
   F1-Score difference: {best_model['F1_Score'] - neural_network['F1_Score']:+.4f}
   AUC difference: {best_model['AUC_Score'] - neural_network['AUC_Score']:+.4f}
   Default rate difference: {best_model['Default_Rate_%'] - neural_network['Default_Rate_%']:+.1f}%

🎯 TARGET ACHIEVEMENT:
   F1 > 0.88: {len(comparison_df[comparison_df['F1_Score'] >= 0.88])} models
   Default rate ~8.1%: {len(comparison_df[abs(comparison_df['Default_Rate_%'] - 8.1) <= 1.0])} models

💡 KEY INSIGHTS:
   • Best traditional ML: {comparison_df[~comparison_df['Model'].str.contains('Neural Network')].iloc[0]['Model']}
   • Neural Network vs Best Traditional: {'Neural Network wins' if neural_network['F1_Score'] > comparison_df[~comparison_df['Model'].str.contains('Neural Network')].iloc[0]['F1_Score'] else 'Traditional ML wins'}
   • Most balanced model: {comparison_df.loc[abs(comparison_df['Default_Rate_%'] - 8.1).idxmin(), 'Model']}

🚀 RECOMMENDATIONS:
   1. {'Use Neural Network' if neural_network['F1_Score'] >= best_model['F1_Score'] - 0.01 else f'Consider {best_model["Model"]}'} for best F1-score
   2. Use ensemble of top 3 models for maximum robustness
   3. {'Neural Network achieves' if abs(neural_network['Default_Rate_%'] - 8.1) <= 1.0 else 'Optimize threshold to achieve'} target default rate
   4. Monitor model performance over time for drift detection
"""
        
        return report

# =============================================================================
# 4. FEATURE IMPORTANCE ANALYSIS
# =============================================================================

def analyze_feature_importance(models: Dict, feature_names: List[str], top_n: int = 20):
    """
    Analyze and compare feature importance across models
    """
    
    print(f"\n🎯 FEATURE IMPORTANCE ANALYSIS (Top {top_n})")
    print("=" * 35)
    
    importance_data = {}
    
    # Extract feature importance from tree-based models
    for model_key, model in models.items():
        if hasattr(model, 'feature_importances_'):
            importance_data[model_key] = model.feature_importances_
        elif hasattr(model, 'coef_'):  # For logistic regression
            importance_data[model_key] = np.abs(model.coef_[0])
    
    if not importance_data:
        print("❌ No models with feature importance found")
        return
    
    # Create feature importance DataFrame
    importance_df = pd.DataFrame(importance_data, index=feature_names)
    
    # Calculate average importance
    importance_df['average'] = importance_df.mean(axis=1)
    importance_df = importance_df.sort_values('average', ascending=False)
    
    # Display top features
    print(f"🏆 TOP {top_n} MOST IMPORTANT FEATURES:")
    for i, (feature, row) in enumerate(importance_df.head(top_n).iterrows()):
        print(f"   {i+1:2d}. {feature:<30} | Avg: {row['average']:.4f}")
    
    # Plot feature importance comparison
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # Top features across all models
    top_features = importance_df.head(top_n)
    
    # Plot 1: Average importance
    ax1.barh(range(len(top_features)), top_features['average'])
    ax1.set_yticks(range(len(top_features)))
    ax1.set_yticklabels(top_features.index, fontsize=10)
    ax1.set_xlabel('Average Feature Importance')
    ax1.set_title(f'Top {top_n} Features - Average Importance')
    ax1.grid(axis='x', alpha=0.3)
    
    # Plot 2: Heatmap of importance across models
    model_cols = [col for col in top_features.columns if col != 'average']
    if model_cols:
        im = ax2.imshow(top_features[model_cols].values, aspect='auto', cmap='viridis')
        ax2.set_xticks(range(len(model_cols)))
        ax2.set_xticklabels(model_cols, rotation=45)
        ax2.set_yticks(range(len(top_features)))
        ax2.set_yticklabels(top_features.index, fontsize=10)
        ax2.set_title('Feature Importance Heatmap Across Models')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax2)
        cbar.set_label('Importance Score')
    
    plt.tight_layout()
    plt.show()
    
    return importance_df

# =============================================================================
# 5. MAIN EXECUTION PIPELINE
# =============================================================================

def run_complete_model_comparison(data_path: str = "fe_data/application_train_engineered.csv") -> Dict:
    """
    Complete pipeline for comparing traditional ML with neural network
    
    Args:
        data_path: Path to your engineered CSV data
        
    Returns:
        Dictionary with all results and models
    """
    
    print("🚀 HOME CREDIT: COMPLETE MODEL COMPARISON")
    print("=" * 45)
    print("🎯 Comparing Traditional ML vs Neural Network")
    print(f"📁 Data source: {data_path}")
    print()
    
    results = {}
    
    try:
        # 1. Data Preparation
        print("1️⃣ DATA PREPARATION")
        preparator = HomeCreditDataPreparator(data_path)
        data = preparator.load_and_prepare_data()
        
        if data is None:
            print("❌ Data preparation failed")
            return None
        
        X_train, X_test, y_train, y_test, feature_names = data
        neural_network_baseline = preparator.get_neural_network_baseline()
        
        # 2. Train Traditional ML Models
        print("\n2️⃣ TRADITIONAL ML TRAINING")
        trainer = TraditionalMLTrainer(X_train, X_test, y_train, y_test, feature_names)
        traditional_results = trainer.train_all_models()
        
        # 3. Optimize Best Model
        print("\n3️⃣ HYPERPARAMETER OPTIMIZATION")
        # Find best traditional model
        best_traditional_key = max(traditional_results.keys(), 
                                 key=lambda k: traditional_results[k]['f1_score'])
        
        print(f"🏆 Best traditional model: {traditional_results[best_traditional_key]['model_name']}")
        
        # Optimize XGBoost (typically performs well)
        if 'xgboost' in traditional_results:
            trainer.optimize_best_model('xgboost', n_trials=30)
        
        # 4. Model Comparison
        print("\n4️⃣ MODEL COMPARISON & ANALYSIS")
        comparator = ModelComparator(trainer.results, neural_network_baseline)
        comparison_df = comparator.create_comparison_table()
        
        print("\n📊 COMPARISON TABLE:")
        print(comparison_df.to_string(index=False))
        
        # 5. Visualizations
        print("\n5️⃣ CREATING VISUALIZATIONS")
        comparison_fig = comparator.plot_model_comparison(comparison_df)
        
        # 6. Feature Importance Analysis
        print("\n6️⃣ FEATURE IMPORTANCE ANALYSIS")
        importance_df = analyze_feature_importance(trainer.models, feature_names, top_n=20)
        
        # 7. Summary Report
        print("\n7️⃣ GENERATING SUMMARY REPORT")
        summary_report = comparator.generate_summary_report(comparison_df)
        print(summary_report)
        
        # Compile results
        results = {
            'comparison_table': comparison_df,
            'traditional_results': trainer.results,
            'neural_network_baseline': neural_network_baseline,
            'trained_models': trainer.models,
            'feature_importance': importance_df,
            'summary_report': summary_report,
            'data_info': {
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'features': len(feature_names),
                'target_distribution': {
                    'no_default': (y_train == 0).sum(),
                    'default': (y_train == 1).sum()
                }
            }
        }
        
        print(f"\n🎉 ANALYSIS COMPLETE!")
        print(f"📊 {len(trainer.results)} traditional models + 1 neural network compared")
        
        return results
        
    except Exception as e:
        print(f"❌ Error in model comparison: {e}")
        import traceback
        traceback.print_exc()
        return None

# =============================================================================
# 6. QUICK START FUNCTIONS
# =============================================================================

def quick_xgboost_vs_neural_network(data_path: str = "fe_data/application_train_engineered.csv"):
    """
    Quick comparison between XGBoost and Neural Network only
    """
    
    print("⚡ QUICK COMPARISON: XGBoost vs Neural Network")
    print("=" * 47)
    
    # Load data
    preparator = HomeCreditDataPreparator(data_path)
    data = preparator.load_and_prepare_data()
    
    if data is None:
        return None
    
    X_train, X_test, y_train, y_test, feature_names = data
    
    # Train XGBoost
    print("\n🚀 Training XGBoost...")
    xgb_model = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        scale_pos_weight=len(y_train[y_train == 0]) / len(y_train[y_train == 1])
    )
    
    xgb_model.fit(X_train, y_train)
    y_pred = xgb_model.predict(X_test)
    y_pred_proba = xgb_model.predict_proba(X_test)[:, 1]
    
    # Calculate metrics
    xgb_f1 = f1_score(y_test, y_pred, average='weighted')
    xgb_auc = roc_auc_score(y_test, y_pred_proba)
    xgb_default_rate = (y_pred == 1).mean() * 100
    
    # Neural network baseline
    nn_f1 = 0.8902
    nn_auc = 0.7713
    nn_default_rate = 7.6
    
    print(f"\n📊 QUICK RESULTS:")
    print(f"{'Metric':<20} {'XGBoost':<10} {'Neural Net':<12} {'Winner'}")
    print("-" * 50)
    print(f"{'F1-Score':<20} {xgb_f1:<10.4f} {nn_f1:<12.4f} {'XGBoost' if xgb_f1 > nn_f1 else 'Neural Net'}")
    print(f"{'AUC Score':<20} {xgb_auc:<10.4f} {nn_auc:<12.4f} {'XGBoost' if xgb_auc > nn_auc else 'Neural Net'}")
    print(f"{'Default Rate %':<20} {xgb_default_rate:<10.1f} {nn_default_rate:<12.1f} {'Closer to 8.1%'}")
    
    return {
        'xgboost': {'f1': xgb_f1, 'auc': xgb_auc, 'default_rate': xgb_default_rate},
        'neural_network': {'f1': nn_f1, 'auc': nn_auc, 'default_rate': nn_default_rate}
    }

# =============================================================================
# 7. USAGE INSTRUCTIONS
# =============================================================================

if __name__ == "__main__":
    print("🏠 HOME CREDIT: TRADITIONAL ML vs NEURAL NETWORK")
    print("=" * 50)
    print()
    print("📋 USAGE OPTIONS:")
    print()
    print("1️⃣ Complete Analysis:")
    print("   results = run_complete_model_comparison('fe_data/application_train_engineered.csv')")
    print()
    print("2️⃣ Quick XGBoost vs Neural Network:")
    print("   quick_results = quick_xgboost_vs_neural_network('fe_data/application_train_engineered.csv')")
    print()
    print("📁 Make sure your data path points to the engineered CSV file")
    print("🎯 Uses the same target variable and preprocessing as your neural network")
    print()
    print("🚀 Expected models to test:")
    print("   • XGBoost")
    print("   • Random Forest") 
    print("   • LightGBM")
    print("   • CatBoost")
    print("   • Gradient Boosting")
    print("   • Extra Trees")
    print("   • Logistic Regression")
    print("   • Your Neural Network (F1=0.8902)")