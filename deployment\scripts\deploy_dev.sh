#!/bin/bash

echo "🚀 Starting Home Credit Model - Development Deployment"

# Load environment variables
export $(cat .env.dev | xargs)

# Build and start services
docker-compose -f docker-compose.yml up --build -d

echo "✅ Development deployment completed!"
echo "📋 Services:"
echo "   API: http://localhost:8000"
echo "   Frontend: http://localhost:8501"
echo "   Docs: http://localhost:8000/docs"

# Show logs
echo "📊 Showing logs..."
docker-compose logs -f
