"""
SIMPLE DATA SETUP FOR TRADITIONAL ML MODELS
============================================
Lightweight script to convert your tensor data back to format 
suitable for XGBoost, Random Forest, etc.

Use this if you want a simple start or have package installation issues.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, roc_auc_score, classification_report
import xgboost as xgb
import matplotlib.pyplot as plt
import seaborn as sns

def load_and_prepare_data(data_path="fe_data/application_train_engineered.csv", 
                         target_col="TARGET", test_size=0.2, random_state=42):
    """
    Simple function to load and prepare your data for traditional ML
    
    Args:
        data_path: Path to your engineered CSV file
        target_col: Name of target column (adjust if different)
        test_size: Test split size
        random_state: Random seed
    
    Returns:
        X_train, X_test, y_train, y_test, feature_names, scaler
    """
    
    print("📊 Loading Home Credit Data for Traditional ML")
    print("=" * 47)
    
    # Load data
    try:
        df = pd.read_csv(data_path)
        print(f"✅ Data loaded: {df.shape[0]:,} rows, {df.shape[1]:,} columns")
    except FileNotFoundError:
        print(f"❌ File not found: {data_path}")
        print("Available files in fe_data/:")
        import os
        if os.path.exists("fe_data"):
            files = os.listdir("fe_data")
            for f in files:
                print(f"   - {f}")
        return None
    
    # Show first few columns to help identify target
    print(f"📋 First 10 columns: {list(df.columns[:10])}")
    
    # Try to find target column
    possible_targets = [target_col, 'TARGET', 'target', 'Target', 'default', 'DEFAULT']
    target_column = None
    
    for col in possible_targets:
        if col in df.columns:
            target_column = col
            break
    
    if target_column is None:
        print("❌ Target column not found. Available columns:")
        print(df.columns.tolist())
        print("\n🔧 Please specify the correct target column name")
        return None
    
    print(f"🎯 Using target column: {target_column}")
    
    # Separate features and target
    X = df.drop(columns=[target_column])
    y = df[target_column]
    
    print(f"📊 Target distribution:")
    print(f"   Class 0: {(y == 0).sum():,} ({(y == 0).mean():.1%})")
    print(f"   Class 1: {(y == 1).sum():,} ({(y == 1).mean():.1%})")
    
    # Handle missing values
    print(f"🔧 Handling missing values...")
    missing_before = X.isnull().sum().sum()
    if missing_before > 0:
        print(f"   Found {missing_before:,} missing values")
        
        # Fill numeric columns with median
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())
        
        # Fill categorical columns with mode
        cat_cols = X.select_dtypes(include=['object']).columns
        for col in cat_cols:
            X[col] = X[col].fillna(X[col].mode()[0] if len(X[col].mode()) > 0 else 'Unknown')
        
        missing_after = X.isnull().sum().sum()
        print(f"   Missing values after filling: {missing_after}")
    
    # Handle categorical variables
    categorical_cols = X.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        print(f"🔤 Encoding {len(categorical_cols)} categorical columns...")
        label_encoders = {}
        
        for col in categorical_cols:
            le = LabelEncoder()
            X[col] = le.fit_transform(X[col].astype(str))
            label_encoders[col] = le
    
    # Get feature names
    feature_names = X.columns.tolist()
    print(f"📊 Final dataset: {len(feature_names)} features")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    print(f"📊 Data split:")
    print(f"   Training: {X_train.shape[0]:,} samples")
    print(f"   Test: {X_test.shape[0]:,} samples")
    
    # Scale features (important for some models)
    print(f"⚖️  Scaling features...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Convert back to DataFrames for easier use
    X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_names, index=X_train.index)
    X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_names, index=X_test.index)
    
    print("✅ Data preparation complete!")
    
    return X_train_scaled, X_test_scaled, y_train, y_test, feature_names, scaler

def quick_model_comparison(X_train, X_test, y_train, y_test):
    """
    Quick comparison of XGBoost vs Random Forest vs Your Neural Network
    """
    
    print("\n🚀 QUICK MODEL COMPARISON")
    print("=" * 25)
    
    results = {}
    
    # 1. XGBoost
    print("🟢 Training XGBoost...")
    xgb_model = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        eval_metric='logloss',
        scale_pos_weight=len(y_train[y_train == 0]) / len(y_train[y_train == 1])
    )
    
    xgb_model.fit(X_train, y_train)
    xgb_pred = xgb_model.predict(X_test)
    xgb_pred_proba = xgb_model.predict_proba(X_test)[:, 1]
    
    xgb_f1 = f1_score(y_test, xgb_pred, average='weighted')
    xgb_auc = roc_auc_score(y_test, xgb_pred_proba)
    xgb_default_rate = (xgb_pred == 1).mean() * 100
    
    results['XGBoost'] = {
        'f1': xgb_f1,
        'auc': xgb_auc,
        'default_rate': xgb_default_rate,
        'model': xgb_model
    }
    
    print(f"   F1: {xgb_f1:.4f}, AUC: {xgb_auc:.4f}, Default Rate: {xgb_default_rate:.1f}%")
    
    # 2. Random Forest
    print("🌳 Training Random Forest...")
    rf_model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        random_state=42,
        n_jobs=-1,
        class_weight='balanced'
    )
    
    rf_model.fit(X_train, y_train)
    rf_pred = rf_model.predict(X_test)
    rf_pred_proba = rf_model.predict_proba(X_test)[:, 1]
    
    rf_f1 = f1_score(y_test, rf_pred, average='weighted')
    rf_auc = roc_auc_score(y_test, rf_pred_proba)
    rf_default_rate = (rf_pred == 1).mean() * 100
    
    results['Random Forest'] = {
        'f1': rf_f1,
        'auc': rf_auc,
        'default_rate': rf_default_rate,
        'model': rf_model
    }
    
    print(f"   F1: {rf_f1:.4f}, AUC: {rf_auc:.4f}, Default Rate: {rf_default_rate:.1f}%")
    
    # 3. Your Neural Network (baseline)
    results['Neural Network'] = {
        'f1': 0.8902,
        'auc': 0.7713,
        'default_rate': 7.6,
        'model': 'Your trained model'
    }
    
    print("🧠 Neural Network (Your Model):")
    print(f"   F1: 0.8902, AUC: 0.7713, Default Rate: 7.6%")
    
    return results

def plot_comparison_results(results):
    """
    Simple visualization of results
    """
    
    print("\n📊 Creating comparison visualization...")
    
    # Prepare data for plotting
    models = list(results.keys())
    f1_scores = [results[model]['f1'] for model in models]
    auc_scores = [results[model]['auc'] for model in models]
    default_rates = [results[model]['default_rate'] for model in models]
    
    # Create subplots
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
    
    # F1-Score comparison
    bars1 = ax1.bar(models, f1_scores, color=['lightblue', 'lightgreen', 'orange'])
    ax1.set_ylabel('F1-Score')
    ax1.set_title('F1-Score Comparison')
    ax1.axhline(y=0.88, color='red', linestyle='--', alpha=0.7, label='Target (0.88)')
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars1, f1_scores):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{value:.4f}', ha='center', va='bottom')
    
    # AUC Score comparison
    bars2 = ax2.bar(models, auc_scores, color=['lightblue', 'lightgreen', 'orange'])
    ax2.set_ylabel('AUC Score')
    ax2.set_title('AUC Score Comparison')
    ax2.grid(axis='y', alpha=0.3)
    
    for bar, value in zip(bars2, auc_scores):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{value:.4f}', ha='center', va='bottom')
    
    # Default Rate comparison
    bars3 = ax3.bar(models, default_rates, color=['lightblue', 'lightgreen', 'orange'])
    ax3.set_ylabel('Default Rate (%)')
    ax3.set_title('Default Rate vs Target')
    ax3.axhline(y=8.1, color='red', linestyle='--', alpha=0.7, label='Target (8.1%)')
    ax3.legend()
    ax3.grid(axis='y', alpha=0.3)
    
    for bar, value in zip(bars3, default_rates):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                f'{value:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
    return fig

def show_feature_importance(model, feature_names, top_n=20):
    """
    Show feature importance for tree-based models
    """
    
    if not hasattr(model, 'feature_importances_'):
        print(f"❌ Model doesn't have feature importance")
        return
    
    print(f"\n🎯 TOP {top_n} FEATURE IMPORTANCE")
    print("=" * 30)
    
    # Get feature importance
    importance = model.feature_importances_
    feature_importance = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False)
    
    # Show top features
    top_features = feature_importance.head(top_n)
    
    print("🏆 Most Important Features:")
    for i, (_, row) in enumerate(top_features.iterrows()):
        print(f"   {i+1:2d}. {row['feature']:<30} | {row['importance']:.4f}")
    
    # Plot
    plt.figure(figsize=(12, 8))
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Feature Importance')
    plt.title(f'Top {top_n} Most Important Features')
    plt.gca().invert_yaxis()
    plt.grid(axis='x', alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    return feature_importance

def generate_simple_report(results):
    """
    Generate a simple comparison report
    """
    
    print(f"\n📄 SIMPLE COMPARISON REPORT")
    print("=" * 27)
    
    # Find best model
    best_f1_model = max(results.keys(), key=lambda k: results[k]['f1'])
    best_auc_model = max(results.keys(), key=lambda k: results[k]['auc'])
    
    # Find closest to target default rate (8.1%)
    closest_rate_model = min(results.keys(), key=lambda k: abs(results[k]['default_rate'] - 8.1))
    
    print(f"🏆 RESULTS SUMMARY:")
    print(f"   Best F1-Score: {best_f1_model} ({results[best_f1_model]['f1']:.4f})")
    print(f"   Best AUC Score: {best_auc_model} ({results[best_auc_model]['auc']:.4f})")
    print(f"   Closest to 8.1% default rate: {closest_rate_model} ({results[closest_rate_model]['default_rate']:.1f}%)")
    
    print(f"\n📊 DETAILED COMPARISON:")
    print(f"{'Model':<15} {'F1-Score':<10} {'AUC':<8} {'Default%':<10} {'vs Neural Net'}")
    print("-" * 55)
    
    nn_f1 = results['Neural Network']['f1']
    
    for model, metrics in results.items():
        vs_nn = f"{metrics['f1'] - nn_f1:+.4f}" if model != 'Neural Network' else "baseline"
        print(f"{model:<15} {metrics['f1']:<10.4f} {metrics['auc']:<8.4f} {metrics['default_rate']:<10.1f} {vs_nn}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if results[best_f1_model]['f1'] > nn_f1:
        print(f"   • {best_f1_model} outperforms Neural Network by {results[best_f1_model]['f1'] - nn_f1:.4f} F1-score")
    else:
        print(f"   • Neural Network performs best with F1={nn_f1:.4f}")
    
    print(f"   • For 8.1% default rate target: {closest_rate_model} is closest")
    print(f"   • Consider ensemble of top performers for robustness")

# =============================================================================
# MAIN EXECUTION FUNCTION
# =============================================================================

def run_simple_comparison(data_path="fe_data/application_train_engineered.csv"):
    """
    Main function to run simple comparison
    
    Usage:
        results = run_simple_comparison("fe_data/application_train_engineered.csv")
    """
    
    print("🏠 HOME CREDIT: SIMPLE ML COMPARISON")
    print("=" * 37)
    print("🎯 XGBoost vs Random Forest vs Neural Network")
    print(f"📁 Data: {data_path}")
    print()
    
    # 1. Load and prepare data
    data = load_and_prepare_data(data_path)
    
    if data is None:
        print("❌ Data loading failed")
        return None
    
    X_train, X_test, y_train, y_test, feature_names, scaler = data
    
    # 2. Train and compare models
    results = quick_model_comparison(X_train, X_test, y_train, y_test)
    
    # 3. Visualize results
    plot_comparison_results(results)
    
    # 4. Show feature importance for XGBoost
    if 'XGBoost' in results and 'model' in results['XGBoost']:
        show_feature_importance(results['XGBoost']['model'], feature_names, top_n=15)
    
    # 5. Generate report
    generate_simple_report(results)
    
    print(f"\n🎉 SIMPLE COMPARISON COMPLETE!")
    print(f"📊 Compared 3 models on {len(y_test):,} test samples")
    
    return {
        'results': results,
        'data': (X_train, X_test, y_train, y_test),
        'feature_names': feature_names,
        'scaler': scaler
    }

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

if __name__ == "__main__":
    print("🎯 HOME CREDIT: SIMPLE TRADITIONAL ML SETUP")
    print("=" * 42)
    print()
    print("📋 USAGE:")
    print()
    print("1️⃣ Run simple comparison:")
    print("   results = run_simple_comparison('fe_data/application_train_engineered.csv')")
    print()
    print("2️⃣ Just load data:")
    print("   data = load_and_prepare_data('fe_data/application_train_engineered.csv')")
    print("   X_train, X_test, y_train, y_test, features, scaler = data")
    print()
    print("3️⃣ Train your own model:")
    print("   from sklearn.ensemble import RandomForestClassifier")
    print("   model = RandomForestClassifier()")
    print("   model.fit(X_train, y_train)")
    print()
    print("🎯 This will compare:")
    print("   • XGBoost")
    print("   • Random Forest")
    print("   • Your Neural Network (F1=0.8902)")
    print()
    print("📊 Expected: XGBoost and Random Forest should get F1 > 0.85")
    print("🧠 Your Neural Network is the benchmark to beat!")
