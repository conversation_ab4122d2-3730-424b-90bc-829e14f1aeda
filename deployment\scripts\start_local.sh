#!/bin/bash

echo "🚀 Starting Home Credit Model - Local Development (dl_ml environment)"

# Check if we're in the correct conda environment
if [[ "$CONDA_DEFAULT_ENV" != "dl_ml" ]]; then
    echo "⚠️  Please activate the dl_ml environment first:"
    echo "   conda activate dl_ml"
    exit 1
fi

echo "✅ Running in dl_ml environment"

# Configure models
echo "🔧 Configuring model paths..."
chmod +x scripts/configure_models.sh
./scripts/configure_models.sh

# Install dependencies if needed
echo "📦 Checking dependencies..."

# Check if FastAPI dependencies are installed
if ! python -c "import fastapi" 2>/dev/null; then
    echo "📦 Installing API dependencies..."
    pip install -r api/requirements.txt
fi

# Check if Streamlit dependencies are installed
if ! python -c "import streamlit" 2>/dev/null; then
    echo "📦 Installing frontend dependencies..."
    pip install -r frontend/requirements.txt
fi

echo "✅ Dependencies ready"

# Start services locally
echo "🚀 Starting services..."

# Start API in background
echo "🔧 Starting FastAPI backend..."
cd api
python main.py &
API_PID=$!
cd ..

# Wait a moment for API to start
sleep 3

# Start Streamlit frontend
echo "🖥️  Starting Streamlit frontend..."
cd frontend
streamlit run app.py --server.port=8501 --server.address=0.0.0.0 &
FRONTEND_PID=$!
cd ..

echo ""
echo "✅ Services started successfully!"
echo ""
echo "📋 Access URLs:"
echo "   Frontend: http://localhost:8501"
echo "   API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "📊 Process IDs:"
echo "   API PID: $API_PID"
echo "   Frontend PID: $FRONTEND_PID"
echo ""
echo "⏹️  To stop services:"
echo "   kill $API_PID $FRONTEND_PID"
echo "   or use Ctrl+C"

# Wait for user interrupt
trap "echo '⏹️  Stopping services...'; kill $API_PID $FRONTEND_PID 2>/dev/null; exit 0" INT

echo "🔄 Services running... Press Ctrl+C to stop"
wait
