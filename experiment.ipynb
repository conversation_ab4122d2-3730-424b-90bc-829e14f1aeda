import tensorflow as tf
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from tensorflow.keras.callbacks import TensorBoard
from sklearn.preprocessing import StandardScaler,OneHotEncoder
import numpy as np
import datetime
import matplotlib.pyplot as plt
import os
import pandas as pd

iris = load_iris()
iris.data

iris.target

iris_df=pd.DataFrame(iris.data,columns=iris.feature_names)
iris_df

iris_target= pd.DataFrame(iris.target, columns=['target'])
iris_target

scaler=StandardScaler()
scaler.fit(iris_df)
X_scaled =scaler.transform(iris_df)
X_scaled


y = iris.data
y =    iris.target.reshape(150,1)
y

encoder = OneHotEncoder(sparse_output= False)
y_enncoded =encoder.fit_transform(y)
y_enncoded

X_train,X_test,y_train,y_test=train_test_split(X_scaled,y_enncoded,test_size=0.2,random_state=42)

input= tf.keras.Input(shape=(4,))
x = tf.keras.layers.Dense(10, activation='relu')(input)
x= tf.keras.layers.Dense(8, activation='relu')(x)
output = tf.keras.layers.Dense(3, activation= 'softmax')(x)
model = tf.keras.Model(inputs=input, outputs=output)


model.summary()

from tensorflow.keras.utils import plot_model

plot_model(
    model,
    to_file='model.png',        # File to save (optional)
    show_shapes=True,           # Show layer shapes
    show_dtype=True,            # Show data types (not show_datatypes!)
    show_layer_names=True,      # Show layer names
    #rankdir='LR',               # Direction: 'TB', 'BT', 'LR', 'RL'
    expand_nested=True,        # Expand nested models
    dpi=96,                     # Image resolution
    show_layer_activations=True, # Show activation functions
    show_trainable=True         # Show trainable parameters
)

#!pip install visualkeras
import visualkeras
img = visualkeras.layered_view(model, legend=True)
img.save('model1.png')

img = visualkeras.layered_view(model, legend=True)
img.save('model.png')

print(model)
model.layers
model.layers[1]

model.layers[1].trainable_weights

model.layers[2].trainable_weights


model.layers[3].trainable_weights

# Complete Neural Network Visualization Toolkit
# Install required packages first:
# pip install visualkeras netron matplotlib seaborn plotly

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tensorflow.keras.utils import plot_model
import warnings
warnings.filterwarnings('ignore')

class NeuralNetworkVisualizer:
    def __init__(self, model):
        self.model = model
        
    def architecture_overview(self):
        """Display basic model architecture"""
        print("=" * 60)
        print("MODEL ARCHITECTURE OVERVIEW")
        print("=" * 60)
        self.model.summary()
        print(f"\nTotal layers: {len(self.model.layers)}")
        print(f"Total parameters: {self.model.count_params():,}")
        
        # Layer breakdown
        for i, layer in enumerate(self.model.layers):
            weights_count = sum([w.numpy().size for w in layer.weights]) if layer.weights else 0
            print(f"Layer {i}: {layer.name} ({type(layer).__name__}) - {weights_count:,} parameters")
    
    def plot_architecture(self, filename='model_architecture.png'):
        """Create architectural diagram"""
        try:
            plot_model(
                self.model,
                to_file=filename,
                show_shapes=True,
                show_dtype=True,
                show_layer_names=True,
                show_layer_activations=True,
                show_trainable=True,
                rankdir='TB',
                dpi=300
            )
            print(f"✅ Architecture saved to {filename}")
        except Exception as e:
            print(f"❌ plot_model failed: {e}")
            print("Make sure graphviz is installed: pip install graphviz")
    
    def visualkeras_view(self, filename='model_layers.png'):
        """Create beautiful layered view with visualkeras"""
        try:
            import visualkeras
            
            # Layered view (with basic parameters only)
            visualkeras.layered_view(
                self.model, 
                legend=True,
                to_file=filename
            )
            print(f"✅ Layered view saved to {filename}")
            
            # Try alternative graph view if available
            try:
                graph_file = filename.replace('.png', '_graph.png')
                visualkeras.graph_view(
                    self.model,
                    to_file=graph_file
                )
                print(f"✅ Graph view saved to {graph_file}")
            except:
                print("Graph view not available in this visualkeras version")
            
        except ImportError:
            print("❌ visualkeras not installed. Run: pip install visualkeras")
        except Exception as e:
            print(f"❌ visualkeras failed: {e}")
            print("Trying basic visualkeras without advanced options...")
            try:
                import visualkeras
                visualkeras.layered_view(self.model, to_file=filename)
                print(f"✅ Basic layered view saved to {filename}")
            except:
                print("Basic visualkeras also failed")
    
    def visualize_all_weights(self):
        """Visualize weights for all layers"""
        layers_with_weights = [(i, layer) for i, layer in enumerate(self.model.layers) if layer.weights]
        
        if not layers_with_weights:
            print("No layers with weights found!")
            return
        
        n_layers = len(layers_with_weights)
        fig, axes = plt.subplots(n_layers, 2, figsize=(15, 4*n_layers))
        
        if n_layers == 1:
            axes = axes.reshape(1, -1)
        
        for row, (layer_idx, layer) in enumerate(layers_with_weights):
            self._visualize_layer_weights(layer, layer_idx, axes[row])
        
        plt.tight_layout()
        plt.suptitle('Neural Network Weights Visualization', y=1.02, fontsize=16)
        plt.show()
    
    def _visualize_layer_weights(self, layer, layer_idx, axes):
        """Helper to visualize individual layer weights"""
        weights = layer.weights
        layer_name = f"Layer {layer_idx}: {layer.name}"
        
        if len(weights) >= 1:  # Kernel/weights
            kernel = weights[0].numpy()
            
            if kernel.ndim == 2:  # Dense layer
                im1 = axes[0].imshow(kernel, cmap='RdBu', aspect='auto')
                axes[0].set_title(f'{layer_name}\nWeights Shape: {kernel.shape}')
                axes[0].set_xlabel('Output Units')
                axes[0].set_ylabel('Input Units')
                plt.colorbar(im1, ax=axes[0])
                
                # Weight distribution
                axes[1].hist(kernel.flatten(), bins=50, alpha=0.7, color='blue')
                axes[1].set_title(f'Weight Distribution\nMean: {kernel.mean():.4f}, Std: {kernel.std():.4f}')
                axes[1].set_xlabel('Weight Value')
                axes[1].set_ylabel('Frequency')
                
            elif kernel.ndim == 4:  # Convolutional layer
                # Show first few filters
                n_filters = min(8, kernel.shape[-1])
                kernel_slice = kernel[:, :, 0, :n_filters]  # First channel, first few filters
                
                im1 = axes[0].imshow(kernel_slice.reshape(kernel.shape[0], -1), cmap='RdBu', aspect='auto')
                axes[0].set_title(f'{layer_name}\nConv Weights Shape: {kernel.shape}')
                plt.colorbar(im1, ax=axes[0])
                
                axes[1].hist(kernel.flatten(), bins=50, alpha=0.7, color='green')
                axes[1].set_title(f'Conv Weight Distribution\nMean: {kernel.mean():.4f}, Std: {kernel.std():.4f}')
                
        else:
            axes[0].text(0.5, 0.5, f'{layer_name}\nNo weights', ha='center', va='center')
            axes[0].set_xlim(0, 1)
            axes[0].set_ylim(0, 1)
            axes[1].text(0.5, 0.5, 'No weights', ha='center', va='center')
            axes[1].set_xlim(0, 1)
            axes[1].set_ylim(0, 1)
    
    def weight_statistics(self):
        """Print detailed weight statistics"""
        print("\n" + "=" * 60)
        print("DETAILED WEIGHT STATISTICS")
        print("=" * 60)
        
        for i, layer in enumerate(self.model.layers):
            if not layer.weights:
                continue
                
            print(f"\n--- Layer {i}: {layer.name} ({type(layer).__name__}) ---")
            
            for j, weight in enumerate(layer.weights):
                w_vals = weight.numpy()
                weight_type = "Kernel" if j == 0 else "Bias" if j == 1 else f"Weight_{j}"
                
                print(f"\n{weight_type}:")
                print(f"  Shape: {w_vals.shape}")
                print(f"  Total params: {w_vals.size:,}")
                print(f"  Min: {w_vals.min():.6f}")
                print(f"  Max: {w_vals.max():.6f}")
                print(f"  Mean: {w_vals.mean():.6f}")
                print(f"  Std: {w_vals.std():.6f}")
                print(f"  Zeros: {np.sum(w_vals == 0)} ({100*np.sum(w_vals == 0)/w_vals.size:.2f}%)")
    
    def visualize_specific_layer(self, layer_index, detailed=True):
        """Detailed visualization of a specific layer"""
        if layer_index >= len(self.model.layers):
            print(f"Layer index {layer_index} out of bounds. Model has {len(self.model.layers)} layers")
            return
        
        layer = self.model.layers[layer_index]
        if not layer.weights:
            print(f"Layer {layer_index} has no trainable weights")
            return
        
        print(f"\n=== LAYER {layer_index}: {layer.name} ===")
        
        fig, axes = plt.subplots(2, len(layer.weights), figsize=(6*len(layer.weights), 10))
        if len(layer.weights) == 1:
            axes = axes.reshape(-1, 1)
        
        for i, weight in enumerate(layer.weights):
            w_vals = weight.numpy()
            weight_name = weight.name.split('/')[-1]
            
            # Heatmap
            if w_vals.ndim == 2:
                im = axes[0, i].imshow(w_vals, cmap='RdBu', aspect='auto')
                axes[0, i].set_title(f'{weight_name}\nShape: {w_vals.shape}')
                plt.colorbar(im, ax=axes[0, i])
            elif w_vals.ndim == 1:
                axes[0, i].bar(range(len(w_vals)), w_vals, color='skyblue')
                axes[0, i].set_title(f'{weight_name}\nShape: {w_vals.shape}')
            else:
                # For higher dimensional weights, show flattened
                w_flat = w_vals.flatten()
                axes[0, i].plot(w_flat[:min(1000, len(w_flat))])  # Show first 1000 values
                axes[0, i].set_title(f'{weight_name}\nShape: {w_vals.shape}')
            
            # Distribution
            axes[1, i].hist(w_vals.flatten(), bins=50, alpha=0.7, color='orange')
            axes[1, i].set_title(f'Distribution - Mean: {w_vals.mean():.4f}')
            axes[1, i].set_xlabel('Weight Value')
            axes[1, i].set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.show()
        
        if detailed:
            print("\nDetailed Statistics:")
            for i, weight in enumerate(layer.weights):
                w_vals = weight.numpy()
                print(f"\n{weight.name}:")
                print(f"  Shape: {w_vals.shape}")
                print(f"  Range: [{w_vals.min():.6f}, {w_vals.max():.6f}]")
                print(f"  Mean ± Std: {w_vals.mean():.6f} ± {w_vals.std():.6f}")
    
    def interactive_netron(self, filename='model_for_netron.h5'):
        """Save model for Netron interactive visualization"""
        try:
            self.model.save(filename)
            print(f"✅ Model saved as {filename}")
            print("To visualize interactively:")
            print("1. Install Netron: pip install netron")
            print(f"2. Run: netron {filename}")
            print("3. Or upload to: https://netron.app")
        except Exception as e:
            print(f"❌ Failed to save model: {e}")
    
    def create_3d_weight_plot(self, layer_index=1):
        """Create 3D visualization of weights (for Dense layers)"""
        if layer_index >= len(self.model.layers):
            print(f"Layer {layer_index} out of bounds")
            return
        
        layer = self.model.layers[layer_index]
        if not layer.weights or layer.weights[0].numpy().ndim != 2:
            print(f"Layer {layer_index} doesn't have 2D weights suitable for 3D plotting")
            return
        
        weights = layer.weights[0].numpy()
        
        # Create 3D surface plot
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        x = np.arange(weights.shape[1])
        y = np.arange(weights.shape[0])
        X, Y = np.meshgrid(x, y)
        
        surf = ax.plot_surface(X, Y, weights, cmap='viridis', alpha=0.8)
        ax.set_title(f'3D Weight Surface - Layer {layer_index}: {layer.name}')
        ax.set_xlabel('Output Neuron')
        ax.set_ylabel('Input Neuron')
        ax.set_zlabel('Weight Value')
        
        plt.colorbar(surf)
        plt.show()
    
    def complete_visualization(self):
        """Run all visualizations"""
        print("🚀 Starting Complete Neural Network Visualization...")
        
        # 1. Architecture overview
        self.architecture_overview()
        
        # 2. Plot architecture
        self.plot_architecture()
        
        # 3. Visualkeras view
        self.visualkeras_view()
        
        # 4. Weight statistics
        self.weight_statistics()
        
        # 5. All weights visualization
        self.visualize_all_weights()
        
        # 6. Save for Netron
        self.interactive_netron()
        
        print("\n✅ Complete visualization finished!")
        print("Check the generated files and plots!")



# Create visualizer and run all visualizations
visualizer = NeuralNetworkVisualizer(model)



# Quick overview
visualizer.architecture_overview()



# All visualizations
visualizer.complete_visualization()



# Specific layer analysis
visualizer.visualize_specific_layer(1, detailed=True)



# 3D weight plot
visualizer.create_3d_weight_plot(1)

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.colors import Normalize
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec

class NotebookWeightVisualizer:
    def __init__(self, model):
        self.model = model
        self.layer_info = self._extract_layer_info()
        
    def _extract_layer_info(self):
        """Extract detailed layer information"""
        info = []
        for i, layer in enumerate(self.model.layers):
            if layer.weights:  # Only layers with weights
                layer_data = {
                    'index': i,
                    'name': layer.name,
                    'type': type(layer).__name__,
                    'units': getattr(layer, 'units', 0),
                    'weights': [],
                    'biases': []
                }
                
                for weight in layer.weights:
                    w_vals = weight.numpy()
                    if 'kernel' in weight.name.lower() or 'weight' in weight.name.lower():
                        layer_data['weights'].append({
                            'name': weight.name,
                            'values': w_vals,
                            'shape': w_vals.shape
                        })
                    elif 'bias' in weight.name.lower():
                        layer_data['biases'].append({
                            'name': weight.name,
                            'values': w_vals,
                            'shape': w_vals.shape
                        })
                
                info.append(layer_data)
        return info
    
    def display_all_weights_and_biases(self, save_filename=None):
        """Display all weights and biases in organized subplots"""
        if not self.layer_info:
            print("No layers with weights found!")
            return
        
        # Calculate subplot layout
        n_layers = len(self.layer_info)
        fig = plt.figure(figsize=(20, 6*n_layers))
        
        for i, layer in enumerate(self.layer_info):
            # Create subplot grid for this layer
            gs = GridSpec(2, 3, figure=fig, 
                         left=0.05, right=0.95, 
                         top=0.95-i*0.9/n_layers, bottom=0.95-(i+1)*0.9/n_layers,
                         wspace=0.3, hspace=0.4)
            
            # Layer title
            title_ax = fig.add_subplot(gs[0, :])
            title_ax.text(0.5, 0.5, 
                         f'Layer {layer["index"]}: {layer["name"]} ({layer["type"]})\\n'
                         f'{layer["units"]} units', 
                         ha='center', va='center', fontsize=16, weight='bold',
                         bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
            title_ax.axis('off')
            
            # Weight matrix heatmap
            if layer['weights']:
                weight_ax = fig.add_subplot(gs[1, 0])
                self._plot_weight_matrix(weight_ax, layer['weights'][0], f"Layer {layer['index']} Weights")
            
            # Bias vector
            if layer['biases']:
                bias_ax = fig.add_subplot(gs[1, 1])
                self._plot_bias_vector(bias_ax, layer['biases'][0], f"Layer {layer['index']} Biases")
            
            # Weight distribution
            if layer['weights'] or layer['biases']:
                dist_ax = fig.add_subplot(gs[1, 2])
                self._plot_weight_distribution(dist_ax, layer, f"Layer {layer['index']} Distribution")
        
        plt.suptitle('Complete Weights and Biases Visualization', fontsize=20, weight='bold', y=0.98)
        plt.show()
        
        if save_filename:
            fig.savefig(save_filename, dpi=300, bbox_inches='tight')
            print(f"✅ Saved to {save_filename}")
    
    def _plot_weight_matrix(self, ax, weight_info, title):
        """Plot weight matrix as heatmap"""
        w_vals = weight_info['values']
        
        # Create heatmap
        im = ax.imshow(w_vals, cmap='RdBu', aspect='auto', 
                      vmin=-np.abs(w_vals).max(), vmax=np.abs(w_vals).max())
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Weight Value', rotation=270, labelpad=20)
        
        # Add title and labels
        ax.set_title(f'{title}\\nShape: {w_vals.shape}', fontsize=12, weight='bold')
        ax.set_xlabel('Output Neurons')
        ax.set_ylabel('Input Neurons')
        
        # Add weight values for small matrices
        if w_vals.shape[0] <= 8 and w_vals.shape[1] <= 8:
            for i in range(w_vals.shape[0]):
                for j in range(w_vals.shape[1]):
                    text_color = 'white' if abs(w_vals[i, j]) > 0.5 * np.abs(w_vals).max() else 'black'
                    ax.text(j, i, f'{w_vals[i, j]:.3f}', 
                           ha='center', va='center', 
                           color=text_color, fontsize=8, weight='bold')
        
        # Add statistics
        stats_text = f'Min: {w_vals.min():.3f}\\nMax: {w_vals.max():.3f}\\n'
        stats_text += f'Mean: {w_vals.mean():.3f}\\nStd: {w_vals.std():.3f}'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8),
               verticalalignment='top', fontsize=9)
    
    def _plot_bias_vector(self, ax, bias_info, title):
        """Plot bias vector as bar chart"""
        b_vals = bias_info['values']
        
        # Create bar chart
        bars = ax.bar(range(len(b_vals)), b_vals, 
                     color=['red' if b < 0 else 'blue' for b in b_vals],
                     alpha=0.7, edgecolor='black', linewidth=0.5)
        
        # Add value labels on bars
        for i, (bar, val) in enumerate(zip(bars, b_vals)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01*np.sign(height),
                   f'{val:.3f}', ha='center', va='bottom' if height > 0 else 'top',
                   fontsize=9, weight='bold')
        
        # Formatting
        ax.set_title(f'{title}\\nShape: {b_vals.shape}', fontsize=12, weight='bold')
        ax.set_xlabel('Neuron Index')
        ax.set_ylabel('Bias Value')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.grid(True, alpha=0.3)
        
        # Add statistics
        stats_text = f'Min: {b_vals.min():.3f}\\nMax: {b_vals.max():.3f}\\n'
        stats_text += f'Mean: {b_vals.mean():.3f}\\nStd: {b_vals.std():.3f}'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8),
               verticalalignment='top', fontsize=9)
    
    def _plot_weight_distribution(self, ax, layer, title):
        """Plot distribution of all weights and biases"""
        all_values = []
        labels = []
        colors = []
        
        # Collect weights
        for weight_info in layer['weights']:
            all_values.extend(weight_info['values'].flatten())
            labels.extend(['Weight'] * weight_info['values'].size)
            colors.extend(['blue'] * weight_info['values'].size)
        
        # Collect biases
        for bias_info in layer['biases']:
            all_values.extend(bias_info['values'].flatten())
            labels.extend(['Bias'] * bias_info['values'].size)
            colors.extend(['red'] * bias_info['values'].size)
        
        # Create histogram
        if all_values:
            # Separate weights and biases
            weights = [v for v, l in zip(all_values, labels) if l == 'Weight']
            biases = [v for v, l in zip(all_values, labels) if l == 'Bias']
            
            if weights:
                ax.hist(weights, bins=30, alpha=0.7, color='blue', label='Weights', density=True)
            if biases:
                ax.hist(biases, bins=15, alpha=0.7, color='red', label='Biases', density=True)
            
            # Add vertical lines for means
            if weights:
                ax.axvline(np.mean(weights), color='blue', linestyle='--', 
                          label=f'Weight Mean: {np.mean(weights):.3f}')
            if biases:
                ax.axvline(np.mean(biases), color='red', linestyle='--', 
                          label=f'Bias Mean: {np.mean(biases):.3f}')
            
            ax.set_title(title, fontsize=12, weight='bold')
            ax.set_xlabel('Value')
            ax.set_ylabel('Density')
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    def display_individual_layers(self, save_prefix=None):
        """Display each layer individually in separate plots"""
        for i, layer in enumerate(self.layer_info):
            print(f"\\n{'='*60}")
            print(f"LAYER {layer['index']}: {layer['name']} ({layer['type']})")
            print(f"{'='*60}")
            
            # Create figure for this layer
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Layer {layer["index"]}: {layer["name"]}', fontsize=16, weight='bold')
            
            # Weight matrix
            if layer['weights']:
                self._plot_weight_matrix(axes[0, 0], layer['weights'][0], "Weight Matrix")
                
                # Weight statistics
                w_vals = layer['weights'][0]['values']
                print(f"\\n📊 WEIGHT STATISTICS:")
                print(f"   Shape: {w_vals.shape}")
                print(f"   Total parameters: {w_vals.size:,}")
                print(f"   Range: [{w_vals.min():.6f}, {w_vals.max():.6f}]")
                print(f"   Mean ± Std: {w_vals.mean():.6f} ± {w_vals.std():.6f}")
                print(f"   Zero weights: {np.sum(w_vals == 0)} ({100*np.sum(w_vals == 0)/w_vals.size:.1f}%)")
            else:
                axes[0, 0].text(0.5, 0.5, 'No Weight Matrix', ha='center', va='center', fontsize=14)
                axes[0, 0].set_title('Weight Matrix')
            
            # Bias vector
            if layer['biases']:
                self._plot_bias_vector(axes[0, 1], layer['biases'][0], "Bias Vector")
                
                # Bias statistics
                b_vals = layer['biases'][0]['values']
                print(f"\\n📊 BIAS STATISTICS:")
                print(f"   Shape: {b_vals.shape}")
                print(f"   Range: [{b_vals.min():.6f}, {b_vals.max():.6f}]")
                print(f"   Mean ± Std: {b_vals.mean():.6f} ± {b_vals.std():.6f}")
                print(f"   Values: {b_vals}")
            else:
                axes[0, 1].text(0.5, 0.5, 'No Bias Vector', ha='center', va='center', fontsize=14)
                axes[0, 1].set_title('Bias Vector')
            
            # Distribution
            self._plot_weight_distribution(axes[1, 0], layer, "Value Distribution")
            
            # Weight-bias correlation (if both exist)
            if layer['weights'] and layer['biases']:
                w_vals = layer['weights'][0]['values']
                b_vals = layer['biases'][0]['values']
                
                # Plot weight means vs biases
                if w_vals.ndim == 2:
                    weight_means = w_vals.mean(axis=0)  # Mean weight per output neuron
                    axes[1, 1].scatter(weight_means, b_vals, alpha=0.7, s=100, color='purple')
                    axes[1, 1].set_xlabel('Mean Weight per Output Neuron')
                    axes[1, 1].set_ylabel('Bias Value')
                    axes[1, 1].set_title('Weight-Bias Relationship')
                    axes[1, 1].grid(True, alpha=0.3)
                    
                    # Add correlation coefficient
                    corr = np.corrcoef(weight_means, b_vals)[0, 1]
                    axes[1, 1].text(0.02, 0.98, f'Correlation: {corr:.3f}', 
                                   transform=axes[1, 1].transAxes, 
                                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8),
                                   verticalalignment='top')
            else:
                axes[1, 1].text(0.5, 0.5, 'Weight-Bias Analysis\\nNot Available', 
                               ha='center', va='center', fontsize=14)
                axes[1, 1].set_title('Weight-Bias Relationship')
            
            plt.tight_layout()
            plt.show()
            
            if save_prefix:
                filename = f"{save_prefix}_layer_{layer['index']}.png"
                fig.savefig(filename, dpi=300, bbox_inches='tight')
                print(f"✅ Saved layer {layer['index']} to {filename}")
    
    def display_network_architecture_with_weights(self, save_filename=None):
        """Display network architecture with weight information"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
        
        # Left side: Network architecture
        self._draw_network_architecture(ax1)
        
        # Right side: Weight summary table
        self._draw_weight_summary_table(ax2)
        
        plt.suptitle('Neural Network Architecture with Weight Summary', fontsize=18, weight='bold')
        plt.tight_layout()
        plt.show()
        
        if save_filename:
            fig.savefig(save_filename, dpi=300, bbox_inches='tight')
            print(f"✅ Saved to {save_filename}")
    
    def _draw_network_architecture(self, ax):
        """Draw the network architecture diagram"""
        # Get all layers (including input)
        all_layers = []
        for i, layer in enumerate(self.model.layers):
            if hasattr(layer, 'units'):
                all_layers.append((f"Layer {i}\\n{layer.name}\\n{layer.units} units", layer.units))
            elif hasattr(layer, 'input_shape') and layer.input_shape:
                input_size = layer.input_shape[-1] if layer.input_shape[-1] else 1
                all_layers.append((f"Layer {i}\\n{layer.name}\\n{input_size} inputs", input_size))
        
        # Position layers
        x_positions = np.linspace(0.1, 0.9, len(all_layers))
        colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink']
        
        for i, ((name, units), x_pos) in enumerate(zip(all_layers, x_positions)):
            # Draw layer rectangle
            height = 0.1 + 0.3 * (units / max(layer[1] for layer in all_layers))
            rect = patches.Rectangle((x_pos-0.06, 0.5-height/2), 0.12, height, 
                                   facecolor=colors[i % len(colors)], 
                                   edgecolor='black', linewidth=2)
            ax.add_patch(rect)
            
            # Add text
            ax.text(x_pos, 0.5, name, ha='center', va='center', 
                   fontsize=10, weight='bold')
            
            # Draw connections with weight info
            if i > 0:
                # Find weight info for this connection
                weight_info = ""
                if i-1 < len(self.layer_info):
                    layer_info = self.layer_info[i-1]
                    if layer_info['weights']:
                        w_shape = layer_info['weights'][0]['shape']
                        weight_info = f"W: {w_shape}"
                
                # Draw arrow
                ax.arrow(x_positions[i-1]+0.06, 0.5, 
                        x_positions[i]-x_positions[i-1]-0.12, 0,
                        head_width=0.02, head_length=0.02, fc='black', ec='black')
                
                # Add weight info
                if weight_info:
                    mid_x = (x_positions[i-1] + x_positions[i]) / 2
                    ax.text(mid_x, 0.7, weight_info, ha='center', va='center', 
                           fontsize=8, bbox=dict(boxstyle="round,pad=0.2", 
                                               facecolor='white', alpha=0.8))
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title('Network Architecture', fontsize=14, weight='bold')
        ax.axis('off')
    
    def _draw_weight_summary_table(self, ax):
        """Draw weight summary table"""
        # Create table data
        table_data = []
        for layer in self.layer_info:
            if layer['weights']:
                w_vals = layer['weights'][0]['values']
                row = [f"L{layer['index']}", layer['name'], 
                       f"{w_vals.shape}", f"{w_vals.size:,}",
                       f"{w_vals.min():.3f}", f"{w_vals.max():.3f}",
                       f"{w_vals.mean():.3f}", f"{w_vals.std():.3f}"]
                table_data.append(row)
            
            if layer['biases']:
                b_vals = layer['biases'][0]['values']
                row = [f"L{layer['index']}", "bias", 
                       f"{b_vals.shape}", f"{b_vals.size:,}",
                       f"{b_vals.min():.3f}", f"{b_vals.max():.3f}",
                       f"{b_vals.mean():.3f}", f"{b_vals.std():.3f}"]
                table_data.append(row)
        
        # Create table
        headers = ['Layer', 'Type', 'Shape', 'Count', 'Min', 'Max', 'Mean', 'Std']
        
        # Draw table
        table = ax.table(cellText=table_data, colLabels=headers, 
                        cellLoc='center', loc='center',
                        colWidths=[0.08, 0.15, 0.12, 0.08, 0.08, 0.08, 0.08, 0.08])
        
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)
        
        # Style the table
        for i in range(len(headers)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        ax.set_title('Weight and Bias Summary', fontsize=14, weight='bold')
        ax.axis('off')
    
    def quick_weight_overview(self):
        """Quick overview of all weights and biases"""
        print("🚀 QUICK WEIGHT & BIAS OVERVIEW")
        print("=" * 60)
        
        total_params = 0
        for layer in self.layer_info:
            print(f"\\n📍 Layer {layer['index']}: {layer['name']} ({layer['type']})")
            
            if layer['weights']:
                w_vals = layer['weights'][0]['values']
                print(f"   🔹 Weights: {w_vals.shape} -> {w_vals.size:,} parameters")
                print(f"      Range: [{w_vals.min():.4f}, {w_vals.max():.4f}]")
                print(f"      Mean±Std: {w_vals.mean():.4f}±{w_vals.std():.4f}")
                total_params += w_vals.size
            
            if layer['biases']:
                b_vals = layer['biases'][0]['values']
                print(f"   🔹 Biases: {b_vals.shape} -> {b_vals.size:,} parameters")
                print(f"      Range: [{b_vals.min():.4f}, {b_vals.max():.4f}]")
                print(f"      Values: {b_vals}")
                total_params += b_vals.size
        
        print(f"\\n🎯 TOTAL TRAINABLE PARAMETERS: {total_params:,}")
        
        # Create a quick visualization
        fig, axes = plt.subplots(1, len(self.layer_info), figsize=(5*len(self.layer_info), 6))
        if len(self.layer_info) == 1:
            axes = [axes]
        
        for i, layer in enumerate(self.layer_info):
            if layer['weights']:
                w_vals = layer['weights'][0]['values']
                im = axes[i].imshow(w_vals, cmap='RdBu', aspect='auto')
                axes[i].set_title(f'Layer {layer["index"]} Weights\\n{w_vals.shape}')
                plt.colorbar(im, ax=axes[i])
            else:
                axes[i].text(0.5, 0.5, 'No Weights', ha='center', va='center')
                axes[i].set_title(f'Layer {layer["index"]}')
        
        plt.suptitle('Weight Matrices Overview', fontsize=16, weight='bold')
        plt.tight_layout()
        plt.show()

# Usage functions
def visualize_model_weights(model, save_files=False):
    """Complete weight visualization for any model"""
    print("🎯 Starting Complete Weight & Bias Visualization...")
    
    visualizer = NotebookWeightVisualizer(model)
    
    # 1. Quick overview
    visualizer.quick_weight_overview()
    
    # 2. Individual layers
    print("\\n" + "="*80)
    print("INDIVIDUAL LAYER ANALYSIS")
    print("="*80)
    save_prefix = "layer_analysis" if save_files else None
    visualizer.display_individual_layers(save_prefix)
    
    # 3. All weights and biases together
    print("\\n" + "="*80)
    print("COMPLETE WEIGHTS & BIASES VISUALIZATION")
    print("="*80)
    save_filename = "complete_weights_biases.png" if save_files else None
    visualizer.display_all_weights_and_biases(save_filename)
    
    # 4. Network architecture with weights
    print("\\n" + "="*80)
    print("NETWORK ARCHITECTURE WITH WEIGHT SUMMARY")
    print("="*80)
    save_filename = "network_architecture_weights.png" if save_files else None
    visualizer.display_network_architecture_with_weights(save_filename)
    
    print("\\n✅ Complete weight visualization finished!")




# Use with your model
visualize_model_weights(model, save_files=True)  # Set to False to not save files



# Or use individual components
visualizer = NotebookWeightVisualizer(model)


visualizer.quick_weight_overview()


visualizer.display_individual_layers()

trainable_count = np.sum([np.prod(v.shape) for v in model.trainable_variables])
print(f'Trainable parameters: {trainable_count}')

model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])


optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)


model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])

model.fit(X_train, y_train, epochs=10, batch_size=8,validation_split=0.2)


log_dir = "logs/fit/" + datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
tensorboard_callback = tf.keras.callbacks.TensorBoard(log_dir=log_dir, histogram_freq=1)

model.fit(X_train, y_train, epochs=10, batch_size=8,validation_split=0.2,callbacks=[tensorboard_callback])

model.save("model.h5")
model.save('my_model.keras')


import joblib
joblib.dump(scaler, 'scaler.joblib')


model =tf.keras.models.load_model('model.h5')
model.summary()

scaler = joblib.load('scaler.joblib')
scaler

a=[[6.8,2.4,4.6,1.75]]
a

new_scaled =scaler.transform(a)
model.predict(new_scaled)

np.argmax(model.predict(new_scaled), axis=1)
# model.predict(new_scaled

iris.target_names[np.argmax(model.predict(new_scaled), axis=1)]

def iris_prediction(new_data):
    new_scaled =scaler.transform(new_data)
    class_label = iris.target_names[np.argmax(model.predict(new_scaled), axis=1)]
    return class_label  

iris_prediction(new_scaled)

iris_prediction([[6.8,2.4,4.6,1.75]])

!pip install keras-tuner
import keras_tuner as kt


import tensorflow as tf
import keras_tuner as kt
import os
import shutil
# Clean up any corrupted tuner files
if os.path.exists("kt_logs"):
    shutil.rmtree("kt_logs")
    print("Cleaned up old tuner logs")
    
def build_model(hp):
    """
    OVERALL PURPOSE: This function builds a neural network with hyperparameter tuning.
    Instead of manually trying different configurations, this function lets Keras Tuner
    automatically test different combinations of:
    - Number of hidden layers (1-3)
    - Number of neurons in each layer (4-32)
    - Learning rates (0.01, 0.001, 0.0001)
    
    The 'hp' parameter is a HyperParameters object from Keras Tuner that suggests
    values to try during the optimization process.
    """
    
    # START: Create empty sequential model (layers stacked one after another)
    model = tf.keras.Sequential()
    
    # INPUT LAYER: Define what data format the model expects
    # shape=(4,) means each input sample has 4 features
    # Example: [sepal_length, sepal_width, petal_length, petal_width] for iris dataset
    model.add(tf.keras.layers.Input(shape=(4,)))
    
    # HYPERPARAMETER TUNING: Variable number of hidden layers
    # hp.Int("num_layers", 1, 3) tries 1, 2, or 3 hidden layers
    for i in range(hp.Int("num_layers", 1, 3)):
        
        # ADD HIDDEN LAYER: Each iteration adds one Dense (fully connected) layer
        model.add(tf.keras.layers.Dense(
            # TUNE NEURONS: Number of neurons in this layer
            # hp.Int(f"units_{i}", min_value=4, max_value=32, step=4)
            # tries: 4, 8, 12, 16, 20, 24, 28, 32 neurons
            # f"units_{i}" creates unique names: "units_0", "units_1", "units_2"
            units=hp.Int(f"units_{i}", 
                        min_value=4,    # Minimum 4 neurons
                        max_value=32,   # Maximum 32 neurons  
                        step=4),        # Increment by 4 (4,8,12,16...)
            
            # ACTIVATION FUNCTION: ReLU adds non-linearity
            # ReLU(x) = max(0, x) - helps model learn complex patterns
            activation="relu"
        ))
    
    # OUTPUT LAYER: Final layer for predictions (fixed, not tuned)
    # 3 neurons = 3 classes (e.g., setosa, versicolor, virginica)
    # softmax activation converts raw outputs to probabilities that sum to 1
    # Example output: [0.1, 0.7, 0.2] means 70% confident it's class 2
    model.add(tf.keras.layers.Dense(
        3,                    # Number of classes to predict
        activation="softmax"  # Converts to probabilities
    ))
    
    # HYPERPARAMETER TUNING: Learning rate optimization
    # hp.Choice tries these 3 learning rates to find the best one:
    # - 1e-2 (0.01): Fast learning, might overshoot optimal solution
    # - 1e-3 (0.001): Moderate learning, good balance
    # - 1e-4 (0.0001): Slow learning, more precise but takes longer
    lr = hp.Choice("learning_rate", [1e-2, 1e-3, 1e-4])
    
    # COMPILE MODEL: Set up the training configuration
    model.compile(
        # OPTIMIZER: Algorithm that updates model weights
        # Adam is adaptive learning rate optimizer (popular choice)
      
        optimizer=tf.keras.optimizers.Adam(learning_rate=lr),
        
        # LOSS FUNCTION: How to measure prediction errors
        # categorical_crossentropy: Standard for multi-class classification
        # Penalizes confident wrong predictions more than uncertain ones
        loss="categorical_crossentropy",
        
        # METRICS: What to track during training (doesn't affect learning)
        # accuracy: Percentage of correct predictions
        metrics=["accuracy"]
    )
    
    # RETURN: Complete model ready for training
    return model


# EXAMPLE OF HOW KERAS TUNER USES THIS:
"""
Keras Tuner will call this function many times with different hp values:

Trial 1: num_layers=1, units_0=8, learning_rate=0.001
Trial 2: num_layers=3, units_0=16, units_1=12, units_2=4, learning_rate=0.01
Trial 3: num_layers=2, units_0=32, units_1=20, learning_rate=0.0001
...and so on

It trains each configuration and picks the one with best validation performance.
"""

# STEP 1: Set up the tuner
tuner = kt.RandomSearch(
    build_model,                    # Function that builds models to test
    objective="val_accuracy",       # Metric to optimize (maximize validation accuracy)
    max_trials=5,                   # Test 5 different model configurations
    executions_per_trial=2,         # Train each configuration 2 times (reduces randomness)
    directory="kt_logs",            # Where to save tuning results
    project_name="iris_tuning"      # Name for this tuning experiment
)

# STEP 2: Search for the best hyperparameters
tuner.search(
    X_train, y_train,               # Training data
    epochs=20,                      # Train each trial for 20 epochs
    validation_split=0.2            # Use 20% of training data for validation
)


# STEP 3: Get the best model
best_model = tuner.get_best_models(num_models=1)[0]

# STEP 4: See what the best configuration was
best_hyperparameters = tuner.get_best_hyperparameters()[0]
print("Best hyperparameters:")
print(f"Number of layers: {best_hyperparameters.get('num_layers')}")
print(f"Learning rate: {best_hyperparameters.get('learning_rate')}")

# Print units for each layer
for i in range(best_hyperparameters.get('num_layers')):
    units = best_hyperparameters.get(f'units_{i}')
    print(f"Layer {i+1} units: {units}")

tuner.results_summary()

# Get the best model from your tuning
best_model = tuner.get_best_models(num_models=1)[0]
best_hp = tuner.get_best_hyperparameters()[0]

print("🏆 BEST MODEL CONFIGURATION:")
print(f"Validation Accuracy: 97.92%")
print(f"Architecture: Input(4) → Dense(8) → Dense(32) → Output(3)")
print(f"Learning Rate: {best_hp.get('learning_rate')}")
print(f"Total Parameters: {best_model.count_params()}")

# Show the model architecture
print("\n📊 MODEL SUMMARY:")
best_model.summary()

# Test on your test set (if you have one)
if 'X_test' in locals() and 'y_test' in locals():
    test_loss, test_accuracy = best_model.evaluate(X_test, y_test, verbose=0)
    print(f"\n🎯 TEST SET PERFORMANCE:")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print(f"Test Loss: {test_loss:.4f}")

# Save the best model
best_model.save('best_iris_model.h5')
print(f"\n💾 Model saved as 'best_iris_model.h5'")

# Manual recreation of best model (for understanding)
print(f"\n🔧 TO RECREATE THIS MODEL MANUALLY:")
print(f"""
model = tf.keras.Sequential([
    tf.keras.layers.Input(shape=(4,)),
    tf.keras.layers.Dense(8, activation='relu'),
    tf.keras.layers.Dense(32, activation='relu'), 
    tf.keras.layers.Dense(3, activation='softmax')
])

model.compile(
    optimizer=tf.keras.optimizers.Adam(learning_rate=0.01),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)
""")

import tensorflow as tf
import numpy as np
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from tensorflow.keras.utils import to_categorical
import matplotlib.pyplot as plt

# =====================================
# OPTIMAL IRIS NETWORK ARCHITECTURE
# Based on Hyperparameter Tuning Results
# =====================================

def create_optimal_iris_model():
    """
    Creates the optimal network architecture for Iris classification
    Based on hyperparameter tuning results:
    - Best configuration: 97.92% validation accuracy
    - Architecture: 4 inputs → 8 neurons → 32 neurons → 3 outputs
    - Learning rate: 0.01 (critical for performance)
    """
    
    model = tf.keras.Sequential([
        # INPUT LAYER: 4 features (sepal_length, sepal_width, petal_length, petal_width)
        tf.keras.layers.Input(shape=(4,), name='iris_features'),
        
        # HIDDEN LAYER 1: 8 neurons with ReLU activation
        # Why 8? Tuning showed this captures essential patterns without overfitting
        tf.keras.layers.Dense(
            units=8, 
            activation='relu', 
            name='feature_extraction',
            kernel_initializer='he_normal'  # Good for ReLU activation
        ),
        
        # HIDDEN LAYER 2: 32 neurons with ReLU activation  
        # Why 32? Provides model capacity for complex decision boundaries
        tf.keras.layers.Dense(
            units=32, 
            activation='relu', 
            name='pattern_recognition',
            kernel_initializer='he_normal'
        ),
        
        # OUTPUT LAYER: 3 neurons for 3 iris species
        # Softmax converts to probabilities that sum to 1
        tf.keras.layers.Dense(
            units=3, 
            activation='softmax', 
            name='classification'
        )
    ], name='OptimalIrisClassifier')
    
    # COMPILE WITH OPTIMAL SETTINGS
    # Learning rate 0.01 was critical - much better than 0.0001
    model.compile(
        optimizer=tf.keras.optimizers.Adam(
            learning_rate=0.01,  # Key finding from tuning!
            beta_1=0.9,
            beta_2=0.999
        ),
        loss='categorical_crossentropy',  # Standard for multi-class classification
        metrics=['accuracy', 'precision', 'recall']
    )
    
    return model

# =====================================
# COMPLETE TRAINING PIPELINE
# =====================================

def prepare_iris_data():
    """Load and prepare Iris dataset for training"""
    
    # Load the famous Iris dataset
    iris = load_iris()
    X, y = iris.data, iris.target
    
    print("📊 DATASET INFO:")
    print(f"Features: {iris.feature_names}")
    print(f"Classes: {iris.target_names}")
    print(f"Samples: {X.shape[0]}, Features: {X.shape[1]}")
    
    # Convert labels to categorical (one-hot encoding)
    y_categorical = to_categorical(y, num_classes=3)
    
    # Split data: 70% train, 15% validation, 15% test
    X_train, X_temp, y_train, y_temp = train_test_split(
        X, y_categorical, test_size=0.3, random_state=42, stratify=y
    )
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp.argmax(axis=1)
    )
    
    print(f"\n📈 DATA SPLIT:")
    print(f"Training: {X_train.shape[0]} samples")
    print(f"Validation: {X_val.shape[0]} samples") 
    print(f"Test: {X_test.shape[0]} samples")
    
    return X_train, X_val, X_test, y_train, y_val, y_test

def train_optimal_model():
    """Train the optimal model with best practices"""
    
    # Prepare data
    X_train, X_val, X_test, y_train, y_val, y_test = prepare_iris_data()
    
    # Create the optimal model
    model = create_optimal_iris_model()
    
    print(f"\n🏗️ MODEL ARCHITECTURE:")
    model.summary()
    
    # Setup callbacks for better training
    callbacks = [
        # Stop early if validation accuracy stops improving
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=10,
            restore_best_weights=True,
            verbose=1
        ),
        
        # Reduce learning rate if stuck
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_accuracy',
            factor=0.5,
            patience=5,
            min_lr=0.0001,
            verbose=1
        ),
        
        # Save training logs for TensorBoard
        tf.keras.callbacks.TensorBoard(
            log_dir="logs/optimal_iris_model",
            histogram_freq=1,
            write_graph=True
        )
    ]
    
    print(f"\n🚀 STARTING TRAINING:")
    print("Based on tuning results - expecting ~98% validation accuracy")
    
    # Train the model
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=50,  # More epochs with early stopping
        batch_size=8,  # Small batch size works well for small dataset
        callbacks=callbacks,
        verbose=1
    )
    
    # Evaluate on test set (final performance)
    test_loss, test_accuracy, test_precision, test_recall = model.evaluate(
        X_test, y_test, verbose=0
    )
    
    print(f"\n🎯 FINAL TEST RESULTS:")
    print(f"Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
    print(f"Test Precision: {test_precision:.4f}")
    print(f"Test Recall: {test_recall:.4f}")
    
    # Save the trained model
    model.save('optimal_iris_classifier.h5')
    print(f"\n💾 Model saved as 'optimal_iris_classifier.h5'")
    
    return model, history, (X_test, y_test)

def plot_training_results(history):
    """Visualize training progress"""
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Optimal Iris Model Training Results', fontsize=16, fontweight='bold')
    
    # Accuracy plot
    axes[0,0].plot(history.history['accuracy'], label='Training', linewidth=2)
    axes[0,0].plot(history.history['val_accuracy'], label='Validation', linewidth=2)
    axes[0,0].set_title('Model Accuracy')
    axes[0,0].set_ylabel('Accuracy')
    axes[0,0].set_xlabel('Epoch')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # Loss plot
    axes[0,1].plot(history.history['loss'], label='Training', linewidth=2)
    axes[0,1].plot(history.history['val_loss'], label='Validation', linewidth=2)
    axes[0,1].set_title('Model Loss')
    axes[0,1].set_ylabel('Loss')
    axes[0,1].set_xlabel('Epoch')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # Precision plot
    axes[1,0].plot(history.history['precision'], label='Training', linewidth=2)
    axes[1,0].plot(history.history['val_precision'], label='Validation', linewidth=2)
    axes[1,0].set_title('Model Precision')
    axes[1,0].set_ylabel('Precision')
    axes[1,0].set_xlabel('Epoch')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # Recall plot
    axes[1,1].plot(history.history['recall'], label='Training', linewidth=2)
    axes[1,1].plot(history.history['val_recall'], label='Validation', linewidth=2)
    axes[1,1].set_title('Model Recall')
    axes[1,1].set_ylabel('Recall')
    axes[1,1].set_xlabel('Epoch')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimal_iris_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()

# =====================================
# RUN THE COMPLETE PIPELINE
# =====================================

if __name__ == "__main__":
    print("🌸 OPTIMAL IRIS CLASSIFICATION NETWORK 🌸")
    print("=" * 50)
    print("Based on hyperparameter tuning results:")
    print("• Architecture: 4 → 8 → 32 → 3")
    print("• Learning Rate: 0.01")
    print("• Expected Accuracy: ~98%")
    print("=" * 50)
    
    # Train the optimal model
    model, history, test_data = train_optimal_model()
    
    # Visualize results
    plot_training_results(history)
    
    print(f"\n✅ TRAINING COMPLETE!")
    print(f"Your optimal Iris classifier is ready!")
    print(f"Run 'tensorboard --logdir=logs/optimal_iris_model' to view detailed training logs")