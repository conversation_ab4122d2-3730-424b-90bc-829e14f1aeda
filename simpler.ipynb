"""
HOME CREDIT: QUICK START GUIDE
==============================
Step-by-step guide to compare traditional ML models with your neural network
using your existing data files.
"""

# =============================================================================
# STEP 1: INSTALL REQUIRED PACKAGES
# =============================================================================

"""
First, install required packages if you don't have them:

pip install xgboost lightgbm catboost optuna
pip install scikit-learn pandas numpy matplotlib seaborn

If you get installation errors, you can start with just:
pip install xgboost scikit-learn pandas numpy matplotlib
"""

# =============================================================================
# STEP 2: QUICK START (5 MINUTES)
# =============================================================================

def quick_start():
    """
    5-minute quick start to compare models
    """
    
    print("🚀 HOME CREDIT: 5-MINUTE QUICK START")
    print("=" * 37)
    
    # Import the simple comparison function
    from simple_ml_setup import run_simple_comparison
    
    # Run comparison using your data
    # Adjust the path to match your file location
    results = run_simple_comparison("fe_data/application_train_engineered.csv")
    
    if results:
        print("\n✅ Quick comparison completed!")
        print("📊 Check the plots and results above")
        return results
    else:
        print("❌ Comparison failed - check your data path")
        return None

# =============================================================================
# STEP 3: COMPREHENSIVE ANALYSIS (30 MINUTES)
# =============================================================================

def comprehensive_analysis():
    """
    Full comprehensive analysis with all models
    """
    
    print("🔬 HOME CREDIT: COMPREHENSIVE ANALYSIS")
    print("=" * 39)
    
    # Import the comprehensive comparison
    from traditional_ml_comparison import run_complete_model_comparison
    
    # Run full analysis
    results = run_complete_model_comparison("fe_data/application_train_engineered.csv")
    
    if results:
        print("\n✅ Comprehensive analysis completed!")
        print("📊 Check all visualizations and detailed report above")
        return results
    else:
        print("❌ Analysis failed - check your data path and packages")
        return None

# =============================================================================
# STEP 4: CUSTOM MODEL TRAINING
# =============================================================================

def train_custom_models():
    """
    Example of training your own custom models
    """
    
    print("🛠️ TRAINING CUSTOM MODELS")
    print("=" * 25)
    
    # Load your data
    from simple_ml_setup import load_and_prepare_data
    
    # Prepare data
    data = load_and_prepare_data("fe_data/application_train_engineered.csv")
    
    if data is None:
        print("❌ Failed to load data")
        return
    
    X_train, X_test, y_train, y_test, feature_names, scaler = data
    
    print(f"📊 Data loaded: {len(X_train)} train, {len(X_test)} test samples")
    
    # Train different models
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.linear_model import LogisticRegression
    import xgboost as xgb
    from sklearn.metrics import f1_score, roc_auc_score
    
    models = {
        'Random Forest': RandomForestClassifier(
            n_estimators=200,
            max_depth=12,
            random_state=42,
            n_jobs=-1,
            class_weight='balanced'
        ),
        
        'XGBoost': xgb.XGBClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            random_state=42,
            eval_metric='logloss',
            scale_pos_weight=len(y_train[y_train == 0]) / len(y_train[y_train == 1])
        ),
        
        'Gradient Boosting': GradientBoostingClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            random_state=42
        ),
        
        'Logistic Regression': LogisticRegression(
            random_state=42,
            max_iter=1000,
            class_weight='balanced'
        )
    }
    
    results = {}
    
    print("\n🤖 Training models...")
    
    for name, model in models.items():
        print(f"\n   Training {name}...")
        
        # Train
        model.fit(X_train, y_train)
        
        # Predict
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # Metrics
        f1 = f1_score(y_test, y_pred, average='weighted')
        auc = roc_auc_score(y_test, y_pred_proba)
        default_rate = (y_pred == 1).mean() * 100
        
        results[name] = {
            'f1': f1,
            'auc': auc,
            'default_rate': default_rate,
            'model': model
        }
        
        print(f"      F1: {f1:.4f}, AUC: {auc:.4f}, Default Rate: {default_rate:.1f}%")
    
    # Compare with Neural Network
    print(f"\n🧠 Neural Network Baseline:")
    print(f"      F1: 0.8902, AUC: 0.7713, Default Rate: 7.6%")
    
    # Find best model
    best_model_name = max(results.keys(), key=lambda k: results[k]['f1'])
    best_f1 = results[best_model_name]['f1']
    
    print(f"\n🏆 RESULTS:")
    print(f"   Best Traditional ML: {best_model_name} (F1: {best_f1:.4f})")
    print(f"   Neural Network: F1: 0.8902")
    
    if best_f1 > 0.8902:
        print(f"   🎉 {best_model_name} beats Neural Network by {best_f1 - 0.8902:.4f}!")
    else:
        print(f"   🧠 Neural Network still leads by {0.8902 - best_f1:.4f}")
    
    return results

# =============================================================================
# STEP 5: TROUBLESHOOTING
# =============================================================================

def troubleshoot_data_loading():
    """
    Help diagnose data loading issues
    """
    
    print("🔧 TROUBLESHOOTING DATA LOADING")
    print("=" * 31)
    
    import os
    import pandas as pd
    
    # Check if directories exist
    directories = ["fe_data", "data"]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ Directory exists: {directory}")
            files = os.listdir(directory)
            print(f"   Files found:")
            for file in files:
                if file.endswith('.csv'):
                    print(f"      📄 {file}")
        else:
            print(f"❌ Directory not found: {directory}")
    
    # Try to load different possible file paths
    possible_paths = [
        "fe_data/application_train_engineered.csv",
        "data/application_train_engineered.csv", 
        "fe_data/application_train_clean.csv",
        "data/application_train_clean.csv",
        "application_train_engineered.csv",
        "application_train_clean.csv"
    ]
    
    print(f"\n🔍 Checking possible data file paths:")
    
    for path in possible_paths:
        if os.path.exists(path):
            try:
                df = pd.read_csv(path)
                print(f"✅ {path} - Shape: {df.shape}, Columns: {list(df.columns[:5])}...")
                
                # Check for target column
                possible_targets = ['TARGET', 'target', 'Target', 'default', 'DEFAULT']
                target_found = None
                for target in possible_targets:
                    if target in df.columns:
                        target_found = target
                        break
                
                if target_found:
                    print(f"   🎯 Target column found: {target_found}")
                    target_dist = df[target_found].value_counts()
                    print(f"   📊 Target distribution: {target_dist.to_dict()}")
                else:
                    print(f"   ⚠️  No standard target column found")
                
            except Exception as e:
                print(f"❌ {path} - Error loading: {e}")
        else:
            print(f"❌ {path} - File not found")
    
    print(f"\n💡 SUGGESTIONS:")
    print(f"   1. Make sure your CSV file is in the correct location")
    print(f"   2. Check that the target column is named 'TARGET' or similar")
    print(f"   3. Ensure the CSV file has the engineered features (not raw data)")
    print(f"   4. Try using the full path to your file")

def check_model_performance():
    """
    Quick check of what performance to expect
    """
    
    print("📊 EXPECTED MODEL PERFORMANCE")
    print("=" * 29)
    
    print("🎯 PERFORMANCE TARGETS:")
    print("   Neural Network (Your Baseline): F1=0.8902, AUC=0.7713")
    print("   Target to Beat: F1 > 0.88")
    print("   Default Rate Target: ~8.1%")
    
    print(f"\n🤖 EXPECTED TRADITIONAL ML PERFORMANCE:")
    print("   XGBoost: F1 ~0.86-0.90, AUC ~0.75-0.78")
    print("   Random Forest: F1 ~0.84-0.88, AUC ~0.72-0.76") 
    print("   LightGBM: F1 ~0.86-0.90, AUC ~0.74-0.77")
    print("   Gradient Boosting: F1 ~0.84-0.87, AUC ~0.72-0.75")
    print("   Logistic Regression: F1 ~0.80-0.84, AUC ~0.70-0.73")
    
    print(f"\n💡 WHAT TO EXPECT:")
    print("   • XGBoost and LightGBM will likely perform best")
    print("   • Tree-based models should get close to your Neural Network")
    print("   • Your Neural Network may still be the winner!")
    print("   • Default rate will likely be 15-30% without threshold optimization")
    print("   • You can apply the same threshold optimization to traditional ML")

# =============================================================================
# STEP 6: COMPLETE WORKFLOW
# =============================================================================

def complete_workflow():
    """
    Complete workflow from start to finish
    """
    
    print("🎯 COMPLETE WORKFLOW: START TO FINISH")
    print("=" * 38)
    
    try:
        # Step 1: Quick comparison
        print("\n1️⃣ STEP 1: QUICK COMPARISON")
        print("-" * 26)
        
        quick_results = quick_start()
        
        if quick_results is None:
            print("❌ Quick comparison failed - trying troubleshooting...")
            troubleshoot_data_loading()
            return None
        
        # Step 2: Train custom models 
        print("\n2️⃣ STEP 2: CUSTOM MODEL TRAINING")
        print("-" * 30)
        
        custom_results = train_custom_models()
        
        if custom_results is None:
            print("❌ Custom training failed")
            return None
        
        # Step 3: Performance summary
        print("\n3️⃣ STEP 3: FINAL PERFORMANCE SUMMARY")
        print("-" * 35)
        
        # Combine all results
        all_models = {}
        
        # From quick comparison
        for model, metrics in quick_results['results'].items():
            all_models[f"{model} (Quick)"] = metrics
        
        # From custom training
        for model, metrics in custom_results.items():
            all_models[f"{model} (Custom)"] = metrics
        
        # Find overall best
        best_model = max(all_models.keys(), key=lambda k: all_models[k]['f1'])
        best_f1 = all_models[best_model]['f1']
        
        print(f"🏆 FINAL RESULTS:")
        print(f"   Best Model Overall: {best_model}")
        print(f"   Best F1-Score: {best_f1:.4f}")
        print(f"   Neural Network Baseline: 0.8902")
        
        if best_f1 > 0.8902:
            print(f"   🎉 Traditional ML wins by {best_f1 - 0.8902:.4f}!")
        elif best_f1 > 0.88:
            print(f"   🎯 Exceeds target F1 > 0.88!")
        else:
            print(f"   🧠 Neural Network still leads!")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"   1. Apply threshold optimization to best traditional ML model")
        print(f"   2. Consider ensemble of top 3 models")
        print(f"   3. Test on additional validation data")
        print(f"   4. Deploy the best model for production use")
        
        return {
            'quick_results': quick_results,
            'custom_results': custom_results,
            'best_model': best_model,
            'best_f1': best_f1
        }
        
    except Exception as e:
        print(f"❌ Workflow failed: {e}")
        print(f"\n🔧 Try troubleshooting:")
        troubleshoot_data_loading()
        return None

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

if __name__ == "__main__":
    print("🏠 HOME CREDIT: TRADITIONAL ML vs NEURAL NETWORK")
    print("=" * 50)
    print("🎯 Compare your F1=0.8902 Neural Network with XGBoost, Random Forest, etc.")
    print()
    
    print("📋 CHOOSE YOUR APPROACH:")
    print()
    print("1️⃣ Quick Start (5 minutes):")
    print("   results = quick_start()")
    print()
    print("2️⃣ Comprehensive Analysis (30 minutes):")
    print("   results = comprehensive_analysis()")
    print()
    print("3️⃣ Custom Model Training:")
    print("   results = train_custom_models()")
    print()
    print("4️⃣ Complete Workflow:")
    print("   results = complete_workflow()")
    print()
    print("5️⃣ Troubleshooting:")
    print("   troubleshoot_data_loading()")
    print("   check_model_performance()")
    print()
    
    print("💡 RECOMMENDATIONS:")
    print("   • Start with quick_start() to verify everything works")
    print("   • Use comprehensive_analysis() for detailed comparison")  
    print("   • Your data should be: fe_data/application_train_engineered.csv")
    print("   • Expected: XGBoost will get close to your Neural Network!")
    print()
    
    print("🎯 EXPECTED OUTCOME:")
    print("   Your Neural Network (F1=0.8902) vs Traditional ML")
    print("   Winner will depend on your specific data and features!")