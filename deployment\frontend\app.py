"""
HOME CREDIT MODEL - STREAMLIT PROFESSIONAL FRONTEND
==================================================
Professional Streamlit web application for Home Credit default risk assessment
Features: Interactive UI, Real-time predictions, Batch processing, Analytics
"""

import streamlit as st
import requests
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import base64
import io

# =============================================================================
# 1. CONFIGURATION
# =============================================================================


class Config:
    """Application configuration"""

    # API Configuration
    API_BASE_URL = "http://localhost:8000"
    API_KEY = "demo_key_12345"  # Change for production

    # App Configuration
    APP_TITLE = "Home Credit Default Risk Assessment"
    APP_ICON = "🏦"
    LAYOUT = "wide"

    # Model Performance
    MODEL_F1_SCORE = 0.8876
    MODEL_AUC_SCORE = 0.7713
    OPTIMAL_THRESHOLD = 0.7200
    TARGET_DEFAULT_RATE = 8.1

# =============================================================================
# 2. STREAMLIT CONFIGURATION
# =============================================================================


st.set_page_config(
    page_title=Config.APP_TITLE,
    page_icon=Config.APP_ICON,
    layout=Config.LAYOUT,
    initial_sidebar_state="expanded"
)

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 0.5rem 0;
    }
    
    .risk-approved {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
        font-weight: bold;
        text-align: center;
    }
    
    .risk-rejected {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #dc3545;
        font-weight: bold;
        text-align: center;
    }
    
    .feature-importance {
        background-color: #fff3cd;
        padding: 0.5rem;
        border-radius: 0.3rem;
        margin: 0.2rem 0;
        border-left: 3px solid #ffc107;
    }
    
    .sidebar-info {
        background-color: #e7f3ff;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #007bff;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# =============================================================================
# 3. API CLIENT
# =============================================================================


class HomeCreditAPIClient:
    """
    Client for interacting with Home Credit API
    """

    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def health_check(self) -> Dict[str, Any]:
        """Check API health status"""
        try:
            response = requests.get(
                f"{self.base_url}/health", headers=self.headers, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "error", "message": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        try:
            response = requests.get(
                f"{self.base_url}/model/info", headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"error": str(e)}

    def predict_single(self, application_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make single prediction"""
        try:
            response = requests.post(
                f"{self.base_url}/predict",
                headers=self.headers,
                json=application_data,
                timeout=30
            )
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"error": str(e)}

    def predict_batch(self, applications: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Make batch predictions"""
        try:
            payload = {"applications": applications}
            response = requests.post(
                f"{self.base_url}/predict/batch",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"error": str(e)}

# Initialize API client


@st.cache_resource
def get_api_client():
    return HomeCreditAPIClient(Config.API_BASE_URL, Config.API_KEY)


api_client = get_api_client()

# =============================================================================
# 4. UTILITY FUNCTIONS
# =============================================================================


def format_currency(amount: float) -> str:
    """Format currency amounts"""
    return f"${amount:,.2f}"


def format_percentage(value: float) -> str:
    """Format percentage values"""
    return f"{value:.2%}"


def get_risk_color(risk_category: str) -> str:
    """Get color for risk category"""
    colors = {
        "LOW": "#28a745",
        "MEDIUM": "#ffc107",
        "HIGH": "#fd7e14",
        "VERY_HIGH": "#dc3545"
    }
    return colors.get(risk_category, "#6c757d")


def create_gauge_chart(value: float, title: str, max_value: float = 1.0):
    """Create gauge chart for probability display"""

    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=value,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': title},
        delta={'reference': Config.OPTIMAL_THRESHOLD},
        gauge={
            'axis': {'range': [None, max_value]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 0.3], 'color': "lightgreen"},
                {'range': [0.3, 0.7], 'color': "yellow"},
                {'range': [0.7, max_value], 'color': "red"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': Config.OPTIMAL_THRESHOLD
            }
        }
    ))

    fig.update_layout(height=300)
    return fig


def download_link(object_to_download, download_filename, download_link_text):
    """Generate download link for data"""
    if isinstance(object_to_download, pd.DataFrame):
        object_to_download = object_to_download.to_csv(index=False)

    b64 = base64.b64encode(object_to_download.encode()).decode()
    return f'<a href="data:file/txt;base64,{b64}" download="{download_filename}">{download_link_text}</a>'

# =============================================================================
# 5. MAIN APPLICATION
# =============================================================================


def main():
    """Main application function"""

    # Header
    st.markdown(f"""
    <div class="main-header">
        {Config.APP_ICON} {Config.APP_TITLE}
    </div>
    """, unsafe_allow_html=True)

    # Sidebar
    with st.sidebar:
        st.markdown("""
        <div class="sidebar-info">
            <h3>🎯 Model Performance</h3>
            <p><strong>F1-Score:</strong> 0.8876</p>
            <p><strong>AUC:</strong> 0.7713</p>
            <p><strong>Default Rate:</strong> 8.4%</p>
            <p><strong>Status:</strong> Production Ready ✅</p>
        </div>
        """, unsafe_allow_html=True)

        # Navigation
        st.subheader("📋 Navigation")
        page = st.selectbox(
            "Choose Application Mode:",
            ["🏠 Dashboard", "👤 Single Prediction",
                "📊 Batch Processing", "📈 Analytics", "🔧 API Status"]
        )

    # Route to selected page
    if page == "🏠 Dashboard":
        show_dashboard()
    elif page == "👤 Single Prediction":
        show_single_prediction()
    elif page == "📊 Batch Processing":
        show_batch_processing()
    elif page == "📈 Analytics":
        show_analytics()
    elif page == "🔧 API Status":
        show_api_status()

# =============================================================================
# 6. PAGE FUNCTIONS
# =============================================================================


def show_dashboard():
    """Show main dashboard"""

    st.subheader("🏠 Home Credit Risk Assessment Dashboard")

    # API Health Check
    health = api_client.health_check()

    if health.get("status") == "healthy":
        st.success("✅ API Service: Online and Ready")
    else:
        st.error(f"❌ API Service: {health.get('message', 'Offline')}")
        return

    # Model Info
    model_info = api_client.get_model_info()

    if "error" not in model_info:

        # Key Metrics Row
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                label="F1-Score",
                value=f"{model_info['performance']['f1_score']:.4f}",
                delta="Exceeds 0.88 target"
            )

        with col2:
            st.metric(
                label="AUC Score",
                value=f"{model_info['performance']['auc_score']:.4f}",
                delta="High accuracy"
            )

        with col3:
            st.metric(
                label="Default Rate",
                value=f"{model_info['performance']['default_rate']:.1%}",
                delta="Target: 8.1%"
            )

        with col4:
            st.metric(
                label="Total Features",
                value=model_info['features']['total_features'],
                delta="Comprehensive analysis"
            )

        # Model Architecture
        st.subheader("🏗️ Model Architecture")
        col1, col2 = st.columns([2, 1])

        with col1:
            st.info(f"""
            **Architecture:** {model_info['architecture']}

            **Training Data:** {model_info['training_data']['samples']:,} applications

            **Key Features:** {', '.join(model_info['features']['most_important'][:5])}

            **Optimal Threshold:** {model_info['performance']['optimal_threshold']:.4f}
            """)

        with col2:
            # Performance visualization
            fig = create_gauge_chart(
                model_info['performance']['f1_score'],
                "F1-Score Performance"
            )
            st.plotly_chart(fig, use_container_width=True)

        # Quick Start Guide
        st.subheader("🚀 Quick Start Guide")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            ### 👤 Single Prediction
            - Assess individual applications
            - Real-time risk analysis
            - Detailed explanations
            """)
            if st.button("Start Single Prediction", key="single"):
                st.session_state.page = "👤 Single Prediction"
                st.experimental_rerun()

        with col2:
            st.markdown("""
            ### 📊 Batch Processing
            - Upload CSV files
            - Process multiple applications
            - Download results
            """)
            if st.button("Start Batch Processing", key="batch"):
                st.session_state.page = "📊 Batch Processing"
                st.experimental_rerun()

        with col3:
            st.markdown("""
            ### 📈 Analytics
            - View prediction trends
            - Risk distribution analysis
            - Performance monitoring
            """)
            if st.button("View Analytics", key="analytics"):
                st.session_state.page = "📈 Analytics"
                st.experimental_rerun()

    else:
        st.error(
            f"Unable to load model information: {model_info.get('error')}")


def show_single_prediction():
    """Show single prediction interface"""

    st.subheader("👤 Single Application Risk Assessment")

    # Input Form
    with st.form("credit_application"):
        st.markdown("### 📋 Application Information")

        # Personal Information
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### Personal Details")
            days_birth = st.number_input(
                "Age (years)",
                min_value=18, max_value=80, value=35,
                help="Customer age in years"
            )

            code_gender = st.selectbox("Gender", ["M", "F"])

            cnt_children = st.number_input(
                "Number of Children",
                min_value=0, max_value=20, value=0
            )

            name_family_status = st.selectbox(
                "Family Status",
                ["Single / not married", "Married",
                    "Civil marriage", "Widow", "Separated"]
            )

            name_education_type = st.selectbox(
                "Education",
                ["Secondary / secondary special", "Higher education",
                    "Incomplete higher", "Lower secondary", "Academic degree"]
            )

        with col2:
            st.markdown("#### Employment & Income")

            amt_income_total = st.number_input(
                "Annual Income ($)",
                min_value=25000, max_value=5000000, value=150000,
                step=5000
            )

            days_employed = st.number_input(
                "Years Employed",
                min_value=0, max_value=50, value=5,
                help="Years in current employment"
            )

            name_income_type = st.selectbox(
                "Income Type",
                ["Working", "Commercial associate",
                    "Pensioner", "State servant", "Student"]
            )

            organization_type = st.selectbox(
                "Organization Type",
                ["Business Entity Type 3", "School",
                    "Government", "Religion", "Other", "XNA"]
            )

        # Loan Information
        st.markdown("#### Loan Details")
        col3, col4 = st.columns(2)

        with col3:
            amt_credit = st.number_input(
                "Loan Amount ($)",
                min_value=50000, max_value=5000000, value=500000,
                step=10000
            )

            amt_annuity = st.number_input(
                "Monthly Payment ($)",
                min_value=1000, max_value=300000, value=25000,
                step=1000
            )

        with col4:
            amt_goods_price = st.number_input(
                "Goods Price ($)",
                min_value=40000, max_value=5000000, value=450000,
                step=10000
            )

            name_contract_type = st.selectbox(
                "Contract Type",
                ["Cash loans", "Revolving loans"]
            )

        # External Scores (Most Important Features)
        st.markdown("#### External Credit Scores")
        col5, col6, col7 = st.columns(3)

        with col5:
            ext_source_1 = st.slider(
                "External Source 1",
                min_value=0.0, max_value=1.0, value=0.5, step=0.01,
                help="Credit bureau score 1 (higher is better)"
            )

        with col6:
            ext_source_2 = st.slider(
                "External Source 2",
                min_value=0.0, max_value=1.0, value=0.6, step=0.01,
                help="Credit bureau score 2 (most important feature)"
            )

        with col7:
            ext_source_3 = st.slider(
                "External Source 3",
                min_value=0.0, max_value=1.0, value=0.5, step=0.01,
                help="Credit bureau score 3"
            )

        # Regional Information
        st.markdown("#### Regional Information")
        col8, col9 = st.columns(2)

        with col8:
            region_population_relative = st.slider(
                "Regional Population (relative)",
                min_value=0.0, max_value=1.0, value=0.5,
                help="Population density of region"
            )

        with col9:
            region_rating_client = st.selectbox(
                "Region Rating",
                [1, 2, 3],
                help="1=Best, 3=Worst"
            )

        name_housing_type = st.selectbox(
            "Housing Type",
            ["House / apartment", "Municipal apartment", "With parents",
                "Co-op apartment", "Rented apartment", "Office apartment"]
        )

        # Submit Button
        submitted = st.form_submit_button(
            "🔍 Assess Credit Risk", use_container_width=True)

    if submitted:
        # Prepare application data
        application_data = {
            "days_birth": int(-days_birth * 365),  # Convert to negative days
            # Convert to negative days
            "days_employed": int(-days_employed * 365),
            "amt_income_total": amt_income_total,
            "code_gender": code_gender,
            "name_family_status": name_family_status,
            "cnt_children": cnt_children,
            "name_education_type": name_education_type,
            "amt_credit": amt_credit,
            "amt_annuity": amt_annuity,
            "amt_goods_price": amt_goods_price,
            "name_contract_type": name_contract_type,
            "name_income_type": name_income_type,
            "name_housing_type": name_housing_type,
            "ext_source_1": ext_source_1,
            "ext_source_2": ext_source_2,
            "ext_source_3": ext_source_3,
            "region_population_relative": region_population_relative,
            "region_rating_client": region_rating_client,
            "region_rating_client_w_city": region_rating_client,
            "organization_type": organization_type,
            "occupation_type": "Laborers",
            "additional_features": {}
        }

        # Make prediction
        with st.spinner("🔄 Analyzing credit risk..."):
            result = api_client.predict_single(application_data)

        if "error" not in result:
            show_prediction_results(result)
        else:
            st.error(f"Prediction failed: {result['error']}")


def show_prediction_results(result: Dict[str, Any]):
    """Display prediction results"""

    st.markdown("---")
    st.subheader("📊 Risk Assessment Results")

    # Main Result
    col1, col2 = st.columns([2, 1])

    with col1:
        # Decision
        if result["risk_prediction"] == "APPROVED":
            st.markdown(f"""
            <div class="risk-approved">
                ✅ APPLICATION APPROVED
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="risk-rejected">
                ❌ APPLICATION REJECTED
            </div>
            """, unsafe_allow_html=True)

        # Key Metrics
        col_a, col_b, col_c = st.columns(3)

        with col_a:
            st.metric(
                "Default Probability",
                f"{result['default_probability']:.2%}",
                delta=f"Threshold: {result['optimal_threshold']:.2%}"
            )

        with col_b:
            st.metric(
                "Confidence Score",
                f"{result['confidence_score']:.2%}",
                delta="Model certainty"
            )

        with col_c:
            risk_color = get_risk_color(result['risk_category'])
            st.markdown(f"""
            <div style="background-color: {risk_color}; color: white; padding: 1rem; border-radius: 0.5rem; text-align: center;">
                <strong>Risk Level</strong><br>
                {result['risk_category']}
            </div>
            """, unsafe_allow_html=True)

    with col2:
        # Gauge Chart
        fig = create_gauge_chart(
            result['default_probability'],
            "Default Probability"
        )
        st.plotly_chart(fig, use_container_width=True)

    # Top Risk Factors
    st.subheader("🎯 Top Risk Factors")

    for i, factor in enumerate(result['top_risk_factors'][:5]):
        col_feature, col_importance, col_value = st.columns([2, 1, 1])

        with col_feature:
            st.markdown(
                f"**{i+1}. {factor['feature'].replace('_', ' ').title()}**")

        with col_importance:
            st.metric("Importance", f"{factor['importance']:.4f}")

        with col_value:
            st.metric("Value", f"{factor['value']}")

    # Technical Details
    with st.expander("🔧 Technical Details"):
        col_tech1, col_tech2 = st.columns(2)

        with col_tech1:
            st.markdown(f"""
            **Model Information:**
            - Version: {result['model_version']}
            - F1-Score: {result['f1_score']:.4f}
            - AUC Score: {result['auc_score']:.4f}
            """)

        with col_tech2:
            st.markdown(f"""
            **Prediction Details:**
            - ID: {result['prediction_id']}
            - Processing Time: {result['processing_time_ms']:.1f}ms
            - Timestamp: {result['timestamp']}
            """)


def show_batch_processing():
    """Show batch processing interface"""

    st.subheader("📊 Batch Credit Risk Processing")

    # File Upload
    st.markdown("### 📁 Upload Applications")

    uploaded_file = st.file_uploader(
        "Choose CSV file",
        type=['csv'],
        help="Upload a CSV file with credit applications. Maximum 100 applications per batch."
    )

    # Sample Data Template
    with st.expander("📋 Download Sample Template"):
        sample_data = {
            'days_birth': [-12000, -15000, -18000],
            'days_employed': [-2000, -3000, -1500],
            'amt_income_total': [150000, 200000, 100000],
            'code_gender': ['M', 'F', 'M'],
            'cnt_children': [0, 2, 1],
            'amt_credit': [500000, 750000, 300000],
            'amt_annuity': [25000, 35000, 18000],
            'amt_goods_price': [450000, 700000, 280000],
            'ext_source_2': [0.6, 0.8, 0.4],
            'ext_source_3': [0.5, 0.7, 0.3]
        }

        sample_df = pd.DataFrame(sample_data)
        st.dataframe(sample_df)

        csv = sample_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Sample Template",
            data=csv,
            file_name="home_credit_template.csv",
            mime="text/csv"
        )

    if uploaded_file is not None:
        try:
            # Read uploaded file
            df = pd.read_csv(uploaded_file)

            st.success(
                f"✅ File uploaded successfully! Found {len(df)} applications.")

            # Show preview
            st.markdown("### 👀 Data Preview")
            st.dataframe(df.head(10))

            # Validation
            required_columns = ['days_birth',
                                'amt_income_total', 'amt_credit', 'code_gender']
            missing_columns = [
                col for col in required_columns if col not in df.columns]

            if missing_columns:
                st.error(f"❌ Missing required columns: {missing_columns}")
                return

            # Process button
            if st.button("🚀 Process Batch", use_container_width=True):

                if len(df) > 100:
                    st.warning(
                        "⚠️ Maximum 100 applications per batch. Processing first 100 rows.")
                    df = df.head(100)

                # Prepare batch data (simplified for demo)
                applications = []
                for _, row in df.iterrows():
                    app_data = {
                        "days_birth": int(row.get('days_birth', -12000)),
                        "days_employed": int(row.get('days_employed', -2000)),
                        "amt_income_total": float(row.get('amt_income_total', 150000)),
                        "code_gender": str(row.get('code_gender', 'M')),
                        "name_family_status": str(row.get('name_family_status', 'Married')),
                        "cnt_children": int(row.get('cnt_children', 0)),
                        "name_education_type": str(row.get('name_education_type', 'Secondary / secondary special')),
                        "amt_credit": float(row.get('amt_credit', 500000)),
                        "amt_annuity": float(row.get('amt_annuity', 25000)),
                        "amt_goods_price": float(row.get('amt_goods_price', 450000)),
                        "name_contract_type": str(row.get('name_contract_type', 'Cash loans')),
                        "name_income_type": str(row.get('name_income_type', 'Working')),
                        "name_housing_type": str(row.get('name_housing_type', 'House / apartment')),
                        "ext_source_1": float(row.get('ext_source_1', 0.5)),
                        "ext_source_2": float(row.get('ext_source_2', 0.6)),
                        "ext_source_3": float(row.get('ext_source_3', 0.5)),
                        "region_population_relative": float(row.get('region_population_relative', 0.5)),
                        "region_rating_client": int(row.get('region_rating_client', 2)),
                        "region_rating_client_w_city": int(row.get('region_rating_client_w_city', 2)),
                        "organization_type": str(row.get('organization_type', 'Business Entity Type 3')),
                        "occupation_type": str(row.get('occupation_type', 'Laborers')),
                        "additional_features": {}
                    }
                    applications.append(app_data)

                # Make batch prediction
                with st.spinner(f"🔄 Processing {len(applications)} applications..."):
                    result = api_client.predict_batch(applications)

                if "error" not in result:
                    show_batch_results(result, df)
                else:
                    st.error(f"Batch processing failed: {result['error']}")

        except Exception as e:
            st.error(f"Error reading file: {e}")


def show_batch_results(result: Dict[str, Any], original_df: pd.DataFrame):
    """Display batch processing results"""

    st.markdown("---")
    st.subheader("📊 Batch Processing Results")

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Processed", result['total_processed'])

    with col2:
        st.metric("Successful", result['success_count'])

    with col3:
        st.metric("Errors", result['error_count'])

    with col4:
        st.metric("Processing Time", f"{result['processing_time_ms']:.0f}ms")

    if result['predictions']:
        # Create results DataFrame
        results_data = []
        for i, pred in enumerate(result['predictions']):
            results_data.append({
                'Application_ID': i + 1,
                'Default_Probability': pred['default_probability'],
                'Risk_Prediction': pred['risk_prediction'],
                'Risk_Category': pred['risk_category'],
                'Confidence_Score': pred['confidence_score']
            })

        results_df = pd.DataFrame(results_data)

        # Summary statistics
        st.markdown("### 📈 Summary Statistics")

        col_stats1, col_stats2 = st.columns(2)

        with col_stats1:
            approved_count = len(
                results_df[results_df['Risk_Prediction'] == 'APPROVED'])
            rejected_count = len(
                results_df[results_df['Risk_Prediction'] == 'REJECTED'])

            st.metric("Approved", approved_count,
                      delta=f"{approved_count/len(results_df):.1%}")
            st.metric("Rejected", rejected_count,
                      delta=f"{rejected_count/len(results_df):.1%}")

        with col_stats2:
            avg_probability = results_df['Default_Probability'].mean()
            avg_confidence = results_df['Confidence_Score'].mean()

            st.metric("Avg Default Probability", f"{avg_probability:.2%}")
            st.metric("Avg Confidence", f"{avg_confidence:.2%}")

        # Visualizations
        col_viz1, col_viz2 = st.columns(2)

        with col_viz1:
            # Risk distribution
            fig_risk = px.histogram(
                results_df,
                x='Risk_Category',
                title="Risk Category Distribution",
                color='Risk_Category'
            )
            st.plotly_chart(fig_risk, use_container_width=True)

        with col_viz2:
            # Probability distribution
            fig_prob = px.histogram(
                results_df,
                x='Default_Probability',
                title="Default Probability Distribution",
                nbins=20
            )
            fig_prob.add_vline(x=Config.OPTIMAL_THRESHOLD,
                               line_dash="dash", line_color="red")
            st.plotly_chart(fig_prob, use_container_width=True)

        # Results Table
        st.markdown("### 📋 Detailed Results")
        st.dataframe(results_df, use_container_width=True)

        # Download Results
        csv_results = results_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Results (CSV)",
            data=csv_results,
            file_name=f"credit_risk_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv",
            use_container_width=True
        )


def show_analytics():
    """Show analytics and monitoring dashboard"""

    st.subheader("📈 Analytics & Performance Monitoring")

    # Model Performance Section
    st.markdown("### 🎯 Model Performance Metrics")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="metric-container">
            <h4>F1-Score Performance</h4>
            <h2 style="color: #28a745;">0.8876</h2>
            <p>✅ Exceeds target of 0.88</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="metric-container">
            <h4>AUC Score</h4>
            <h2 style="color: #1f77b4;">0.7713</h2>
            <p>📊 Strong discriminative power</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="metric-container">
            <h4>Default Rate</h4>
            <h2 style="color: #ffc107;">8.4%</h2>
            <p>🎯 Target: 8.1% (±0.5%)</p>
        </div>
        """, unsafe_allow_html=True)

    # Feature Importance
    st.markdown("### 🎯 Top Feature Importance")

    # Sample feature importance data
    feature_data = {
        'Feature': ['EXT_SOURCE_2', 'DAYS_BIRTH', 'EXT_SOURCE_3', 'DAYS_EMPLOYED', 'AMT_CREDIT_SUM_DEBT'],
        'Importance': [0.0847, 0.0634, 0.0521, 0.0445, 0.0398],
        'Category': ['External Score', 'Personal Info', 'External Score', 'Employment', 'Credit History']
    }

    feature_df = pd.DataFrame(feature_data)

    fig_features = px.bar(
        feature_df,
        x='Importance',
        y='Feature',
        color='Category',
        orientation='h',
        title="Top 5 Most Important Features"
    )
    fig_features.update_layout(height=400)
    st.plotly_chart(fig_features, use_container_width=True)


def show_api_status():
    """Show API status and diagnostics"""

    st.subheader("🔧 API Status & Diagnostics")

    # Health Check
    with st.spinner("Checking API health..."):
        health = api_client.health_check()

    if health.get("status") == "healthy":
        st.success("✅ API Service: Healthy")

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Status", "Healthy ✅")
            st.metric("Model Loaded", "Yes ✅" if health.get(
                "model_loaded") else "No ❌")

        with col2:
            st.metric("Version", health.get("version", "Unknown"))
            if "uptime_seconds" in health:
                uptime_hours = health["uptime_seconds"] / 3600
                st.metric("Uptime", f"{uptime_hours:.1f} hours")

    else:
        st.error(f"❌ API Service: {health.get('message', 'Unhealthy')}")

    # Model Information
    st.markdown("### 🤖 Model Information")

    model_info = api_client.get_model_info()

    if "error" not in model_info:
        st.json(model_info)
    else:
        st.error(f"Unable to retrieve model info: {model_info.get('error')}")

    # API Endpoints
    st.markdown("### 🔗 Available Endpoints")

    endpoints = [
        {"Method": "GET", "Endpoint": "/health", "Description": "Health check"},
        {"Method": "GET", "Endpoint": "/model/info",
            "Description": "Model information"},
        {"Method": "POST", "Endpoint": "/predict",
            "Description": "Single prediction"},
        {"Method": "POST", "Endpoint": "/predict/batch",
            "Description": "Batch predictions"},
        {"Method": "GET", "Endpoint": "/docs", "Description": "API documentation"}
    ]

    endpoints_df = pd.DataFrame(endpoints)
    st.dataframe(endpoints_df, use_container_width=True)

    # Test Connection
    if st.button("🔄 Test API Connection"):
        with st.spinner("Testing connection..."):
            start_time = time.time()
            response = api_client.health_check()
            response_time = (time.time() - start_time) * 1000

            if response.get("status") == "healthy":
                st.success(
                    f"✅ Connection successful! Response time: {response_time:.1f}ms")
            else:
                st.error(f"❌ Connection failed: {response.get('message')}")

# =============================================================================
# 7. RUN APPLICATION
# =============================================================================


if __name__ == "__main__":
    main()
