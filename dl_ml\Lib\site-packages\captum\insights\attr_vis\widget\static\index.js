define(["@jupyter-widgets/base","moment"],(function(e,t){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=47)}([function(e,t,n){"use strict";e.exports=n(48)},function(e,t,n){var r=n(52);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(26)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){var r=n(31),i="object"==typeof self&&self&&self.Object===Object&&self,a=r||i||Function("return this")();e.exports=a},function(e,t){var n=Array.isArray;e.exports=n},function(e,t,n){var r=n(74),i=n(79);e.exports=function(e,t){var n=i(e,t);return r(n)?n:void 0}},function(e,t,n){var r=n(9),i=n(75),a=n(76),o=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?i(e):a(e)}},function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},function(e,t,n){var r=n(64),i=n(65),a=n(66),o=n(67),l=n(68);function u(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}u.prototype.clear=r,u.prototype.delete=i,u.prototype.get=a,u.prototype.has=o,u.prototype.set=l,e.exports=u},function(e,t,n){var r=n(29);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return-1}},function(e,t,n){var r=n(2).Symbol;e.exports=r},function(e,t,n){var r=n(4)(Object,"create");e.exports=r},function(e,t,n){var r=n(88);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},function(e,t,n){var r=n(23);e.exports=function(e){if("string"==typeof e||r(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},function(t,n){t.exports=e},function(e,t,n){(function(e,r){var i;
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */(function(){var a="Expected a function",o="__lodash_placeholder__",l=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]],u="[object Arguments]",s="[object Array]",c="[object Boolean]",f="[object Date]",d="[object Error]",h="[object Function]",p="[object GeneratorFunction]",g="[object Map]",v="[object Number]",m="[object Object]",b="[object RegExp]",y="[object Set]",x="[object String]",_="[object Symbol]",w="[object WeakMap]",k="[object ArrayBuffer]",S="[object DataView]",T="[object Float32Array]",E="[object Float64Array]",C="[object Int8Array]",P="[object Int16Array]",M="[object Int32Array]",O="[object Uint8Array]",I="[object Uint16Array]",A="[object Uint32Array]",D=/\b__p \+= '';/g,N=/\b(__p \+=) '' \+/g,z=/(__e\(.*?\)|\b__t\)) \+\n'';/g,j=/&(?:amp|lt|gt|quot|#39);/g,R=/[&<>"']/g,F=RegExp(j.source),L=RegExp(R.source),B=/<%-([\s\S]+?)%>/g,W=/<%([\s\S]+?)%>/g,V=/<%=([\s\S]+?)%>/g,U=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,H=/^\w*$/,q=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,$=/[\\^$.*+?()[\]{}|]/g,Q=RegExp($.source),Y=/^\s+|\s+$/g,K=/^\s+/,G=/\s+$/,Z=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,X=/\{\n\/\* \[wrapped with (.+)\] \*/,J=/,? & /,ee=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,te=/\\(\\)?/g,ne=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,re=/\w*$/,ie=/^[-+]0x[0-9a-f]+$/i,ae=/^0b[01]+$/i,oe=/^\[object .+?Constructor\]$/,le=/^0o[0-7]+$/i,ue=/^(?:0|[1-9]\d*)$/,se=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ce=/($^)/,fe=/['\n\r\u2028\u2029\\]/g,de="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",he="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",pe="[\\ud800-\\udfff]",ge="["+he+"]",ve="["+de+"]",me="\\d+",be="[\\u2700-\\u27bf]",ye="[a-z\\xdf-\\xf6\\xf8-\\xff]",xe="[^\\ud800-\\udfff"+he+me+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",_e="\\ud83c[\\udffb-\\udfff]",we="[^\\ud800-\\udfff]",ke="(?:\\ud83c[\\udde6-\\uddff]){2}",Se="[\\ud800-\\udbff][\\udc00-\\udfff]",Te="[A-Z\\xc0-\\xd6\\xd8-\\xde]",Ee="(?:"+ye+"|"+xe+")",Ce="(?:"+Te+"|"+xe+")",Pe="(?:"+ve+"|"+_e+")"+"?",Me="[\\ufe0e\\ufe0f]?"+Pe+("(?:\\u200d(?:"+[we,ke,Se].join("|")+")[\\ufe0e\\ufe0f]?"+Pe+")*"),Oe="(?:"+[be,ke,Se].join("|")+")"+Me,Ie="(?:"+[we+ve+"?",ve,ke,Se,pe].join("|")+")",Ae=RegExp("['’]","g"),De=RegExp(ve,"g"),Ne=RegExp(_e+"(?="+_e+")|"+Ie+Me,"g"),ze=RegExp([Te+"?"+ye+"+(?:['’](?:d|ll|m|re|s|t|ve))?(?="+[ge,Te,"$"].join("|")+")",Ce+"+(?:['’](?:D|LL|M|RE|S|T|VE))?(?="+[ge,Te+Ee,"$"].join("|")+")",Te+"?"+Ee+"+(?:['’](?:d|ll|m|re|s|t|ve))?",Te+"+(?:['’](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",me,Oe].join("|"),"g"),je=RegExp("[\\u200d\\ud800-\\udfff"+de+"\\ufe0e\\ufe0f]"),Re=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Fe=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Le=-1,Be={};Be[T]=Be[E]=Be[C]=Be[P]=Be[M]=Be[O]=Be["[object Uint8ClampedArray]"]=Be[I]=Be[A]=!0,Be[u]=Be[s]=Be[k]=Be[c]=Be[S]=Be[f]=Be[d]=Be[h]=Be[g]=Be[v]=Be[m]=Be[b]=Be[y]=Be[x]=Be[w]=!1;var We={};We[u]=We[s]=We[k]=We[S]=We[c]=We[f]=We[T]=We[E]=We[C]=We[P]=We[M]=We[g]=We[v]=We[m]=We[b]=We[y]=We[x]=We[_]=We[O]=We["[object Uint8ClampedArray]"]=We[I]=We[A]=!0,We[d]=We[h]=We[w]=!1;var Ve={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Ue=parseFloat,He=parseInt,qe="object"==typeof e&&e&&e.Object===Object&&e,$e="object"==typeof self&&self&&self.Object===Object&&self,Qe=qe||$e||Function("return this")(),Ye=t&&!t.nodeType&&t,Ke=Ye&&"object"==typeof r&&r&&!r.nodeType&&r,Ge=Ke&&Ke.exports===Ye,Ze=Ge&&qe.process,Xe=function(){try{var e=Ke&&Ke.require&&Ke.require("util").types;return e||Ze&&Ze.binding&&Ze.binding("util")}catch(e){}}(),Je=Xe&&Xe.isArrayBuffer,et=Xe&&Xe.isDate,tt=Xe&&Xe.isMap,nt=Xe&&Xe.isRegExp,rt=Xe&&Xe.isSet,it=Xe&&Xe.isTypedArray;function at(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function ot(e,t,n,r){for(var i=-1,a=null==e?0:e.length;++i<a;){var o=e[i];t(r,o,n(o),e)}return r}function lt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function ut(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function st(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function ct(e,t){for(var n=-1,r=null==e?0:e.length,i=0,a=[];++n<r;){var o=e[n];t(o,n,e)&&(a[i++]=o)}return a}function ft(e,t){return!!(null==e?0:e.length)&&_t(e,t,0)>-1}function dt(e,t,n){for(var r=-1,i=null==e?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1}function ht(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}function pt(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}function gt(e,t,n,r){var i=-1,a=null==e?0:e.length;for(r&&a&&(n=e[++i]);++i<a;)n=t(n,e[i],i,e);return n}function vt(e,t,n,r){var i=null==e?0:e.length;for(r&&i&&(n=e[--i]);i--;)n=t(n,e[i],i,e);return n}function mt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var bt=Tt("length");function yt(e,t,n){var r;return n(e,(function(e,n,i){if(t(e,n,i))return r=n,!1})),r}function xt(e,t,n,r){for(var i=e.length,a=n+(r?1:-1);r?a--:++a<i;)if(t(e[a],a,e))return a;return-1}function _t(e,t,n){return t==t?function(e,t,n){var r=n-1,i=e.length;for(;++r<i;)if(e[r]===t)return r;return-1}(e,t,n):xt(e,kt,n)}function wt(e,t,n,r){for(var i=n-1,a=e.length;++i<a;)if(r(e[i],t))return i;return-1}function kt(e){return e!=e}function St(e,t){var n=null==e?0:e.length;return n?Pt(e,t)/n:NaN}function Tt(e){return function(t){return null==t?void 0:t[e]}}function Et(e){return function(t){return null==e?void 0:e[t]}}function Ct(e,t,n,r,i){return i(e,(function(e,i,a){n=r?(r=!1,e):t(n,e,i,a)})),n}function Pt(e,t){for(var n,r=-1,i=e.length;++r<i;){var a=t(e[r]);void 0!==a&&(n=void 0===n?a:n+a)}return n}function Mt(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function Ot(e){return function(t){return e(t)}}function It(e,t){return ht(t,(function(t){return e[t]}))}function At(e,t){return e.has(t)}function Dt(e,t){for(var n=-1,r=e.length;++n<r&&_t(t,e[n],0)>-1;);return n}function Nt(e,t){for(var n=e.length;n--&&_t(t,e[n],0)>-1;);return n}function zt(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}var jt=Et({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),Rt=Et({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function Ft(e){return"\\"+Ve[e]}function Lt(e){return je.test(e)}function Bt(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function Wt(e,t){return function(n){return e(t(n))}}function Vt(e,t){for(var n=-1,r=e.length,i=0,a=[];++n<r;){var l=e[n];l!==t&&l!==o||(e[n]=o,a[i++]=n)}return a}function Ut(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function Ht(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}function qt(e){return Lt(e)?function(e){var t=Ne.lastIndex=0;for(;Ne.test(e);)++t;return t}(e):bt(e)}function $t(e){return Lt(e)?function(e){return e.match(Ne)||[]}(e):function(e){return e.split("")}(e)}var Qt=Et({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var Yt=function e(t){var n,r=(t=null==t?Qe:Yt.defaults(Qe.Object(),t,Yt.pick(Qe,Fe))).Array,i=t.Date,de=t.Error,he=t.Function,pe=t.Math,ge=t.Object,ve=t.RegExp,me=t.String,be=t.TypeError,ye=r.prototype,xe=he.prototype,_e=ge.prototype,we=t["__core-js_shared__"],ke=xe.toString,Se=_e.hasOwnProperty,Te=0,Ee=(n=/[^.]+$/.exec(we&&we.keys&&we.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",Ce=_e.toString,Pe=ke.call(ge),Me=Qe._,Oe=ve("^"+ke.call(Se).replace($,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ie=Ge?t.Buffer:void 0,Ne=t.Symbol,je=t.Uint8Array,Ve=Ie?Ie.allocUnsafe:void 0,qe=Wt(ge.getPrototypeOf,ge),$e=ge.create,Ye=_e.propertyIsEnumerable,Ke=ye.splice,Ze=Ne?Ne.isConcatSpreadable:void 0,Xe=Ne?Ne.iterator:void 0,bt=Ne?Ne.toStringTag:void 0,Et=function(){try{var e=Ji(ge,"defineProperty");return e({},"",{}),e}catch(e){}}(),Kt=t.clearTimeout!==Qe.clearTimeout&&t.clearTimeout,Gt=i&&i.now!==Qe.Date.now&&i.now,Zt=t.setTimeout!==Qe.setTimeout&&t.setTimeout,Xt=pe.ceil,Jt=pe.floor,en=ge.getOwnPropertySymbols,tn=Ie?Ie.isBuffer:void 0,nn=t.isFinite,rn=ye.join,an=Wt(ge.keys,ge),on=pe.max,ln=pe.min,un=i.now,sn=t.parseInt,cn=pe.random,fn=ye.reverse,dn=Ji(t,"DataView"),hn=Ji(t,"Map"),pn=Ji(t,"Promise"),gn=Ji(t,"Set"),vn=Ji(t,"WeakMap"),mn=Ji(ge,"create"),bn=vn&&new vn,yn={},xn=Ea(dn),_n=Ea(hn),wn=Ea(pn),kn=Ea(gn),Sn=Ea(vn),Tn=Ne?Ne.prototype:void 0,En=Tn?Tn.valueOf:void 0,Cn=Tn?Tn.toString:void 0;function Pn(e){if(Ho(e)&&!Do(e)&&!(e instanceof An)){if(e instanceof In)return e;if(Se.call(e,"__wrapped__"))return Ca(e)}return new In(e)}var Mn=function(){function e(){}return function(t){if(!Uo(t))return{};if($e)return $e(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();function On(){}function In(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=void 0}function An(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=4294967295,this.__views__=[]}function Dn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Nn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function zn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function jn(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new zn;++t<n;)this.add(e[t])}function Rn(e){var t=this.__data__=new Nn(e);this.size=t.size}function Fn(e,t){var n=Do(e),r=!n&&Ao(e),i=!n&&!r&&Ro(e),a=!n&&!r&&!i&&Xo(e),o=n||r||i||a,l=o?Mt(e.length,me):[],u=l.length;for(var s in e)!t&&!Se.call(e,s)||o&&("length"==s||i&&("offset"==s||"parent"==s)||a&&("buffer"==s||"byteLength"==s||"byteOffset"==s)||oa(s,u))||l.push(s);return l}function Ln(e){var t=e.length;return t?e[jr(0,t-1)]:void 0}function Bn(e,t){return ka(mi(e),Kn(t,0,e.length))}function Wn(e){return ka(mi(e))}function Vn(e,t,n){(void 0!==n&&!Mo(e[t],n)||void 0===n&&!(t in e))&&Qn(e,t,n)}function Un(e,t,n){var r=e[t];Se.call(e,t)&&Mo(r,n)&&(void 0!==n||t in e)||Qn(e,t,n)}function Hn(e,t){for(var n=e.length;n--;)if(Mo(e[n][0],t))return n;return-1}function qn(e,t,n,r){return er(e,(function(e,i,a){t(r,e,n(e),a)})),r}function $n(e,t){return e&&bi(t,xl(t),e)}function Qn(e,t,n){"__proto__"==t&&Et?Et(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function Yn(e,t){for(var n=-1,i=t.length,a=r(i),o=null==e;++n<i;)a[n]=o?void 0:gl(e,t[n]);return a}function Kn(e,t,n){return e==e&&(void 0!==n&&(e=e<=n?e:n),void 0!==t&&(e=e>=t?e:t)),e}function Gn(e,t,n,r,i,a){var o,l=1&t,s=2&t,d=4&t;if(n&&(o=i?n(e,r,i,a):n(e)),void 0!==o)return o;if(!Uo(e))return e;var w=Do(e);if(w){if(o=function(e){var t=e.length,n=new e.constructor(t);t&&"string"==typeof e[0]&&Se.call(e,"index")&&(n.index=e.index,n.input=e.input);return n}(e),!l)return mi(e,o)}else{var D=na(e),N=D==h||D==p;if(Ro(e))return fi(e,l);if(D==m||D==u||N&&!i){if(o=s||N?{}:ia(e),!l)return s?function(e,t){return bi(e,ta(e),t)}(e,function(e,t){return e&&bi(t,_l(t),e)}(o,e)):function(e,t){return bi(e,ea(e),t)}(e,$n(o,e))}else{if(!We[D])return i?e:{};o=function(e,t,n){var r=e.constructor;switch(t){case k:return di(e);case c:case f:return new r(+e);case S:return function(e,t){var n=t?di(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case T:case E:case C:case P:case M:case O:case"[object Uint8ClampedArray]":case I:case A:return hi(e,n);case g:return new r;case v:case x:return new r(e);case b:return function(e){var t=new e.constructor(e.source,re.exec(e));return t.lastIndex=e.lastIndex,t}(e);case y:return new r;case _:return i=e,En?ge(En.call(i)):{}}var i}(e,D,l)}}a||(a=new Rn);var z=a.get(e);if(z)return z;a.set(e,o),Ko(e)?e.forEach((function(r){o.add(Gn(r,t,n,r,e,a))})):qo(e)&&e.forEach((function(r,i){o.set(i,Gn(r,t,n,i,e,a))}));var j=w?void 0:(d?s?$i:qi:s?_l:xl)(e);return lt(j||e,(function(r,i){j&&(r=e[i=r]),Un(o,i,Gn(r,t,n,i,e,a))})),o}function Zn(e,t,n){var r=n.length;if(null==e)return!r;for(e=ge(e);r--;){var i=n[r],a=t[i],o=e[i];if(void 0===o&&!(i in e)||!a(o))return!1}return!0}function Xn(e,t,n){if("function"!=typeof e)throw new be(a);return ya((function(){e.apply(void 0,n)}),t)}function Jn(e,t,n,r){var i=-1,a=ft,o=!0,l=e.length,u=[],s=t.length;if(!l)return u;n&&(t=ht(t,Ot(n))),r?(a=dt,o=!1):t.length>=200&&(a=At,o=!1,t=new jn(t));e:for(;++i<l;){var c=e[i],f=null==n?c:n(c);if(c=r||0!==c?c:0,o&&f==f){for(var d=s;d--;)if(t[d]===f)continue e;u.push(c)}else a(t,f,r)||u.push(c)}return u}Pn.templateSettings={escape:B,evaluate:W,interpolate:V,variable:"",imports:{_:Pn}},Pn.prototype=On.prototype,Pn.prototype.constructor=Pn,In.prototype=Mn(On.prototype),In.prototype.constructor=In,An.prototype=Mn(On.prototype),An.prototype.constructor=An,Dn.prototype.clear=function(){this.__data__=mn?mn(null):{},this.size=0},Dn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Dn.prototype.get=function(e){var t=this.__data__;if(mn){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Se.call(t,e)?t[e]:void 0},Dn.prototype.has=function(e){var t=this.__data__;return mn?void 0!==t[e]:Se.call(t,e)},Dn.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=mn&&void 0===t?"__lodash_hash_undefined__":t,this},Nn.prototype.clear=function(){this.__data__=[],this.size=0},Nn.prototype.delete=function(e){var t=this.__data__,n=Hn(t,e);return!(n<0)&&(n==t.length-1?t.pop():Ke.call(t,n,1),--this.size,!0)},Nn.prototype.get=function(e){var t=this.__data__,n=Hn(t,e);return n<0?void 0:t[n][1]},Nn.prototype.has=function(e){return Hn(this.__data__,e)>-1},Nn.prototype.set=function(e,t){var n=this.__data__,r=Hn(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},zn.prototype.clear=function(){this.size=0,this.__data__={hash:new Dn,map:new(hn||Nn),string:new Dn}},zn.prototype.delete=function(e){var t=Zi(this,e).delete(e);return this.size-=t?1:0,t},zn.prototype.get=function(e){return Zi(this,e).get(e)},zn.prototype.has=function(e){return Zi(this,e).has(e)},zn.prototype.set=function(e,t){var n=Zi(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},jn.prototype.add=jn.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},jn.prototype.has=function(e){return this.__data__.has(e)},Rn.prototype.clear=function(){this.__data__=new Nn,this.size=0},Rn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Rn.prototype.get=function(e){return this.__data__.get(e)},Rn.prototype.has=function(e){return this.__data__.has(e)},Rn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Nn){var r=n.__data__;if(!hn||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new zn(r)}return n.set(e,t),this.size=n.size,this};var er=_i(ur),tr=_i(sr,!0);function nr(e,t){var n=!0;return er(e,(function(e,r,i){return n=!!t(e,r,i)})),n}function rr(e,t,n){for(var r=-1,i=e.length;++r<i;){var a=e[r],o=t(a);if(null!=o&&(void 0===l?o==o&&!Zo(o):n(o,l)))var l=o,u=a}return u}function ir(e,t){var n=[];return er(e,(function(e,r,i){t(e,r,i)&&n.push(e)})),n}function ar(e,t,n,r,i){var a=-1,o=e.length;for(n||(n=aa),i||(i=[]);++a<o;){var l=e[a];t>0&&n(l)?t>1?ar(l,t-1,n,r,i):pt(i,l):r||(i[i.length]=l)}return i}var or=wi(),lr=wi(!0);function ur(e,t){return e&&or(e,t,xl)}function sr(e,t){return e&&lr(e,t,xl)}function cr(e,t){return ct(t,(function(t){return Bo(e[t])}))}function fr(e,t){for(var n=0,r=(t=li(t,e)).length;null!=e&&n<r;)e=e[Ta(t[n++])];return n&&n==r?e:void 0}function dr(e,t,n){var r=t(e);return Do(e)?r:pt(r,n(e))}function hr(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":bt&&bt in ge(e)?function(e){var t=Se.call(e,bt),n=e[bt];try{e[bt]=void 0;var r=!0}catch(e){}var i=Ce.call(e);r&&(t?e[bt]=n:delete e[bt]);return i}(e):function(e){return Ce.call(e)}(e)}function pr(e,t){return e>t}function gr(e,t){return null!=e&&Se.call(e,t)}function vr(e,t){return null!=e&&t in ge(e)}function mr(e,t,n){for(var i=n?dt:ft,a=e[0].length,o=e.length,l=o,u=r(o),s=1/0,c=[];l--;){var f=e[l];l&&t&&(f=ht(f,Ot(t))),s=ln(f.length,s),u[l]=!n&&(t||a>=120&&f.length>=120)?new jn(l&&f):void 0}f=e[0];var d=-1,h=u[0];e:for(;++d<a&&c.length<s;){var p=f[d],g=t?t(p):p;if(p=n||0!==p?p:0,!(h?At(h,g):i(c,g,n))){for(l=o;--l;){var v=u[l];if(!(v?At(v,g):i(e[l],g,n)))continue e}h&&h.push(g),c.push(p)}}return c}function br(e,t,n){var r=null==(e=ga(e,t=li(t,e)))?e:e[Ta(Fa(t))];return null==r?void 0:at(r,e,n)}function yr(e){return Ho(e)&&hr(e)==u}function xr(e,t,n,r,i){return e===t||(null==e||null==t||!Ho(e)&&!Ho(t)?e!=e&&t!=t:function(e,t,n,r,i,a){var o=Do(e),l=Do(t),h=o?s:na(e),p=l?s:na(t),w=(h=h==u?m:h)==m,T=(p=p==u?m:p)==m,E=h==p;if(E&&Ro(e)){if(!Ro(t))return!1;o=!0,w=!1}if(E&&!w)return a||(a=new Rn),o||Xo(e)?Ui(e,t,n,r,i,a):function(e,t,n,r,i,a,o){switch(n){case S:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case k:return!(e.byteLength!=t.byteLength||!a(new je(e),new je(t)));case c:case f:case v:return Mo(+e,+t);case d:return e.name==t.name&&e.message==t.message;case b:case x:return e==t+"";case g:var l=Bt;case y:var u=1&r;if(l||(l=Ut),e.size!=t.size&&!u)return!1;var s=o.get(e);if(s)return s==t;r|=2,o.set(e,t);var h=Ui(l(e),l(t),r,i,a,o);return o.delete(e),h;case _:if(En)return En.call(e)==En.call(t)}return!1}(e,t,h,n,r,i,a);if(!(1&n)){var C=w&&Se.call(e,"__wrapped__"),P=T&&Se.call(t,"__wrapped__");if(C||P){var M=C?e.value():e,O=P?t.value():t;return a||(a=new Rn),i(M,O,n,r,a)}}if(!E)return!1;return a||(a=new Rn),function(e,t,n,r,i,a){var o=1&n,l=qi(e),u=l.length,s=qi(t).length;if(u!=s&&!o)return!1;var c=u;for(;c--;){var f=l[c];if(!(o?f in t:Se.call(t,f)))return!1}var d=a.get(e);if(d&&a.get(t))return d==t;var h=!0;a.set(e,t),a.set(t,e);var p=o;for(;++c<u;){f=l[c];var g=e[f],v=t[f];if(r)var m=o?r(v,g,f,t,e,a):r(g,v,f,e,t,a);if(!(void 0===m?g===v||i(g,v,n,r,a):m)){h=!1;break}p||(p="constructor"==f)}if(h&&!p){var b=e.constructor,y=t.constructor;b==y||!("constructor"in e)||!("constructor"in t)||"function"==typeof b&&b instanceof b&&"function"==typeof y&&y instanceof y||(h=!1)}return a.delete(e),a.delete(t),h}(e,t,n,r,i,a)}(e,t,n,r,xr,i))}function _r(e,t,n,r){var i=n.length,a=i,o=!r;if(null==e)return!a;for(e=ge(e);i--;){var l=n[i];if(o&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++i<a;){var u=(l=n[i])[0],s=e[u],c=l[1];if(o&&l[2]){if(void 0===s&&!(u in e))return!1}else{var f=new Rn;if(r)var d=r(s,c,u,e,t,f);if(!(void 0===d?xr(c,s,3,r,f):d))return!1}}return!0}function wr(e){return!(!Uo(e)||(t=e,Ee&&Ee in t))&&(Bo(e)?Oe:oe).test(Ea(e));var t}function kr(e){return"function"==typeof e?e:null==e?$l:"object"==typeof e?Do(e)?Mr(e[0],e[1]):Pr(e):tu(e)}function Sr(e){if(!fa(e))return an(e);var t=[];for(var n in ge(e))Se.call(e,n)&&"constructor"!=n&&t.push(n);return t}function Tr(e){if(!Uo(e))return function(e){var t=[];if(null!=e)for(var n in ge(e))t.push(n);return t}(e);var t=fa(e),n=[];for(var r in e)("constructor"!=r||!t&&Se.call(e,r))&&n.push(r);return n}function Er(e,t){return e<t}function Cr(e,t){var n=-1,i=zo(e)?r(e.length):[];return er(e,(function(e,r,a){i[++n]=t(e,r,a)})),i}function Pr(e){var t=Xi(e);return 1==t.length&&t[0][2]?ha(t[0][0],t[0][1]):function(n){return n===e||_r(n,e,t)}}function Mr(e,t){return ua(e)&&da(t)?ha(Ta(e),t):function(n){var r=gl(n,e);return void 0===r&&r===t?vl(n,e):xr(t,r,3)}}function Or(e,t,n,r,i){e!==t&&or(t,(function(a,o){if(i||(i=new Rn),Uo(a))!function(e,t,n,r,i,a,o){var l=ma(e,n),u=ma(t,n),s=o.get(u);if(s)return void Vn(e,n,s);var c=a?a(l,u,n+"",e,t,o):void 0,f=void 0===c;if(f){var d=Do(u),h=!d&&Ro(u),p=!d&&!h&&Xo(u);c=u,d||h||p?Do(l)?c=l:jo(l)?c=mi(l):h?(f=!1,c=fi(u,!0)):p?(f=!1,c=hi(u,!0)):c=[]:Qo(u)||Ao(u)?(c=l,Ao(l)?c=ol(l):Uo(l)&&!Bo(l)||(c=ia(u))):f=!1}f&&(o.set(u,c),i(c,u,r,a,o),o.delete(u));Vn(e,n,c)}(e,t,o,n,Or,r,i);else{var l=r?r(ma(e,o),a,o+"",e,t,i):void 0;void 0===l&&(l=a),Vn(e,o,l)}}),_l)}function Ir(e,t){var n=e.length;if(n)return oa(t+=t<0?n:0,n)?e[t]:void 0}function Ar(e,t,n){var r=-1;return t=ht(t.length?t:[$l],Ot(Gi())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Cr(e,(function(e,n,i){return{criteria:ht(t,(function(t){return t(e)})),index:++r,value:e}})),(function(e,t){return function(e,t,n){var r=-1,i=e.criteria,a=t.criteria,o=i.length,l=n.length;for(;++r<o;){var u=pi(i[r],a[r]);if(u){if(r>=l)return u;var s=n[r];return u*("desc"==s?-1:1)}}return e.index-t.index}(e,t,n)}))}function Dr(e,t,n){for(var r=-1,i=t.length,a={};++r<i;){var o=t[r],l=fr(e,o);n(l,o)&&Wr(a,li(o,e),l)}return a}function Nr(e,t,n,r){var i=r?wt:_t,a=-1,o=t.length,l=e;for(e===t&&(t=mi(t)),n&&(l=ht(e,Ot(n)));++a<o;)for(var u=0,s=t[a],c=n?n(s):s;(u=i(l,c,u,r))>-1;)l!==e&&Ke.call(l,u,1),Ke.call(e,u,1);return e}function zr(e,t){for(var n=e?t.length:0,r=n-1;n--;){var i=t[n];if(n==r||i!==a){var a=i;oa(i)?Ke.call(e,i,1):Jr(e,i)}}return e}function jr(e,t){return e+Jt(cn()*(t-e+1))}function Rr(e,t){var n="";if(!e||t<1||t>9007199254740991)return n;do{t%2&&(n+=e),(t=Jt(t/2))&&(e+=e)}while(t);return n}function Fr(e,t){return xa(pa(e,t,$l),e+"")}function Lr(e){return Ln(Ml(e))}function Br(e,t){var n=Ml(e);return ka(n,Kn(t,0,n.length))}function Wr(e,t,n,r){if(!Uo(e))return e;for(var i=-1,a=(t=li(t,e)).length,o=a-1,l=e;null!=l&&++i<a;){var u=Ta(t[i]),s=n;if(i!=o){var c=l[u];void 0===(s=r?r(c,u,l):void 0)&&(s=Uo(c)?c:oa(t[i+1])?[]:{})}Un(l,u,s),l=l[u]}return e}var Vr=bn?function(e,t){return bn.set(e,t),e}:$l,Ur=Et?function(e,t){return Et(e,"toString",{configurable:!0,enumerable:!1,value:Ul(t),writable:!0})}:$l;function Hr(e){return ka(Ml(e))}function qr(e,t,n){var i=-1,a=e.length;t<0&&(t=-t>a?0:a+t),(n=n>a?a:n)<0&&(n+=a),a=t>n?0:n-t>>>0,t>>>=0;for(var o=r(a);++i<a;)o[i]=e[i+t];return o}function $r(e,t){var n;return er(e,(function(e,r,i){return!(n=t(e,r,i))})),!!n}function Qr(e,t,n){var r=0,i=null==e?r:e.length;if("number"==typeof t&&t==t&&i<=2147483647){for(;r<i;){var a=r+i>>>1,o=e[a];null!==o&&!Zo(o)&&(n?o<=t:o<t)?r=a+1:i=a}return i}return Yr(e,t,$l,n)}function Yr(e,t,n,r){t=n(t);for(var i=0,a=null==e?0:e.length,o=t!=t,l=null===t,u=Zo(t),s=void 0===t;i<a;){var c=Jt((i+a)/2),f=n(e[c]),d=void 0!==f,h=null===f,p=f==f,g=Zo(f);if(o)var v=r||p;else v=s?p&&(r||d):l?p&&d&&(r||!h):u?p&&d&&!h&&(r||!g):!h&&!g&&(r?f<=t:f<t);v?i=c+1:a=c}return ln(a,4294967294)}function Kr(e,t){for(var n=-1,r=e.length,i=0,a=[];++n<r;){var o=e[n],l=t?t(o):o;if(!n||!Mo(l,u)){var u=l;a[i++]=0===o?0:o}}return a}function Gr(e){return"number"==typeof e?e:Zo(e)?NaN:+e}function Zr(e){if("string"==typeof e)return e;if(Do(e))return ht(e,Zr)+"";if(Zo(e))return Cn?Cn.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Xr(e,t,n){var r=-1,i=ft,a=e.length,o=!0,l=[],u=l;if(n)o=!1,i=dt;else if(a>=200){var s=t?null:Ri(e);if(s)return Ut(s);o=!1,i=At,u=new jn}else u=t?[]:l;e:for(;++r<a;){var c=e[r],f=t?t(c):c;if(c=n||0!==c?c:0,o&&f==f){for(var d=u.length;d--;)if(u[d]===f)continue e;t&&u.push(f),l.push(c)}else i(u,f,n)||(u!==l&&u.push(f),l.push(c))}return l}function Jr(e,t){return null==(e=ga(e,t=li(t,e)))||delete e[Ta(Fa(t))]}function ei(e,t,n,r){return Wr(e,t,n(fr(e,t)),r)}function ti(e,t,n,r){for(var i=e.length,a=r?i:-1;(r?a--:++a<i)&&t(e[a],a,e););return n?qr(e,r?0:a,r?a+1:i):qr(e,r?a+1:0,r?i:a)}function ni(e,t){var n=e;return n instanceof An&&(n=n.value()),gt(t,(function(e,t){return t.func.apply(t.thisArg,pt([e],t.args))}),n)}function ri(e,t,n){var i=e.length;if(i<2)return i?Xr(e[0]):[];for(var a=-1,o=r(i);++a<i;)for(var l=e[a],u=-1;++u<i;)u!=a&&(o[a]=Jn(o[a]||l,e[u],t,n));return Xr(ar(o,1),t,n)}function ii(e,t,n){for(var r=-1,i=e.length,a=t.length,o={};++r<i;){var l=r<a?t[r]:void 0;n(o,e[r],l)}return o}function ai(e){return jo(e)?e:[]}function oi(e){return"function"==typeof e?e:$l}function li(e,t){return Do(e)?e:ua(e,t)?[e]:Sa(ll(e))}var ui=Fr;function si(e,t,n){var r=e.length;return n=void 0===n?r:n,!t&&n>=r?e:qr(e,t,n)}var ci=Kt||function(e){return Qe.clearTimeout(e)};function fi(e,t){if(t)return e.slice();var n=e.length,r=Ve?Ve(n):new e.constructor(n);return e.copy(r),r}function di(e){var t=new e.constructor(e.byteLength);return new je(t).set(new je(e)),t}function hi(e,t){var n=t?di(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function pi(e,t){if(e!==t){var n=void 0!==e,r=null===e,i=e==e,a=Zo(e),o=void 0!==t,l=null===t,u=t==t,s=Zo(t);if(!l&&!s&&!a&&e>t||a&&o&&u&&!l&&!s||r&&o&&u||!n&&u||!i)return 1;if(!r&&!a&&!s&&e<t||s&&n&&i&&!r&&!a||l&&n&&i||!o&&i||!u)return-1}return 0}function gi(e,t,n,i){for(var a=-1,o=e.length,l=n.length,u=-1,s=t.length,c=on(o-l,0),f=r(s+c),d=!i;++u<s;)f[u]=t[u];for(;++a<l;)(d||a<o)&&(f[n[a]]=e[a]);for(;c--;)f[u++]=e[a++];return f}function vi(e,t,n,i){for(var a=-1,o=e.length,l=-1,u=n.length,s=-1,c=t.length,f=on(o-u,0),d=r(f+c),h=!i;++a<f;)d[a]=e[a];for(var p=a;++s<c;)d[p+s]=t[s];for(;++l<u;)(h||a<o)&&(d[p+n[l]]=e[a++]);return d}function mi(e,t){var n=-1,i=e.length;for(t||(t=r(i));++n<i;)t[n]=e[n];return t}function bi(e,t,n,r){var i=!n;n||(n={});for(var a=-1,o=t.length;++a<o;){var l=t[a],u=r?r(n[l],e[l],l,n,e):void 0;void 0===u&&(u=e[l]),i?Qn(n,l,u):Un(n,l,u)}return n}function yi(e,t){return function(n,r){var i=Do(n)?ot:qn,a=t?t():{};return i(n,e,Gi(r,2),a)}}function xi(e){return Fr((function(t,n){var r=-1,i=n.length,a=i>1?n[i-1]:void 0,o=i>2?n[2]:void 0;for(a=e.length>3&&"function"==typeof a?(i--,a):void 0,o&&la(n[0],n[1],o)&&(a=i<3?void 0:a,i=1),t=ge(t);++r<i;){var l=n[r];l&&e(t,l,r,a)}return t}))}function _i(e,t){return function(n,r){if(null==n)return n;if(!zo(n))return e(n,r);for(var i=n.length,a=t?i:-1,o=ge(n);(t?a--:++a<i)&&!1!==r(o[a],a,o););return n}}function wi(e){return function(t,n,r){for(var i=-1,a=ge(t),o=r(t),l=o.length;l--;){var u=o[e?l:++i];if(!1===n(a[u],u,a))break}return t}}function ki(e){return function(t){var n=Lt(t=ll(t))?$t(t):void 0,r=n?n[0]:t.charAt(0),i=n?si(n,1).join(""):t.slice(1);return r[e]()+i}}function Si(e){return function(t){return gt(Bl(Al(t).replace(Ae,"")),e,"")}}function Ti(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Mn(e.prototype),r=e.apply(n,t);return Uo(r)?r:n}}function Ei(e){return function(t,n,r){var i=ge(t);if(!zo(t)){var a=Gi(n,3);t=xl(t),n=function(e){return a(i[e],e,i)}}var o=e(t,n,r);return o>-1?i[a?t[o]:o]:void 0}}function Ci(e){return Hi((function(t){var n=t.length,r=n,i=In.prototype.thru;for(e&&t.reverse();r--;){var o=t[r];if("function"!=typeof o)throw new be(a);if(i&&!l&&"wrapper"==Yi(o))var l=new In([],!0)}for(r=l?r:n;++r<n;){var u=Yi(o=t[r]),s="wrapper"==u?Qi(o):void 0;l=s&&sa(s[0])&&424==s[1]&&!s[4].length&&1==s[9]?l[Yi(s[0])].apply(l,s[3]):1==o.length&&sa(o)?l[u]():l.thru(o)}return function(){var e=arguments,r=e[0];if(l&&1==e.length&&Do(r))return l.plant(r).value();for(var i=0,a=n?t[i].apply(this,e):r;++i<n;)a=t[i].call(this,a);return a}}))}function Pi(e,t,n,i,a,o,l,u,s,c){var f=128&t,d=1&t,h=2&t,p=24&t,g=512&t,v=h?void 0:Ti(e);return function m(){for(var b=arguments.length,y=r(b),x=b;x--;)y[x]=arguments[x];if(p)var _=Ki(m),w=zt(y,_);if(i&&(y=gi(y,i,a,p)),o&&(y=vi(y,o,l,p)),b-=w,p&&b<c){var k=Vt(y,_);return zi(e,t,Pi,m.placeholder,n,y,k,u,s,c-b)}var S=d?n:this,T=h?S[e]:e;return b=y.length,u?y=va(y,u):g&&b>1&&y.reverse(),f&&s<b&&(y.length=s),this&&this!==Qe&&this instanceof m&&(T=v||Ti(T)),T.apply(S,y)}}function Mi(e,t){return function(n,r){return function(e,t,n,r){return ur(e,(function(e,i,a){t(r,n(e),i,a)})),r}(n,e,t(r),{})}}function Oi(e,t){return function(n,r){var i;if(void 0===n&&void 0===r)return t;if(void 0!==n&&(i=n),void 0!==r){if(void 0===i)return r;"string"==typeof n||"string"==typeof r?(n=Zr(n),r=Zr(r)):(n=Gr(n),r=Gr(r)),i=e(n,r)}return i}}function Ii(e){return Hi((function(t){return t=ht(t,Ot(Gi())),Fr((function(n){var r=this;return e(t,(function(e){return at(e,r,n)}))}))}))}function Ai(e,t){var n=(t=void 0===t?" ":Zr(t)).length;if(n<2)return n?Rr(t,e):t;var r=Rr(t,Xt(e/qt(t)));return Lt(t)?si($t(r),0,e).join(""):r.slice(0,e)}function Di(e){return function(t,n,i){return i&&"number"!=typeof i&&la(t,n,i)&&(n=i=void 0),t=nl(t),void 0===n?(n=t,t=0):n=nl(n),function(e,t,n,i){for(var a=-1,o=on(Xt((t-e)/(n||1)),0),l=r(o);o--;)l[i?o:++a]=e,e+=n;return l}(t,n,i=void 0===i?t<n?1:-1:nl(i),e)}}function Ni(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=al(t),n=al(n)),e(t,n)}}function zi(e,t,n,r,i,a,o,l,u,s){var c=8&t;t|=c?32:64,4&(t&=~(c?64:32))||(t&=-4);var f=[e,t,i,c?a:void 0,c?o:void 0,c?void 0:a,c?void 0:o,l,u,s],d=n.apply(void 0,f);return sa(e)&&ba(d,f),d.placeholder=r,_a(d,e,t)}function ji(e){var t=pe[e];return function(e,n){if(e=al(e),(n=null==n?0:ln(rl(n),292))&&nn(e)){var r=(ll(e)+"e").split("e");return+((r=(ll(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var Ri=gn&&1/Ut(new gn([,-0]))[1]==1/0?function(e){return new gn(e)}:Zl;function Fi(e){return function(t){var n=na(t);return n==g?Bt(t):n==y?Ht(t):function(e,t){return ht(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Li(e,t,n,i,l,u,s,c){var f=2&t;if(!f&&"function"!=typeof e)throw new be(a);var d=i?i.length:0;if(d||(t&=-97,i=l=void 0),s=void 0===s?s:on(rl(s),0),c=void 0===c?c:rl(c),d-=l?l.length:0,64&t){var h=i,p=l;i=l=void 0}var g=f?void 0:Qi(e),v=[e,t,n,i,l,h,p,u,s,c];if(g&&function(e,t){var n=e[1],r=t[1],i=n|r,a=i<131,l=128==r&&8==n||128==r&&256==n&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!a&&!l)return e;1&r&&(e[2]=t[2],i|=1&n?0:4);var u=t[3];if(u){var s=e[3];e[3]=s?gi(s,u,t[4]):u,e[4]=s?Vt(e[3],o):t[4]}(u=t[5])&&(s=e[5],e[5]=s?vi(s,u,t[6]):u,e[6]=s?Vt(e[5],o):t[6]);(u=t[7])&&(e[7]=u);128&r&&(e[8]=null==e[8]?t[8]:ln(e[8],t[8]));null==e[9]&&(e[9]=t[9]);e[0]=t[0],e[1]=i}(v,g),e=v[0],t=v[1],n=v[2],i=v[3],l=v[4],!(c=v[9]=void 0===v[9]?f?0:e.length:on(v[9]-d,0))&&24&t&&(t&=-25),t&&1!=t)m=8==t||16==t?function(e,t,n){var i=Ti(e);return function a(){for(var o=arguments.length,l=r(o),u=o,s=Ki(a);u--;)l[u]=arguments[u];var c=o<3&&l[0]!==s&&l[o-1]!==s?[]:Vt(l,s);if((o-=c.length)<n)return zi(e,t,Pi,a.placeholder,void 0,l,c,void 0,void 0,n-o);var f=this&&this!==Qe&&this instanceof a?i:e;return at(f,this,l)}}(e,t,c):32!=t&&33!=t||l.length?Pi.apply(void 0,v):function(e,t,n,i){var a=1&t,o=Ti(e);return function t(){for(var l=-1,u=arguments.length,s=-1,c=i.length,f=r(c+u),d=this&&this!==Qe&&this instanceof t?o:e;++s<c;)f[s]=i[s];for(;u--;)f[s++]=arguments[++l];return at(d,a?n:this,f)}}(e,t,n,i);else var m=function(e,t,n){var r=1&t,i=Ti(e);return function t(){var a=this&&this!==Qe&&this instanceof t?i:e;return a.apply(r?n:this,arguments)}}(e,t,n);return _a((g?Vr:ba)(m,v),e,t)}function Bi(e,t,n,r){return void 0===e||Mo(e,_e[n])&&!Se.call(r,n)?t:e}function Wi(e,t,n,r,i,a){return Uo(e)&&Uo(t)&&(a.set(t,e),Or(e,t,void 0,Wi,a),a.delete(t)),e}function Vi(e){return Qo(e)?void 0:e}function Ui(e,t,n,r,i,a){var o=1&n,l=e.length,u=t.length;if(l!=u&&!(o&&u>l))return!1;var s=a.get(e);if(s&&a.get(t))return s==t;var c=-1,f=!0,d=2&n?new jn:void 0;for(a.set(e,t),a.set(t,e);++c<l;){var h=e[c],p=t[c];if(r)var g=o?r(p,h,c,t,e,a):r(h,p,c,e,t,a);if(void 0!==g){if(g)continue;f=!1;break}if(d){if(!mt(t,(function(e,t){if(!At(d,t)&&(h===e||i(h,e,n,r,a)))return d.push(t)}))){f=!1;break}}else if(h!==p&&!i(h,p,n,r,a)){f=!1;break}}return a.delete(e),a.delete(t),f}function Hi(e){return xa(pa(e,void 0,Da),e+"")}function qi(e){return dr(e,xl,ea)}function $i(e){return dr(e,_l,ta)}var Qi=bn?function(e){return bn.get(e)}:Zl;function Yi(e){for(var t=e.name+"",n=yn[t],r=Se.call(yn,t)?n.length:0;r--;){var i=n[r],a=i.func;if(null==a||a==e)return i.name}return t}function Ki(e){return(Se.call(Pn,"placeholder")?Pn:e).placeholder}function Gi(){var e=Pn.iteratee||Ql;return e=e===Ql?kr:e,arguments.length?e(arguments[0],arguments[1]):e}function Zi(e,t){var n,r,i=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:i.map}function Xi(e){for(var t=xl(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,da(i)]}return t}function Ji(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return wr(n)?n:void 0}var ea=en?function(e){return null==e?[]:(e=ge(e),ct(en(e),(function(t){return Ye.call(e,t)})))}:iu,ta=en?function(e){for(var t=[];e;)pt(t,ea(e)),e=qe(e);return t}:iu,na=hr;function ra(e,t,n){for(var r=-1,i=(t=li(t,e)).length,a=!1;++r<i;){var o=Ta(t[r]);if(!(a=null!=e&&n(e,o)))break;e=e[o]}return a||++r!=i?a:!!(i=null==e?0:e.length)&&Vo(i)&&oa(o,i)&&(Do(e)||Ao(e))}function ia(e){return"function"!=typeof e.constructor||fa(e)?{}:Mn(qe(e))}function aa(e){return Do(e)||Ao(e)||!!(Ze&&e&&e[Ze])}function oa(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&ue.test(e))&&e>-1&&e%1==0&&e<t}function la(e,t,n){if(!Uo(n))return!1;var r=typeof t;return!!("number"==r?zo(n)&&oa(t,n.length):"string"==r&&t in n)&&Mo(n[t],e)}function ua(e,t){if(Do(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Zo(e))||(H.test(e)||!U.test(e)||null!=t&&e in ge(t))}function sa(e){var t=Yi(e),n=Pn[t];if("function"!=typeof n||!(t in An.prototype))return!1;if(e===n)return!0;var r=Qi(n);return!!r&&e===r[0]}(dn&&na(new dn(new ArrayBuffer(1)))!=S||hn&&na(new hn)!=g||pn&&"[object Promise]"!=na(pn.resolve())||gn&&na(new gn)!=y||vn&&na(new vn)!=w)&&(na=function(e){var t=hr(e),n=t==m?e.constructor:void 0,r=n?Ea(n):"";if(r)switch(r){case xn:return S;case _n:return g;case wn:return"[object Promise]";case kn:return y;case Sn:return w}return t});var ca=we?Bo:au;function fa(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||_e)}function da(e){return e==e&&!Uo(e)}function ha(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in ge(n)))}}function pa(e,t,n){return t=on(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,o=on(i.length-t,0),l=r(o);++a<o;)l[a]=i[t+a];a=-1;for(var u=r(t+1);++a<t;)u[a]=i[a];return u[t]=n(l),at(e,this,u)}}function ga(e,t){return t.length<2?e:fr(e,qr(t,0,-1))}function va(e,t){for(var n=e.length,r=ln(t.length,n),i=mi(e);r--;){var a=t[r];e[r]=oa(a,n)?i[a]:void 0}return e}function ma(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var ba=wa(Vr),ya=Zt||function(e,t){return Qe.setTimeout(e,t)},xa=wa(Ur);function _a(e,t,n){var r=t+"";return xa(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(Z,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return lt(l,(function(n){var r="_."+n[0];t&n[1]&&!ft(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(X);return t?t[1].split(J):[]}(r),n)))}function wa(e){var t=0,n=0;return function(){var r=un(),i=16-(r-n);if(n=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function ka(e,t){var n=-1,r=e.length,i=r-1;for(t=void 0===t?r:t;++n<t;){var a=jr(n,i),o=e[a];e[a]=e[n],e[n]=o}return e.length=t,e}var Sa=function(e){var t=ko(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(q,(function(e,n,r,i){t.push(r?i.replace(te,"$1"):n||e)})),t}));function Ta(e){if("string"==typeof e||Zo(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Ea(e){if(null!=e){try{return ke.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ca(e){if(e instanceof An)return e.clone();var t=new In(e.__wrapped__,e.__chain__);return t.__actions__=mi(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var Pa=Fr((function(e,t){return jo(e)?Jn(e,ar(t,1,jo,!0)):[]})),Ma=Fr((function(e,t){var n=Fa(t);return jo(n)&&(n=void 0),jo(e)?Jn(e,ar(t,1,jo,!0),Gi(n,2)):[]})),Oa=Fr((function(e,t){var n=Fa(t);return jo(n)&&(n=void 0),jo(e)?Jn(e,ar(t,1,jo,!0),void 0,n):[]}));function Ia(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:rl(n);return i<0&&(i=on(r+i,0)),xt(e,Gi(t,3),i)}function Aa(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r-1;return void 0!==n&&(i=rl(n),i=n<0?on(r+i,0):ln(i,r-1)),xt(e,Gi(t,3),i,!0)}function Da(e){return(null==e?0:e.length)?ar(e,1):[]}function Na(e){return e&&e.length?e[0]:void 0}var za=Fr((function(e){var t=ht(e,ai);return t.length&&t[0]===e[0]?mr(t):[]})),ja=Fr((function(e){var t=Fa(e),n=ht(e,ai);return t===Fa(n)?t=void 0:n.pop(),n.length&&n[0]===e[0]?mr(n,Gi(t,2)):[]})),Ra=Fr((function(e){var t=Fa(e),n=ht(e,ai);return(t="function"==typeof t?t:void 0)&&n.pop(),n.length&&n[0]===e[0]?mr(n,void 0,t):[]}));function Fa(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}var La=Fr(Ba);function Ba(e,t){return e&&e.length&&t&&t.length?Nr(e,t):e}var Wa=Hi((function(e,t){var n=null==e?0:e.length,r=Yn(e,t);return zr(e,ht(t,(function(e){return oa(e,n)?+e:e})).sort(pi)),r}));function Va(e){return null==e?e:fn.call(e)}var Ua=Fr((function(e){return Xr(ar(e,1,jo,!0))})),Ha=Fr((function(e){var t=Fa(e);return jo(t)&&(t=void 0),Xr(ar(e,1,jo,!0),Gi(t,2))})),qa=Fr((function(e){var t=Fa(e);return t="function"==typeof t?t:void 0,Xr(ar(e,1,jo,!0),void 0,t)}));function $a(e){if(!e||!e.length)return[];var t=0;return e=ct(e,(function(e){if(jo(e))return t=on(e.length,t),!0})),Mt(t,(function(t){return ht(e,Tt(t))}))}function Qa(e,t){if(!e||!e.length)return[];var n=$a(e);return null==t?n:ht(n,(function(e){return at(t,void 0,e)}))}var Ya=Fr((function(e,t){return jo(e)?Jn(e,t):[]})),Ka=Fr((function(e){return ri(ct(e,jo))})),Ga=Fr((function(e){var t=Fa(e);return jo(t)&&(t=void 0),ri(ct(e,jo),Gi(t,2))})),Za=Fr((function(e){var t=Fa(e);return t="function"==typeof t?t:void 0,ri(ct(e,jo),void 0,t)})),Xa=Fr($a);var Ja=Fr((function(e){var t=e.length,n=t>1?e[t-1]:void 0;return n="function"==typeof n?(e.pop(),n):void 0,Qa(e,n)}));function eo(e){var t=Pn(e);return t.__chain__=!0,t}function to(e,t){return t(e)}var no=Hi((function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,i=function(t){return Yn(t,e)};return!(t>1||this.__actions__.length)&&r instanceof An&&oa(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:to,args:[i],thisArg:void 0}),new In(r,this.__chain__).thru((function(e){return t&&!e.length&&e.push(void 0),e}))):this.thru(i)}));var ro=yi((function(e,t,n){Se.call(e,n)?++e[n]:Qn(e,n,1)}));var io=Ei(Ia),ao=Ei(Aa);function oo(e,t){return(Do(e)?lt:er)(e,Gi(t,3))}function lo(e,t){return(Do(e)?ut:tr)(e,Gi(t,3))}var uo=yi((function(e,t,n){Se.call(e,n)?e[n].push(t):Qn(e,n,[t])}));var so=Fr((function(e,t,n){var i=-1,a="function"==typeof t,o=zo(e)?r(e.length):[];return er(e,(function(e){o[++i]=a?at(t,e,n):br(e,t,n)})),o})),co=yi((function(e,t,n){Qn(e,n,t)}));function fo(e,t){return(Do(e)?ht:Cr)(e,Gi(t,3))}var ho=yi((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]}));var po=Fr((function(e,t){if(null==e)return[];var n=t.length;return n>1&&la(e,t[0],t[1])?t=[]:n>2&&la(t[0],t[1],t[2])&&(t=[t[0]]),Ar(e,ar(t,1),[])})),go=Gt||function(){return Qe.Date.now()};function vo(e,t,n){return t=n?void 0:t,Li(e,128,void 0,void 0,void 0,void 0,t=e&&null==t?e.length:t)}function mo(e,t){var n;if("function"!=typeof t)throw new be(a);return e=rl(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=void 0),n}}var bo=Fr((function(e,t,n){var r=1;if(n.length){var i=Vt(n,Ki(bo));r|=32}return Li(e,r,t,n,i)})),yo=Fr((function(e,t,n){var r=3;if(n.length){var i=Vt(n,Ki(yo));r|=32}return Li(t,r,e,n,i)}));function xo(e,t,n){var r,i,o,l,u,s,c=0,f=!1,d=!1,h=!0;if("function"!=typeof e)throw new be(a);function p(t){var n=r,a=i;return r=i=void 0,c=t,l=e.apply(a,n)}function g(e){return c=e,u=ya(m,t),f?p(e):l}function v(e){var n=e-s;return void 0===s||n>=t||n<0||d&&e-c>=o}function m(){var e=go();if(v(e))return b(e);u=ya(m,function(e){var n=t-(e-s);return d?ln(n,o-(e-c)):n}(e))}function b(e){return u=void 0,h&&r?p(e):(r=i=void 0,l)}function y(){var e=go(),n=v(e);if(r=arguments,i=this,s=e,n){if(void 0===u)return g(s);if(d)return ci(u),u=ya(m,t),p(s)}return void 0===u&&(u=ya(m,t)),l}return t=al(t)||0,Uo(n)&&(f=!!n.leading,o=(d="maxWait"in n)?on(al(n.maxWait)||0,t):o,h="trailing"in n?!!n.trailing:h),y.cancel=function(){void 0!==u&&ci(u),c=0,r=s=i=u=void 0},y.flush=function(){return void 0===u?l:b(go())},y}var _o=Fr((function(e,t){return Xn(e,1,t)})),wo=Fr((function(e,t,n){return Xn(e,al(t)||0,n)}));function ko(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new be(a);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(ko.Cache||zn),n}function So(e){if("function"!=typeof e)throw new be(a);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}ko.Cache=zn;var To=ui((function(e,t){var n=(t=1==t.length&&Do(t[0])?ht(t[0],Ot(Gi())):ht(ar(t,1),Ot(Gi()))).length;return Fr((function(r){for(var i=-1,a=ln(r.length,n);++i<a;)r[i]=t[i].call(this,r[i]);return at(e,this,r)}))})),Eo=Fr((function(e,t){return Li(e,32,void 0,t,Vt(t,Ki(Eo)))})),Co=Fr((function(e,t){return Li(e,64,void 0,t,Vt(t,Ki(Co)))})),Po=Hi((function(e,t){return Li(e,256,void 0,void 0,void 0,t)}));function Mo(e,t){return e===t||e!=e&&t!=t}var Oo=Ni(pr),Io=Ni((function(e,t){return e>=t})),Ao=yr(function(){return arguments}())?yr:function(e){return Ho(e)&&Se.call(e,"callee")&&!Ye.call(e,"callee")},Do=r.isArray,No=Je?Ot(Je):function(e){return Ho(e)&&hr(e)==k};function zo(e){return null!=e&&Vo(e.length)&&!Bo(e)}function jo(e){return Ho(e)&&zo(e)}var Ro=tn||au,Fo=et?Ot(et):function(e){return Ho(e)&&hr(e)==f};function Lo(e){if(!Ho(e))return!1;var t=hr(e);return t==d||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Qo(e)}function Bo(e){if(!Uo(e))return!1;var t=hr(e);return t==h||t==p||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Wo(e){return"number"==typeof e&&e==rl(e)}function Vo(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function Uo(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Ho(e){return null!=e&&"object"==typeof e}var qo=tt?Ot(tt):function(e){return Ho(e)&&na(e)==g};function $o(e){return"number"==typeof e||Ho(e)&&hr(e)==v}function Qo(e){if(!Ho(e)||hr(e)!=m)return!1;var t=qe(e);if(null===t)return!0;var n=Se.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&ke.call(n)==Pe}var Yo=nt?Ot(nt):function(e){return Ho(e)&&hr(e)==b};var Ko=rt?Ot(rt):function(e){return Ho(e)&&na(e)==y};function Go(e){return"string"==typeof e||!Do(e)&&Ho(e)&&hr(e)==x}function Zo(e){return"symbol"==typeof e||Ho(e)&&hr(e)==_}var Xo=it?Ot(it):function(e){return Ho(e)&&Vo(e.length)&&!!Be[hr(e)]};var Jo=Ni(Er),el=Ni((function(e,t){return e<=t}));function tl(e){if(!e)return[];if(zo(e))return Go(e)?$t(e):mi(e);if(Xe&&e[Xe])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Xe]());var t=na(e);return(t==g?Bt:t==y?Ut:Ml)(e)}function nl(e){return e?(e=al(e))===1/0||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function rl(e){var t=nl(e),n=t%1;return t==t?n?t-n:t:0}function il(e){return e?Kn(rl(e),0,4294967295):0}function al(e){if("number"==typeof e)return e;if(Zo(e))return NaN;if(Uo(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Uo(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(Y,"");var n=ae.test(e);return n||le.test(e)?He(e.slice(2),n?2:8):ie.test(e)?NaN:+e}function ol(e){return bi(e,_l(e))}function ll(e){return null==e?"":Zr(e)}var ul=xi((function(e,t){if(fa(t)||zo(t))bi(t,xl(t),e);else for(var n in t)Se.call(t,n)&&Un(e,n,t[n])})),sl=xi((function(e,t){bi(t,_l(t),e)})),cl=xi((function(e,t,n,r){bi(t,_l(t),e,r)})),fl=xi((function(e,t,n,r){bi(t,xl(t),e,r)})),dl=Hi(Yn);var hl=Fr((function(e,t){e=ge(e);var n=-1,r=t.length,i=r>2?t[2]:void 0;for(i&&la(t[0],t[1],i)&&(r=1);++n<r;)for(var a=t[n],o=_l(a),l=-1,u=o.length;++l<u;){var s=o[l],c=e[s];(void 0===c||Mo(c,_e[s])&&!Se.call(e,s))&&(e[s]=a[s])}return e})),pl=Fr((function(e){return e.push(void 0,Wi),at(kl,void 0,e)}));function gl(e,t,n){var r=null==e?void 0:fr(e,t);return void 0===r?n:r}function vl(e,t){return null!=e&&ra(e,t,vr)}var ml=Mi((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ce.call(t)),e[t]=n}),Ul($l)),bl=Mi((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ce.call(t)),Se.call(e,t)?e[t].push(n):e[t]=[n]}),Gi),yl=Fr(br);function xl(e){return zo(e)?Fn(e):Sr(e)}function _l(e){return zo(e)?Fn(e,!0):Tr(e)}var wl=xi((function(e,t,n){Or(e,t,n)})),kl=xi((function(e,t,n,r){Or(e,t,n,r)})),Sl=Hi((function(e,t){var n={};if(null==e)return n;var r=!1;t=ht(t,(function(t){return t=li(t,e),r||(r=t.length>1),t})),bi(e,$i(e),n),r&&(n=Gn(n,7,Vi));for(var i=t.length;i--;)Jr(n,t[i]);return n}));var Tl=Hi((function(e,t){return null==e?{}:function(e,t){return Dr(e,t,(function(t,n){return vl(e,n)}))}(e,t)}));function El(e,t){if(null==e)return{};var n=ht($i(e),(function(e){return[e]}));return t=Gi(t),Dr(e,n,(function(e,n){return t(e,n[0])}))}var Cl=Fi(xl),Pl=Fi(_l);function Ml(e){return null==e?[]:It(e,xl(e))}var Ol=Si((function(e,t,n){return t=t.toLowerCase(),e+(n?Il(t):t)}));function Il(e){return Ll(ll(e).toLowerCase())}function Al(e){return(e=ll(e))&&e.replace(se,jt).replace(De,"")}var Dl=Si((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),Nl=Si((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),zl=ki("toLowerCase");var jl=Si((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}));var Rl=Si((function(e,t,n){return e+(n?" ":"")+Ll(t)}));var Fl=Si((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),Ll=ki("toUpperCase");function Bl(e,t,n){return e=ll(e),void 0===(t=n?void 0:t)?function(e){return Re.test(e)}(e)?function(e){return e.match(ze)||[]}(e):function(e){return e.match(ee)||[]}(e):e.match(t)||[]}var Wl=Fr((function(e,t){try{return at(e,void 0,t)}catch(e){return Lo(e)?e:new de(e)}})),Vl=Hi((function(e,t){return lt(t,(function(t){t=Ta(t),Qn(e,t,bo(e[t],e))})),e}));function Ul(e){return function(){return e}}var Hl=Ci(),ql=Ci(!0);function $l(e){return e}function Ql(e){return kr("function"==typeof e?e:Gn(e,1))}var Yl=Fr((function(e,t){return function(n){return br(n,e,t)}})),Kl=Fr((function(e,t){return function(n){return br(e,n,t)}}));function Gl(e,t,n){var r=xl(t),i=cr(t,r);null!=n||Uo(t)&&(i.length||!r.length)||(n=t,t=e,e=this,i=cr(t,xl(t)));var a=!(Uo(n)&&"chain"in n&&!n.chain),o=Bo(e);return lt(i,(function(n){var r=t[n];e[n]=r,o&&(e.prototype[n]=function(){var t=this.__chain__;if(a||t){var n=e(this.__wrapped__),i=n.__actions__=mi(this.__actions__);return i.push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,pt([this.value()],arguments))})})),e}function Zl(){}var Xl=Ii(ht),Jl=Ii(st),eu=Ii(mt);function tu(e){return ua(e)?Tt(Ta(e)):function(e){return function(t){return fr(t,e)}}(e)}var nu=Di(),ru=Di(!0);function iu(){return[]}function au(){return!1}var ou=Oi((function(e,t){return e+t}),0),lu=ji("ceil"),uu=Oi((function(e,t){return e/t}),1),su=ji("floor");var cu,fu=Oi((function(e,t){return e*t}),1),du=ji("round"),hu=Oi((function(e,t){return e-t}),0);return Pn.after=function(e,t){if("function"!=typeof t)throw new be(a);return e=rl(e),function(){if(--e<1)return t.apply(this,arguments)}},Pn.ary=vo,Pn.assign=ul,Pn.assignIn=sl,Pn.assignInWith=cl,Pn.assignWith=fl,Pn.at=dl,Pn.before=mo,Pn.bind=bo,Pn.bindAll=Vl,Pn.bindKey=yo,Pn.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Do(e)?e:[e]},Pn.chain=eo,Pn.chunk=function(e,t,n){t=(n?la(e,t,n):void 0===t)?1:on(rl(t),0);var i=null==e?0:e.length;if(!i||t<1)return[];for(var a=0,o=0,l=r(Xt(i/t));a<i;)l[o++]=qr(e,a,a+=t);return l},Pn.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,i=[];++t<n;){var a=e[t];a&&(i[r++]=a)}return i},Pn.concat=function(){var e=arguments.length;if(!e)return[];for(var t=r(e-1),n=arguments[0],i=e;i--;)t[i-1]=arguments[i];return pt(Do(n)?mi(n):[n],ar(t,1))},Pn.cond=function(e){var t=null==e?0:e.length,n=Gi();return e=t?ht(e,(function(e){if("function"!=typeof e[1])throw new be(a);return[n(e[0]),e[1]]})):[],Fr((function(n){for(var r=-1;++r<t;){var i=e[r];if(at(i[0],this,n))return at(i[1],this,n)}}))},Pn.conforms=function(e){return function(e){var t=xl(e);return function(n){return Zn(n,e,t)}}(Gn(e,1))},Pn.constant=Ul,Pn.countBy=ro,Pn.create=function(e,t){var n=Mn(e);return null==t?n:$n(n,t)},Pn.curry=function e(t,n,r){var i=Li(t,8,void 0,void 0,void 0,void 0,void 0,n=r?void 0:n);return i.placeholder=e.placeholder,i},Pn.curryRight=function e(t,n,r){var i=Li(t,16,void 0,void 0,void 0,void 0,void 0,n=r?void 0:n);return i.placeholder=e.placeholder,i},Pn.debounce=xo,Pn.defaults=hl,Pn.defaultsDeep=pl,Pn.defer=_o,Pn.delay=wo,Pn.difference=Pa,Pn.differenceBy=Ma,Pn.differenceWith=Oa,Pn.drop=function(e,t,n){var r=null==e?0:e.length;return r?qr(e,(t=n||void 0===t?1:rl(t))<0?0:t,r):[]},Pn.dropRight=function(e,t,n){var r=null==e?0:e.length;return r?qr(e,0,(t=r-(t=n||void 0===t?1:rl(t)))<0?0:t):[]},Pn.dropRightWhile=function(e,t){return e&&e.length?ti(e,Gi(t,3),!0,!0):[]},Pn.dropWhile=function(e,t){return e&&e.length?ti(e,Gi(t,3),!0):[]},Pn.fill=function(e,t,n,r){var i=null==e?0:e.length;return i?(n&&"number"!=typeof n&&la(e,t,n)&&(n=0,r=i),function(e,t,n,r){var i=e.length;for((n=rl(n))<0&&(n=-n>i?0:i+n),(r=void 0===r||r>i?i:rl(r))<0&&(r+=i),r=n>r?0:il(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},Pn.filter=function(e,t){return(Do(e)?ct:ir)(e,Gi(t,3))},Pn.flatMap=function(e,t){return ar(fo(e,t),1)},Pn.flatMapDeep=function(e,t){return ar(fo(e,t),1/0)},Pn.flatMapDepth=function(e,t,n){return n=void 0===n?1:rl(n),ar(fo(e,t),n)},Pn.flatten=Da,Pn.flattenDeep=function(e){return(null==e?0:e.length)?ar(e,1/0):[]},Pn.flattenDepth=function(e,t){return(null==e?0:e.length)?ar(e,t=void 0===t?1:rl(t)):[]},Pn.flip=function(e){return Li(e,512)},Pn.flow=Hl,Pn.flowRight=ql,Pn.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var i=e[t];r[i[0]]=i[1]}return r},Pn.functions=function(e){return null==e?[]:cr(e,xl(e))},Pn.functionsIn=function(e){return null==e?[]:cr(e,_l(e))},Pn.groupBy=uo,Pn.initial=function(e){return(null==e?0:e.length)?qr(e,0,-1):[]},Pn.intersection=za,Pn.intersectionBy=ja,Pn.intersectionWith=Ra,Pn.invert=ml,Pn.invertBy=bl,Pn.invokeMap=so,Pn.iteratee=Ql,Pn.keyBy=co,Pn.keys=xl,Pn.keysIn=_l,Pn.map=fo,Pn.mapKeys=function(e,t){var n={};return t=Gi(t,3),ur(e,(function(e,r,i){Qn(n,t(e,r,i),e)})),n},Pn.mapValues=function(e,t){var n={};return t=Gi(t,3),ur(e,(function(e,r,i){Qn(n,r,t(e,r,i))})),n},Pn.matches=function(e){return Pr(Gn(e,1))},Pn.matchesProperty=function(e,t){return Mr(e,Gn(t,1))},Pn.memoize=ko,Pn.merge=wl,Pn.mergeWith=kl,Pn.method=Yl,Pn.methodOf=Kl,Pn.mixin=Gl,Pn.negate=So,Pn.nthArg=function(e){return e=rl(e),Fr((function(t){return Ir(t,e)}))},Pn.omit=Sl,Pn.omitBy=function(e,t){return El(e,So(Gi(t)))},Pn.once=function(e){return mo(2,e)},Pn.orderBy=function(e,t,n,r){return null==e?[]:(Do(t)||(t=null==t?[]:[t]),Do(n=r?void 0:n)||(n=null==n?[]:[n]),Ar(e,t,n))},Pn.over=Xl,Pn.overArgs=To,Pn.overEvery=Jl,Pn.overSome=eu,Pn.partial=Eo,Pn.partialRight=Co,Pn.partition=ho,Pn.pick=Tl,Pn.pickBy=El,Pn.property=tu,Pn.propertyOf=function(e){return function(t){return null==e?void 0:fr(e,t)}},Pn.pull=La,Pn.pullAll=Ba,Pn.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Nr(e,t,Gi(n,2)):e},Pn.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Nr(e,t,void 0,n):e},Pn.pullAt=Wa,Pn.range=nu,Pn.rangeRight=ru,Pn.rearg=Po,Pn.reject=function(e,t){return(Do(e)?ct:ir)(e,So(Gi(t,3)))},Pn.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,i=[],a=e.length;for(t=Gi(t,3);++r<a;){var o=e[r];t(o,r,e)&&(n.push(o),i.push(r))}return zr(e,i),n},Pn.rest=function(e,t){if("function"!=typeof e)throw new be(a);return Fr(e,t=void 0===t?t:rl(t))},Pn.reverse=Va,Pn.sampleSize=function(e,t,n){return t=(n?la(e,t,n):void 0===t)?1:rl(t),(Do(e)?Bn:Br)(e,t)},Pn.set=function(e,t,n){return null==e?e:Wr(e,t,n)},Pn.setWith=function(e,t,n,r){return r="function"==typeof r?r:void 0,null==e?e:Wr(e,t,n,r)},Pn.shuffle=function(e){return(Do(e)?Wn:Hr)(e)},Pn.slice=function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&la(e,t,n)?(t=0,n=r):(t=null==t?0:rl(t),n=void 0===n?r:rl(n)),qr(e,t,n)):[]},Pn.sortBy=po,Pn.sortedUniq=function(e){return e&&e.length?Kr(e):[]},Pn.sortedUniqBy=function(e,t){return e&&e.length?Kr(e,Gi(t,2)):[]},Pn.split=function(e,t,n){return n&&"number"!=typeof n&&la(e,t,n)&&(t=n=void 0),(n=void 0===n?4294967295:n>>>0)?(e=ll(e))&&("string"==typeof t||null!=t&&!Yo(t))&&!(t=Zr(t))&&Lt(e)?si($t(e),0,n):e.split(t,n):[]},Pn.spread=function(e,t){if("function"!=typeof e)throw new be(a);return t=null==t?0:on(rl(t),0),Fr((function(n){var r=n[t],i=si(n,0,t);return r&&pt(i,r),at(e,this,i)}))},Pn.tail=function(e){var t=null==e?0:e.length;return t?qr(e,1,t):[]},Pn.take=function(e,t,n){return e&&e.length?qr(e,0,(t=n||void 0===t?1:rl(t))<0?0:t):[]},Pn.takeRight=function(e,t,n){var r=null==e?0:e.length;return r?qr(e,(t=r-(t=n||void 0===t?1:rl(t)))<0?0:t,r):[]},Pn.takeRightWhile=function(e,t){return e&&e.length?ti(e,Gi(t,3),!1,!0):[]},Pn.takeWhile=function(e,t){return e&&e.length?ti(e,Gi(t,3)):[]},Pn.tap=function(e,t){return t(e),e},Pn.throttle=function(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new be(a);return Uo(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),xo(e,t,{leading:r,maxWait:t,trailing:i})},Pn.thru=to,Pn.toArray=tl,Pn.toPairs=Cl,Pn.toPairsIn=Pl,Pn.toPath=function(e){return Do(e)?ht(e,Ta):Zo(e)?[e]:mi(Sa(ll(e)))},Pn.toPlainObject=ol,Pn.transform=function(e,t,n){var r=Do(e),i=r||Ro(e)||Xo(e);if(t=Gi(t,4),null==n){var a=e&&e.constructor;n=i?r?new a:[]:Uo(e)&&Bo(a)?Mn(qe(e)):{}}return(i?lt:ur)(e,(function(e,r,i){return t(n,e,r,i)})),n},Pn.unary=function(e){return vo(e,1)},Pn.union=Ua,Pn.unionBy=Ha,Pn.unionWith=qa,Pn.uniq=function(e){return e&&e.length?Xr(e):[]},Pn.uniqBy=function(e,t){return e&&e.length?Xr(e,Gi(t,2)):[]},Pn.uniqWith=function(e,t){return t="function"==typeof t?t:void 0,e&&e.length?Xr(e,void 0,t):[]},Pn.unset=function(e,t){return null==e||Jr(e,t)},Pn.unzip=$a,Pn.unzipWith=Qa,Pn.update=function(e,t,n){return null==e?e:ei(e,t,oi(n))},Pn.updateWith=function(e,t,n,r){return r="function"==typeof r?r:void 0,null==e?e:ei(e,t,oi(n),r)},Pn.values=Ml,Pn.valuesIn=function(e){return null==e?[]:It(e,_l(e))},Pn.without=Ya,Pn.words=Bl,Pn.wrap=function(e,t){return Eo(oi(t),e)},Pn.xor=Ka,Pn.xorBy=Ga,Pn.xorWith=Za,Pn.zip=Xa,Pn.zipObject=function(e,t){return ii(e||[],t||[],Un)},Pn.zipObjectDeep=function(e,t){return ii(e||[],t||[],Wr)},Pn.zipWith=Ja,Pn.entries=Cl,Pn.entriesIn=Pl,Pn.extend=sl,Pn.extendWith=cl,Gl(Pn,Pn),Pn.add=ou,Pn.attempt=Wl,Pn.camelCase=Ol,Pn.capitalize=Il,Pn.ceil=lu,Pn.clamp=function(e,t,n){return void 0===n&&(n=t,t=void 0),void 0!==n&&(n=(n=al(n))==n?n:0),void 0!==t&&(t=(t=al(t))==t?t:0),Kn(al(e),t,n)},Pn.clone=function(e){return Gn(e,4)},Pn.cloneDeep=function(e){return Gn(e,5)},Pn.cloneDeepWith=function(e,t){return Gn(e,5,t="function"==typeof t?t:void 0)},Pn.cloneWith=function(e,t){return Gn(e,4,t="function"==typeof t?t:void 0)},Pn.conformsTo=function(e,t){return null==t||Zn(e,t,xl(t))},Pn.deburr=Al,Pn.defaultTo=function(e,t){return null==e||e!=e?t:e},Pn.divide=uu,Pn.endsWith=function(e,t,n){e=ll(e),t=Zr(t);var r=e.length,i=n=void 0===n?r:Kn(rl(n),0,r);return(n-=t.length)>=0&&e.slice(n,i)==t},Pn.eq=Mo,Pn.escape=function(e){return(e=ll(e))&&L.test(e)?e.replace(R,Rt):e},Pn.escapeRegExp=function(e){return(e=ll(e))&&Q.test(e)?e.replace($,"\\$&"):e},Pn.every=function(e,t,n){var r=Do(e)?st:nr;return n&&la(e,t,n)&&(t=void 0),r(e,Gi(t,3))},Pn.find=io,Pn.findIndex=Ia,Pn.findKey=function(e,t){return yt(e,Gi(t,3),ur)},Pn.findLast=ao,Pn.findLastIndex=Aa,Pn.findLastKey=function(e,t){return yt(e,Gi(t,3),sr)},Pn.floor=su,Pn.forEach=oo,Pn.forEachRight=lo,Pn.forIn=function(e,t){return null==e?e:or(e,Gi(t,3),_l)},Pn.forInRight=function(e,t){return null==e?e:lr(e,Gi(t,3),_l)},Pn.forOwn=function(e,t){return e&&ur(e,Gi(t,3))},Pn.forOwnRight=function(e,t){return e&&sr(e,Gi(t,3))},Pn.get=gl,Pn.gt=Oo,Pn.gte=Io,Pn.has=function(e,t){return null!=e&&ra(e,t,gr)},Pn.hasIn=vl,Pn.head=Na,Pn.identity=$l,Pn.includes=function(e,t,n,r){e=zo(e)?e:Ml(e),n=n&&!r?rl(n):0;var i=e.length;return n<0&&(n=on(i+n,0)),Go(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&_t(e,t,n)>-1},Pn.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:rl(n);return i<0&&(i=on(r+i,0)),_t(e,t,i)},Pn.inRange=function(e,t,n){return t=nl(t),void 0===n?(n=t,t=0):n=nl(n),function(e,t,n){return e>=ln(t,n)&&e<on(t,n)}(e=al(e),t,n)},Pn.invoke=yl,Pn.isArguments=Ao,Pn.isArray=Do,Pn.isArrayBuffer=No,Pn.isArrayLike=zo,Pn.isArrayLikeObject=jo,Pn.isBoolean=function(e){return!0===e||!1===e||Ho(e)&&hr(e)==c},Pn.isBuffer=Ro,Pn.isDate=Fo,Pn.isElement=function(e){return Ho(e)&&1===e.nodeType&&!Qo(e)},Pn.isEmpty=function(e){if(null==e)return!0;if(zo(e)&&(Do(e)||"string"==typeof e||"function"==typeof e.splice||Ro(e)||Xo(e)||Ao(e)))return!e.length;var t=na(e);if(t==g||t==y)return!e.size;if(fa(e))return!Sr(e).length;for(var n in e)if(Se.call(e,n))return!1;return!0},Pn.isEqual=function(e,t){return xr(e,t)},Pn.isEqualWith=function(e,t,n){var r=(n="function"==typeof n?n:void 0)?n(e,t):void 0;return void 0===r?xr(e,t,void 0,n):!!r},Pn.isError=Lo,Pn.isFinite=function(e){return"number"==typeof e&&nn(e)},Pn.isFunction=Bo,Pn.isInteger=Wo,Pn.isLength=Vo,Pn.isMap=qo,Pn.isMatch=function(e,t){return e===t||_r(e,t,Xi(t))},Pn.isMatchWith=function(e,t,n){return n="function"==typeof n?n:void 0,_r(e,t,Xi(t),n)},Pn.isNaN=function(e){return $o(e)&&e!=+e},Pn.isNative=function(e){if(ca(e))throw new de("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return wr(e)},Pn.isNil=function(e){return null==e},Pn.isNull=function(e){return null===e},Pn.isNumber=$o,Pn.isObject=Uo,Pn.isObjectLike=Ho,Pn.isPlainObject=Qo,Pn.isRegExp=Yo,Pn.isSafeInteger=function(e){return Wo(e)&&e>=-9007199254740991&&e<=9007199254740991},Pn.isSet=Ko,Pn.isString=Go,Pn.isSymbol=Zo,Pn.isTypedArray=Xo,Pn.isUndefined=function(e){return void 0===e},Pn.isWeakMap=function(e){return Ho(e)&&na(e)==w},Pn.isWeakSet=function(e){return Ho(e)&&"[object WeakSet]"==hr(e)},Pn.join=function(e,t){return null==e?"":rn.call(e,t)},Pn.kebabCase=Dl,Pn.last=Fa,Pn.lastIndexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r;return void 0!==n&&(i=(i=rl(n))<0?on(r+i,0):ln(i,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,i):xt(e,kt,i,!0)},Pn.lowerCase=Nl,Pn.lowerFirst=zl,Pn.lt=Jo,Pn.lte=el,Pn.max=function(e){return e&&e.length?rr(e,$l,pr):void 0},Pn.maxBy=function(e,t){return e&&e.length?rr(e,Gi(t,2),pr):void 0},Pn.mean=function(e){return St(e,$l)},Pn.meanBy=function(e,t){return St(e,Gi(t,2))},Pn.min=function(e){return e&&e.length?rr(e,$l,Er):void 0},Pn.minBy=function(e,t){return e&&e.length?rr(e,Gi(t,2),Er):void 0},Pn.stubArray=iu,Pn.stubFalse=au,Pn.stubObject=function(){return{}},Pn.stubString=function(){return""},Pn.stubTrue=function(){return!0},Pn.multiply=fu,Pn.nth=function(e,t){return e&&e.length?Ir(e,rl(t)):void 0},Pn.noConflict=function(){return Qe._===this&&(Qe._=Me),this},Pn.noop=Zl,Pn.now=go,Pn.pad=function(e,t,n){e=ll(e);var r=(t=rl(t))?qt(e):0;if(!t||r>=t)return e;var i=(t-r)/2;return Ai(Jt(i),n)+e+Ai(Xt(i),n)},Pn.padEnd=function(e,t,n){e=ll(e);var r=(t=rl(t))?qt(e):0;return t&&r<t?e+Ai(t-r,n):e},Pn.padStart=function(e,t,n){e=ll(e);var r=(t=rl(t))?qt(e):0;return t&&r<t?Ai(t-r,n)+e:e},Pn.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),sn(ll(e).replace(K,""),t||0)},Pn.random=function(e,t,n){if(n&&"boolean"!=typeof n&&la(e,t,n)&&(t=n=void 0),void 0===n&&("boolean"==typeof t?(n=t,t=void 0):"boolean"==typeof e&&(n=e,e=void 0)),void 0===e&&void 0===t?(e=0,t=1):(e=nl(e),void 0===t?(t=e,e=0):t=nl(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var i=cn();return ln(e+i*(t-e+Ue("1e-"+((i+"").length-1))),t)}return jr(e,t)},Pn.reduce=function(e,t,n){var r=Do(e)?gt:Ct,i=arguments.length<3;return r(e,Gi(t,4),n,i,er)},Pn.reduceRight=function(e,t,n){var r=Do(e)?vt:Ct,i=arguments.length<3;return r(e,Gi(t,4),n,i,tr)},Pn.repeat=function(e,t,n){return t=(n?la(e,t,n):void 0===t)?1:rl(t),Rr(ll(e),t)},Pn.replace=function(){var e=arguments,t=ll(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Pn.result=function(e,t,n){var r=-1,i=(t=li(t,e)).length;for(i||(i=1,e=void 0);++r<i;){var a=null==e?void 0:e[Ta(t[r])];void 0===a&&(r=i,a=n),e=Bo(a)?a.call(e):a}return e},Pn.round=du,Pn.runInContext=e,Pn.sample=function(e){return(Do(e)?Ln:Lr)(e)},Pn.size=function(e){if(null==e)return 0;if(zo(e))return Go(e)?qt(e):e.length;var t=na(e);return t==g||t==y?e.size:Sr(e).length},Pn.snakeCase=jl,Pn.some=function(e,t,n){var r=Do(e)?mt:$r;return n&&la(e,t,n)&&(t=void 0),r(e,Gi(t,3))},Pn.sortedIndex=function(e,t){return Qr(e,t)},Pn.sortedIndexBy=function(e,t,n){return Yr(e,t,Gi(n,2))},Pn.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=Qr(e,t);if(r<n&&Mo(e[r],t))return r}return-1},Pn.sortedLastIndex=function(e,t){return Qr(e,t,!0)},Pn.sortedLastIndexBy=function(e,t,n){return Yr(e,t,Gi(n,2),!0)},Pn.sortedLastIndexOf=function(e,t){if(null==e?0:e.length){var n=Qr(e,t,!0)-1;if(Mo(e[n],t))return n}return-1},Pn.startCase=Rl,Pn.startsWith=function(e,t,n){return e=ll(e),n=null==n?0:Kn(rl(n),0,e.length),t=Zr(t),e.slice(n,n+t.length)==t},Pn.subtract=hu,Pn.sum=function(e){return e&&e.length?Pt(e,$l):0},Pn.sumBy=function(e,t){return e&&e.length?Pt(e,Gi(t,2)):0},Pn.template=function(e,t,n){var r=Pn.templateSettings;n&&la(e,t,n)&&(t=void 0),e=ll(e),t=cl({},t,r,Bi);var i,a,o=cl({},t.imports,r.imports,Bi),l=xl(o),u=It(o,l),s=0,c=t.interpolate||ce,f="__p += '",d=ve((t.escape||ce).source+"|"+c.source+"|"+(c===V?ne:ce).source+"|"+(t.evaluate||ce).source+"|$","g"),h="//# sourceURL="+(Se.call(t,"sourceURL")?(t.sourceURL+"").replace(/[\r\n]/g," "):"lodash.templateSources["+ ++Le+"]")+"\n";e.replace(d,(function(t,n,r,o,l,u){return r||(r=o),f+=e.slice(s,u).replace(fe,Ft),n&&(i=!0,f+="' +\n__e("+n+") +\n'"),l&&(a=!0,f+="';\n"+l+";\n__p += '"),r&&(f+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),s=u+t.length,t})),f+="';\n";var p=Se.call(t,"variable")&&t.variable;p||(f="with (obj) {\n"+f+"\n}\n"),f=(a?f.replace(D,""):f).replace(N,"$1").replace(z,"$1;"),f="function("+(p||"obj")+") {\n"+(p?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+f+"return __p\n}";var g=Wl((function(){return he(l,h+"return "+f).apply(void 0,u)}));if(g.source=f,Lo(g))throw g;return g},Pn.times=function(e,t){if((e=rl(e))<1||e>9007199254740991)return[];var n=4294967295,r=ln(e,4294967295);e-=4294967295;for(var i=Mt(r,t=Gi(t));++n<e;)t(n);return i},Pn.toFinite=nl,Pn.toInteger=rl,Pn.toLength=il,Pn.toLower=function(e){return ll(e).toLowerCase()},Pn.toNumber=al,Pn.toSafeInteger=function(e){return e?Kn(rl(e),-9007199254740991,9007199254740991):0===e?e:0},Pn.toString=ll,Pn.toUpper=function(e){return ll(e).toUpperCase()},Pn.trim=function(e,t,n){if((e=ll(e))&&(n||void 0===t))return e.replace(Y,"");if(!e||!(t=Zr(t)))return e;var r=$t(e),i=$t(t);return si(r,Dt(r,i),Nt(r,i)+1).join("")},Pn.trimEnd=function(e,t,n){if((e=ll(e))&&(n||void 0===t))return e.replace(G,"");if(!e||!(t=Zr(t)))return e;var r=$t(e);return si(r,0,Nt(r,$t(t))+1).join("")},Pn.trimStart=function(e,t,n){if((e=ll(e))&&(n||void 0===t))return e.replace(K,"");if(!e||!(t=Zr(t)))return e;var r=$t(e);return si(r,Dt(r,$t(t))).join("")},Pn.truncate=function(e,t){var n=30,r="...";if(Uo(t)){var i="separator"in t?t.separator:i;n="length"in t?rl(t.length):n,r="omission"in t?Zr(t.omission):r}var a=(e=ll(e)).length;if(Lt(e)){var o=$t(e);a=o.length}if(n>=a)return e;var l=n-qt(r);if(l<1)return r;var u=o?si(o,0,l).join(""):e.slice(0,l);if(void 0===i)return u+r;if(o&&(l+=u.length-l),Yo(i)){if(e.slice(l).search(i)){var s,c=u;for(i.global||(i=ve(i.source,ll(re.exec(i))+"g")),i.lastIndex=0;s=i.exec(c);)var f=s.index;u=u.slice(0,void 0===f?l:f)}}else if(e.indexOf(Zr(i),l)!=l){var d=u.lastIndexOf(i);d>-1&&(u=u.slice(0,d))}return u+r},Pn.unescape=function(e){return(e=ll(e))&&F.test(e)?e.replace(j,Qt):e},Pn.uniqueId=function(e){var t=++Te;return ll(e)+t},Pn.upperCase=Fl,Pn.upperFirst=Ll,Pn.each=oo,Pn.eachRight=lo,Pn.first=Na,Gl(Pn,(cu={},ur(Pn,(function(e,t){Se.call(Pn.prototype,t)||(cu[t]=e)})),cu),{chain:!1}),Pn.VERSION="4.17.15",lt(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Pn[e].placeholder=Pn})),lt(["drop","take"],(function(e,t){An.prototype[e]=function(n){n=void 0===n?1:on(rl(n),0);var r=this.__filtered__&&!t?new An(this):this.clone();return r.__filtered__?r.__takeCount__=ln(n,r.__takeCount__):r.__views__.push({size:ln(n,4294967295),type:e+(r.__dir__<0?"Right":"")}),r},An.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),lt(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;An.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Gi(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),lt(["head","last"],(function(e,t){var n="take"+(t?"Right":"");An.prototype[e]=function(){return this[n](1).value()[0]}})),lt(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");An.prototype[e]=function(){return this.__filtered__?new An(this):this[n](1)}})),An.prototype.compact=function(){return this.filter($l)},An.prototype.find=function(e){return this.filter(e).head()},An.prototype.findLast=function(e){return this.reverse().find(e)},An.prototype.invokeMap=Fr((function(e,t){return"function"==typeof e?new An(this):this.map((function(n){return br(n,e,t)}))})),An.prototype.reject=function(e){return this.filter(So(Gi(e)))},An.prototype.slice=function(e,t){e=rl(e);var n=this;return n.__filtered__&&(e>0||t<0)?new An(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),void 0!==t&&(n=(t=rl(t))<0?n.dropRight(-t):n.take(t-e)),n)},An.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},An.prototype.toArray=function(){return this.take(4294967295)},ur(An.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),i=Pn[r?"take"+("last"==t?"Right":""):t],a=r||/^find/.test(t);i&&(Pn.prototype[t]=function(){var t=this.__wrapped__,o=r?[1]:arguments,l=t instanceof An,u=o[0],s=l||Do(t),c=function(e){var t=i.apply(Pn,pt([e],o));return r&&f?t[0]:t};s&&n&&"function"==typeof u&&1!=u.length&&(l=s=!1);var f=this.__chain__,d=!!this.__actions__.length,h=a&&!f,p=l&&!d;if(!a&&s){t=p?t:new An(this);var g=e.apply(t,o);return g.__actions__.push({func:to,args:[c],thisArg:void 0}),new In(g,f)}return h&&p?e.apply(this,o):(g=this.thru(c),h?r?g.value()[0]:g.value():g)})})),lt(["pop","push","shift","sort","splice","unshift"],(function(e){var t=ye[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Pn.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var i=this.value();return t.apply(Do(i)?i:[],e)}return this[n]((function(n){return t.apply(Do(n)?n:[],e)}))}})),ur(An.prototype,(function(e,t){var n=Pn[t];if(n){var r=n.name+"";Se.call(yn,r)||(yn[r]=[]),yn[r].push({name:t,func:n})}})),yn[Pi(void 0,2).name]=[{name:"wrapper",func:void 0}],An.prototype.clone=function(){var e=new An(this.__wrapped__);return e.__actions__=mi(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=mi(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=mi(this.__views__),e},An.prototype.reverse=function(){if(this.__filtered__){var e=new An(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},An.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Do(e),r=t<0,i=n?e.length:0,a=function(e,t,n){var r=-1,i=n.length;for(;++r<i;){var a=n[r],o=a.size;switch(a.type){case"drop":e+=o;break;case"dropRight":t-=o;break;case"take":t=ln(t,e+o);break;case"takeRight":e=on(e,t-o)}}return{start:e,end:t}}(0,i,this.__views__),o=a.start,l=a.end,u=l-o,s=r?l:o-1,c=this.__iteratees__,f=c.length,d=0,h=ln(u,this.__takeCount__);if(!n||!r&&i==u&&h==u)return ni(e,this.__actions__);var p=[];e:for(;u--&&d<h;){for(var g=-1,v=e[s+=t];++g<f;){var m=c[g],b=m.iteratee,y=m.type,x=b(v);if(2==y)v=x;else if(!x){if(1==y)continue e;break e}}p[d++]=v}return p},Pn.prototype.at=no,Pn.prototype.chain=function(){return eo(this)},Pn.prototype.commit=function(){return new In(this.value(),this.__chain__)},Pn.prototype.next=function(){void 0===this.__values__&&(this.__values__=tl(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?void 0:this.__values__[this.__index__++]}},Pn.prototype.plant=function(e){for(var t,n=this;n instanceof On;){var r=Ca(n);r.__index__=0,r.__values__=void 0,t?i.__wrapped__=r:t=r;var i=r;n=n.__wrapped__}return i.__wrapped__=e,t},Pn.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof An){var t=e;return this.__actions__.length&&(t=new An(this)),(t=t.reverse()).__actions__.push({func:to,args:[Va],thisArg:void 0}),new In(t,this.__chain__)}return this.thru(Va)},Pn.prototype.toJSON=Pn.prototype.valueOf=Pn.prototype.value=function(){return ni(this.__wrapped__,this.__actions__)},Pn.prototype.first=Pn.prototype.head,Xe&&(Pn.prototype[Xe]=function(){return this}),Pn}();Qe._=Yt,void 0===(i=function(){return Yt}.call(t,n,t,r))||(r.exports=i)}).call(this)}).call(this,n(32),n(20)(e))},function(e,t,n){var r=n(63),i=n(6);e.exports=function e(t,n,a,o,l){return t===n||(null==t||null==n||!i(t)&&!i(n)?t!=t&&n!=n:r(t,n,a,o,e,l))}},function(e,t,n){var r=n(4)(n(2),"Map");e.exports=r},function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},function(e,t,n){var r=n(80),i=n(87),a=n(89),o=n(90),l=n(91);function u(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}u.prototype.clear=r,u.prototype.delete=i,u.prototype.get=a,u.prototype.has=o,u.prototype.set=l,e.exports=u},function(e,t,n){var r=n(108),i=n(115),a=n(39);e.exports=function(e){return a(e)?r(e):i(e)}},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},function(e,t,n){var r=n(3),i=n(23),a=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,o=/^\w*$/;e.exports=function(e,t){if(r(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!i(e))||(o.test(e)||!a.test(e)||null!=t&&e in Object(t))}},function(e,t,n){var r=n(5),i=n(6);e.exports=function(e){return"symbol"==typeof e||i(e)&&"[object Symbol]"==r(e)}},function(e,t,n){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;function o(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(e){return!1}}()?Object.assign:function(e,t){for(var n,l,u=o(e),s=1;s<arguments.length;s++){for(var c in n=Object(arguments[s]))i.call(n,c)&&(u[c]=n[c]);if(r){l=r(n);for(var f=0;f<l.length;f++)a.call(n,l[f])&&(u[l[f]]=n[l[f]])}}return u}},function(e,t,n){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var i=(o=r,l=btoa(unescape(encodeURIComponent(JSON.stringify(o)))),u="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(l),"/*# ".concat(u," */")),a=r.sources.map((function(e){return"/*# sourceURL=".concat(r.sourceRoot).concat(e," */")}));return[n].concat(a).concat([i]).join("\n")}var o,l,u;return[n].join("\n")}(t,e);return t[2]?"@media ".concat(t[2],"{").concat(n,"}"):n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},i=0;i<this.length;i++){var a=this[i][0];null!=a&&(r[a]=!0)}for(var o=0;o<e.length;o++){var l=e[o];null!=l[0]&&r[l[0]]||(n&&!l[2]?l[2]=n:n&&(l[2]="(".concat(l[2],") and (").concat(n,")")),t.push(l))}},t}},function(e,t,n){var r,i,a={},o=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=r.apply(this,arguments)),i}),l=function(e,t){return t?t.querySelector(e):document.querySelector(e)},u=function(e){var t={};return function(e,n){if("function"==typeof e)return e();if(void 0===t[e]){var r=l.call(this,e,n);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}}(),s=null,c=0,f=[],d=n(53);function h(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=a[r.id];if(i){i.refs++;for(var o=0;o<i.parts.length;o++)i.parts[o](r.parts[o]);for(;o<r.parts.length;o++)i.parts.push(y(r.parts[o],t))}else{var l=[];for(o=0;o<r.parts.length;o++)l.push(y(r.parts[o],t));a[r.id]={id:r.id,refs:1,parts:l}}}}function p(e,t){for(var n=[],r={},i=0;i<e.length;i++){var a=e[i],o=t.base?a[0]+t.base:a[0],l={css:a[1],media:a[2],sourceMap:a[3]};r[o]?r[o].parts.push(l):n.push(r[o]={id:o,parts:[l]})}return n}function g(e,t){var n=u(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=f[f.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),f.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=u(e.insertAt.before,n);n.insertBefore(t,i)}}function v(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=f.indexOf(e);t>=0&&f.splice(t,1)}function m(e){var t=document.createElement("style");if(void 0===e.attrs.type&&(e.attrs.type="text/css"),void 0===e.attrs.nonce){var r=function(){0;return n.nc}();r&&(e.attrs.nonce=r)}return b(t,e.attrs),g(e,t),t}function b(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function y(e,t){var n,r,i,a;if(t.transform&&e.css){if(!(a="function"==typeof t.transform?t.transform(e.css):t.transform.default(e.css)))return function(){};e.css=a}if(t.singleton){var o=c++;n=s||(s=m(t)),r=w.bind(null,n,o,!1),i=w.bind(null,n,o,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(e){var t=document.createElement("link");return void 0===e.attrs.type&&(e.attrs.type="text/css"),e.attrs.rel="stylesheet",b(t,e.attrs),g(e,t),t}(t),r=S.bind(null,n,t),i=function(){v(n),n.href&&URL.revokeObjectURL(n.href)}):(n=m(t),r=k.bind(null,n),i=function(){v(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=o()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=p(e,t);return h(n,t),function(e){for(var r=[],i=0;i<n.length;i++){var o=n[i];(l=a[o.id]).refs--,r.push(l)}e&&h(p(e,t),t);for(i=0;i<r.length;i++){var l;if(0===(l=r[i]).refs){for(var u=0;u<l.parts.length;u++)l.parts[u]();delete a[l.id]}}}};var x,_=(x=[],function(e,t){return x[e]=t,x.filter(Boolean).join("\n")});function w(e,t,n,r){var i=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=_(t,i);else{var a=document.createTextNode(i),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(a,o[t]):e.appendChild(a)}}function k(e,t){var n=t.css,r=t.media;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function S(e,t,n){var r=n.css,i=n.sourceMap,a=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||a)&&(r=d(r)),i&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var o=new Blob([r],{type:"text/css"}),l=e.href;e.href=URL.createObjectURL(o),l&&URL.revokeObjectURL(l)}},function(e,t,n){e.exports=n(54)()},function(e,t,n){var r=n(7),i=n(69),a=n(70),o=n(71),l=n(72),u=n(73);function s(e){var t=this.__data__=new r(e);this.size=t.size}s.prototype.clear=i,s.prototype.delete=a,s.prototype.get=o,s.prototype.has=l,s.prototype.set=u,e.exports=s},function(e,t){e.exports=function(e,t){return e===t||e!=e&&t!=t}},function(e,t,n){var r=n(5),i=n(17);e.exports=function(e){if(!i(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n(32))},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){var n=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return n.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},function(e,t,n){var r=n(92),i=n(95),a=n(96);e.exports=function(e,t,n,o,l,u){var s=1&n,c=e.length,f=t.length;if(c!=f&&!(s&&f>c))return!1;var d=u.get(e),h=u.get(t);if(d&&h)return d==t&&h==e;var p=-1,g=!0,v=2&n?new r:void 0;for(u.set(e,t),u.set(t,e);++p<c;){var m=e[p],b=t[p];if(o)var y=s?o(b,m,p,t,e,u):o(m,b,p,e,t,u);if(void 0!==y){if(y)continue;g=!1;break}if(v){if(!i(t,(function(e,t){if(!a(v,t)&&(m===e||l(m,e,n,o,u)))return v.push(t)}))){g=!1;break}}else if(m!==b&&!l(m,b,n,o,u)){g=!1;break}}return u.delete(e),u.delete(t),g}},function(e,t,n){var r=n(110),i=n(6),a=Object.prototype,o=a.hasOwnProperty,l=a.propertyIsEnumerable,u=r(function(){return arguments}())?r:function(e){return i(e)&&o.call(e,"callee")&&!l.call(e,"callee")};e.exports=u},function(e,t,n){(function(e){var r=n(2),i=n(111),a=t&&!t.nodeType&&t,o=a&&"object"==typeof e&&e&&!e.nodeType&&e,l=o&&o.exports===a?r.Buffer:void 0,u=(l?l.isBuffer:void 0)||i;e.exports=u}).call(this,n(20)(e))},function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&n.test(e))&&e>-1&&e%1==0&&e<t}},function(e,t,n){var r=n(112),i=n(113),a=n(114),o=a&&a.isTypedArray,l=o?i(o):r;e.exports=l},function(e,t,n){var r=n(30),i=n(21);e.exports=function(e){return null!=e&&i(e.length)&&!r(e)}},function(e,t,n){var r=n(17);e.exports=function(e){return e==e&&!r(e)}},function(e,t){e.exports=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}}},function(e,t,n){var r=n(43),i=n(12);e.exports=function(e,t){for(var n=0,a=(t=r(t,e)).length;null!=e&&n<a;)e=e[i(t[n++])];return n&&n==a?e:void 0}},function(e,t,n){var r=n(3),i=n(22),a=n(141),o=n(144);e.exports=function(e,t){return r(e)?e:i(e,t)?[e]:a(o(e))}},function(e,t,n){"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE){0;try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}}(),e.exports=n(49)},function(e,t,n){"use strict";var r=n(0),i=n(27),a=n(56),o=n(57),l=n(58),u=13,s=9,c=8,f=38,d=40,h={root:"react-tags",rootFocused:"is-focused",selected:"react-tags__selected",selectedTag:"react-tags__selected-tag",selectedTagName:"react-tags__selected-tag-name",search:"react-tags__search",searchInput:"react-tags__search-input",suggestions:"react-tags__suggestions",suggestionActive:"is-active",suggestionDisabled:"is-disabled"},p=function(e){function t(t){e.call(this,t),this.state={query:"",focused:!1,expandable:!1,selectedIndex:-1,classNames:Object.assign({},h,this.props.classNames)},this.inputEventHandlers={onChange:function(){},onBlur:this.handleBlur.bind(this),onFocus:this.handleFocus.bind(this),onInput:this.handleInput.bind(this),onKeyDown:this.handleKeyDown.bind(this)}}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.componentWillReceiveProps=function(e){this.setState({classNames:Object.assign({},h,e.classNames)})},t.prototype.handleInput=function(e){var t=e.target.value;this.props.handleInputChange&&this.props.handleInputChange(t),this.setState({query:t})},t.prototype.handleKeyDown=function(e){var t=this.state,n=t.query,r=t.selectedIndex,i=this.props,a=i.delimiters,o=i.delimiterChars;(a.indexOf(e.keyCode)>-1||o.indexOf(e.key)>-1)&&((n||r>-1)&&e.preventDefault(),this.handleDelimiter()),e.keyCode===c&&0===n.length&&this.props.allowBackspace&&this.deleteTag(this.props.tags.length-1),e.keyCode===f&&(e.preventDefault(),r<=0?this.setState({selectedIndex:this.suggestions.state.options.length-1}):this.setState({selectedIndex:r-1})),e.keyCode===d&&(e.preventDefault(),this.setState({selectedIndex:(r+1)%this.suggestions.state.options.length}))},t.prototype.handleDelimiter=function(){var e=this.state,t=e.query,n=e.selectedIndex;if(t.length>=this.props.minQueryLength){var r=this.suggestions.state.options.findIndex((function(e){return 0===e.name.search(new RegExp("^"+t+"$","i"))})),i=-1===n?r:n;i>-1&&this.suggestions.state.options[i]?this.addTag(this.suggestions.state.options[i]):this.props.allowNew&&this.addTag({name:t})}},t.prototype.handleClick=function(e){document.activeElement!==e.target&&this.input.input.focus()},t.prototype.handleBlur=function(){this.setState({focused:!1,selectedIndex:-1}),this.props.handleBlur&&this.props.handleBlur(),this.props.addOnBlur&&this.handleDelimiter()},t.prototype.handleFocus=function(){this.setState({focused:!0}),this.props.handleFocus&&this.props.handleFocus()},t.prototype.addTag=function(e){e.disabled||("function"!=typeof this.props.handleValidate||this.props.handleValidate(e))&&(this.props.handleAddition(e),this.setState({query:"",selectedIndex:-1}))},t.prototype.deleteTag=function(e){this.props.handleDelete(e),this.props.clearInputOnDelete&&""!==this.state.query&&this.setState({query:""})},t.prototype.render=function(){var e=this,t=this.props.tagComponent||a,n=this.props.tags.map((function(n,i){return r.createElement(t,{key:i,tag:n,classNames:e.state.classNames,onDelete:e.deleteTag.bind(e,i)})})),i=this.state.focused&&this.state.query.length>=this.props.minQueryLength,u=[this.state.classNames.root];return this.state.focused&&u.push(this.state.classNames.rootFocused),r.createElement("div",{className:u.join(" "),onClick:this.handleClick.bind(this)},r.createElement("div",{className:this.state.classNames.selected,"aria-live":"polite","aria-relevant":"additions removals"},n),r.createElement("div",{className:this.state.classNames.search},r.createElement(o,Object.assign({},this.state,{inputAttributes:this.props.inputAttributes,inputEventHandlers:this.inputEventHandlers,ref:function(t){e.input=t},listboxId:"ReactTags-listbox",autofocus:this.props.autofocus,autoresize:this.props.autoresize,expandable:i,placeholder:this.props.placeholder})),r.createElement(l,Object.assign({},this.state,{ref:function(t){e.suggestions=t},listboxId:"ReactTags-listbox",expandable:i,noSuggestionsText:this.props.noSuggestionsText,suggestions:this.props.suggestions,suggestionsFilter:this.props.suggestionsFilter,addTag:this.addTag.bind(this),maxSuggestionsLength:this.props.maxSuggestionsLength}))))},t}(r.Component);p.defaultProps={tags:[],placeholder:"Add new tag",noSuggestionsText:null,suggestions:[],suggestionsFilter:null,autofocus:!0,autoresize:!0,delimiters:[s,u],delimiterChars:[],minQueryLength:2,maxSuggestionsLength:6,allowNew:!1,allowBackspace:!0,tagComponent:null,inputAttributes:{},addOnBlur:!1,clearInputOnDelete:!0},p.propTypes={tags:i.arrayOf(i.object),placeholder:i.string,noSuggestionsText:i.string,suggestions:i.arrayOf(i.object),suggestionsFilter:i.func,autofocus:i.bool,autoresize:i.bool,delimiters:i.arrayOf(i.number),delimiterChars:i.arrayOf(i.string),handleDelete:i.func.isRequired,handleAddition:i.func.isRequired,handleInputChange:i.func,handleFocus:i.func,handleBlur:i.func,handleValidate:i.func,minQueryLength:i.number,maxSuggestionsLength:i.number,classNames:i.object,allowNew:i.bool,allowBackspace:i.bool,tagComponent:i.oneOfType([i.func,i.element]),inputAttributes:i.object,addOnBlur:i.bool,clearInputOnDelete:i.bool},e.exports=p},function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Chart",{enumerable:!0,get:function(){return a.default}}),t.defaults=t.Scatter=t.Bubble=t.Polar=t.Radar=t.HorizontalBar=t.Bar=t.Line=t.Pie=t.Doughnut=t.default=void 0;var r=u(n(0)),i=u(n(27)),a=u(n(60)),o=u(n(62)),l=u(n(124));function u(e){return e&&e.__esModule?e:{default:e}}function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function g(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function v(e,t,n){return t&&g(e.prototype,t),n&&g(e,n),e}function m(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&b(e,t)}function b(e,t){return(b=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=w(e);if(t){var i=w(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return x(this,n)}}function x(e,t){return!t||"object"!==c(t)&&"function"!=typeof t?_(e):t}function _(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w(e){return(w=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var S=void 0!==e&&e.env&&"production",T=function(e){m(n,e);var t=y(n);function n(){var e;return p(this,n),k(_(e=t.call(this)),"handleOnClick",(function(t){var n=e.chartInstance,r=e.props,i=r.getDatasetAtEvent,a=r.getElementAtEvent,o=r.getElementsAtEvent,l=r.onElementsClick;i&&i(n.getDatasetAtEvent(t),t),a&&a(n.getElementAtEvent(t),t),o&&o(n.getElementsAtEvent(t),t),l&&l(n.getElementsAtEvent(t),t)})),k(_(e),"ref",(function(t){e.element=t})),e.chartInstance=void 0,e}return v(n,[{key:"componentDidMount",value:function(){this.renderChart()}},{key:"componentDidUpdate",value:function(){if(this.props.redraw)return this.destroyChart(),void this.renderChart();this.updateChart()}},{key:"shouldComponentUpdate",value:function(e){var t=this.props,n=(t.redraw,t.type),r=t.options,i=t.plugins,a=t.legend,l=t.height,u=t.width;if(!0===e.redraw)return!0;if(l!==e.height||u!==e.width)return!0;if(n!==e.type)return!0;if(!(0,o.default)(a,e.legend))return!0;if(!(0,o.default)(r,e.options))return!0;var s=this.transformDataProp(e);return!(0,o.default)(this.shadowDataProp,s)||!(0,o.default)(i,e.plugins)}},{key:"componentWillUnmount",value:function(){this.destroyChart()}},{key:"transformDataProp",value:function(e){var t=e.data;return"function"==typeof t?t(this.element):t}},{key:"memoizeDataProps",value:function(){if(this.props.data){var e=this.transformDataProp(this.props);return this.shadowDataProp=h(h({},e),{},{datasets:e.datasets&&e.datasets.map((function(e){return h({},e)}))}),this.saveCurrentDatasets(),e}}},{key:"checkDatasets",value:function(e){var t="production"!==S&&"prod"!==S,r=this.props.datasetKeyProvider!==n.getLabelAsKey,i=e.length>1;if(t&&i&&!r){var a=!1;e.forEach((function(e){e.label||(a=!0)})),a&&console.error('[react-chartjs-2] Warning: Each dataset needs a unique key. By default, the "label" property on each dataset is used. Alternatively, you may provide a "datasetKeyProvider" as a prop that returns a unique key.')}}},{key:"getCurrentDatasets",value:function(){return this.chartInstance&&this.chartInstance.config.data&&this.chartInstance.config.data.datasets||[]}},{key:"saveCurrentDatasets",value:function(){var e=this;this.datasets=this.datasets||{},this.getCurrentDatasets().forEach((function(t){e.datasets[e.props.datasetKeyProvider(t)]=t}))}},{key:"updateChart",value:function(){var e=this,t=this.props.options,n=this.memoizeDataProps(this.props);if(this.chartInstance){t&&(this.chartInstance.options=a.default.helpers.configMerge(this.chartInstance.options,t));var r=this.getCurrentDatasets(),i=n.datasets||[];this.checkDatasets(r);var o=(0,l.default)(r,this.props.datasetKeyProvider);this.chartInstance.config.data.datasets=i.map((function(t){var n=o[e.props.datasetKeyProvider(t)];if(n&&n.type===t.type&&t.data){n.data.splice(t.data.length),t.data.forEach((function(e,r){n.data[r]=t.data[r]}));t.data;var r=f(t,["data"]);return h(h({},n),r)}return t}));n.datasets;var u=f(n,["datasets"]);this.chartInstance.config.data=h(h({},this.chartInstance.config.data),u),this.chartInstance.update()}}},{key:"renderChart",value:function(){var e=this.props,t=e.options,r=e.legend,i=e.type,l=e.plugins,u=this.element,s=this.memoizeDataProps();void 0===r||(0,o.default)(n.defaultProps.legend,r)||(t.legend=r),this.chartInstance=new a.default(u,{type:i,data:s,options:t,plugins:l})}},{key:"destroyChart",value:function(){if(this.chartInstance){this.saveCurrentDatasets();var e=Object.values(this.datasets);this.chartInstance.config.data.datasets=e,this.chartInstance.destroy()}}},{key:"render",value:function(){var e=this.props,t=e.height,n=e.width,i=e.id;return r.default.createElement("canvas",{ref:this.ref,height:t,width:n,id:i,onClick:this.handleOnClick})}}]),n}(r.default.Component);k(T,"getLabelAsKey",(function(e){return e.label})),k(T,"propTypes",{data:i.default.oneOfType([i.default.object,i.default.func]).isRequired,getDatasetAtEvent:i.default.func,getElementAtEvent:i.default.func,getElementsAtEvent:i.default.func,height:i.default.number,legend:i.default.object,onElementsClick:i.default.func,options:i.default.object,plugins:i.default.arrayOf(i.default.object),redraw:i.default.bool,type:function(e,t,n){if(!a.default.controllers[e[t]])return new Error("Invalid chart type `"+e[t]+"` supplied to `"+n+"`.")},width:i.default.number,datasetKeyProvider:i.default.func}),k(T,"defaultProps",{legend:{display:!0,position:"bottom"},type:"doughnut",height:150,width:300,redraw:!1,options:{},datasetKeyProvider:T.getLabelAsKey});var E=T;t.default=E;var C=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"doughnut"}))}}]),n}(r.default.Component);t.Doughnut=C;var P=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"pie"}))}}]),n}(r.default.Component);t.Pie=P;var M=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"line"}))}}]),n}(r.default.Component);t.Line=M;var O=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"bar"}))}}]),n}(r.default.Component);t.Bar=O;var I=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"horizontalBar"}))}}]),n}(r.default.Component);t.HorizontalBar=I;var A=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"radar"}))}}]),n}(r.default.Component);t.Radar=A;var D=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"polarArea"}))}}]),n}(r.default.Component);t.Polar=D;var N=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"bubble"}))}}]),n}(r.default.Component);t.Bubble=N;var z=function(e){m(n,e);var t=y(n);function n(){return p(this,n),t.apply(this,arguments)}return v(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,s({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"scatter"}))}}]),n}(r.default.Component);t.Scatter=z;var j=a.default.defaults;t.defaults=j}).call(this,n(59))},function(e,t,n){e.exports=n(157),e.exports.version=n(156).version},function(e,t,n){"use strict";
/** @license React v16.13.1
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(24),i="function"==typeof Symbol&&Symbol.for,a=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,l=i?Symbol.for("react.fragment"):60107,u=i?Symbol.for("react.strict_mode"):60108,s=i?Symbol.for("react.profiler"):60114,c=i?Symbol.for("react.provider"):60109,f=i?Symbol.for("react.context"):60110,d=i?Symbol.for("react.forward_ref"):60112,h=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,g=i?Symbol.for("react.lazy"):60116,v="function"==typeof Symbol&&Symbol.iterator;function m(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var b={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},y={};function x(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||b}function _(){}function w(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||b}x.prototype.isReactComponent={},x.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(m(85));this.updater.enqueueSetState(this,e,t,"setState")},x.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},_.prototype=x.prototype;var k=w.prototype=new _;k.constructor=w,r(k,x.prototype),k.isPureReactComponent=!0;var S={current:null},T=Object.prototype.hasOwnProperty,E={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,n){var r,i={},o=null,l=null;if(null!=t)for(r in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(o=""+t.key),t)T.call(t,r)&&!E.hasOwnProperty(r)&&(i[r]=t[r]);var u=arguments.length-2;if(1===u)i.children=n;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];i.children=s}if(e&&e.defaultProps)for(r in u=e.defaultProps)void 0===i[r]&&(i[r]=u[r]);return{$$typeof:a,type:e,key:o,ref:l,props:i,_owner:S.current}}function P(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var M=/\/+/g,O=[];function I(e,t,n,r){if(O.length){var i=O.pop();return i.result=e,i.keyPrefix=t,i.func=n,i.context=r,i.count=0,i}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function A(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>O.length&&O.push(e)}function D(e,t,n){return null==e?0:function e(t,n,r,i){var l=typeof t;"undefined"!==l&&"boolean"!==l||(t=null);var u=!1;if(null===t)u=!0;else switch(l){case"string":case"number":u=!0;break;case"object":switch(t.$$typeof){case a:case o:u=!0}}if(u)return r(i,t,""===n?"."+N(t,0):n),1;if(u=0,n=""===n?".":n+":",Array.isArray(t))for(var s=0;s<t.length;s++){var c=n+N(l=t[s],s);u+=e(l,c,r,i)}else if(null===t||"object"!=typeof t?c=null:c="function"==typeof(c=v&&t[v]||t["@@iterator"])?c:null,"function"==typeof c)for(t=c.call(t),s=0;!(l=t.next()).done;)u+=e(l=l.value,c=n+N(l,s++),r,i);else if("object"===l)throw r=""+t,Error(m(31,"[object Object]"===r?"object with keys {"+Object.keys(t).join(", ")+"}":r,""));return u}(e,"",t,n)}function N(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function z(e,t){e.func.call(e.context,t,e.count++)}function j(e,t,n){var r=e.result,i=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?R(e,r,n,(function(e){return e})):null!=e&&(P(e)&&(e=function(e,t){return{$$typeof:a,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,i+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(M,"$&/")+"/")+n)),r.push(e))}function R(e,t,n,r,i){var a="";null!=n&&(a=(""+n).replace(M,"$&/")+"/"),D(e,j,t=I(t,a,r,i)),A(t)}var F={current:null};function L(){var e=F.current;if(null===e)throw Error(m(321));return e}var B={ReactCurrentDispatcher:F,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:S,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:function(e,t,n){if(null==e)return e;var r=[];return R(e,r,null,t,n),r},forEach:function(e,t,n){if(null==e)return e;D(e,z,t=I(null,null,t,n)),A(t)},count:function(e){return D(e,(function(){return null}),null)},toArray:function(e){var t=[];return R(e,t,null,(function(e){return e})),t},only:function(e){if(!P(e))throw Error(m(143));return e}},t.Component=x,t.Fragment=l,t.Profiler=s,t.PureComponent=w,t.StrictMode=u,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B,t.cloneElement=function(e,t,n){if(null==e)throw Error(m(267,e));var i=r({},e.props),o=e.key,l=e.ref,u=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,u=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)T.call(t,c)&&!E.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){s=Array(c);for(var f=0;f<c;f++)s[f]=arguments[f+2];i.children=s}return{$$typeof:a,type:e.type,key:o,ref:l,props:i,_owner:u}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:f,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:d,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:g,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return L().useCallback(e,t)},t.useContext=function(e,t){return L().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return L().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return L().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return L().useLayoutEffect(e,t)},t.useMemo=function(e,t){return L().useMemo(e,t)},t.useReducer=function(e,t,n){return L().useReducer(e,t,n)},t.useRef=function(e){return L().useRef(e)},t.useState=function(e){return L().useState(e)},t.version="16.13.1"},function(e,t,n){"use strict";
/** @license React v16.13.1
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(0),i=n(24),a=n(50);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!r)throw Error(o(227));function l(e,t,n,r,i,a,o,l,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(e){this.onError(e)}}var u=!1,s=null,c=!1,f=null,d={onError:function(e){u=!0,s=e}};function h(e,t,n,r,i,a,o,c,f){u=!1,s=null,l.apply(d,arguments)}var p=null,g=null,v=null;function m(e,t,n){var r=e.type||"unknown-event";e.currentTarget=v(n),function(e,t,n,r,i,a,l,d,p){if(h.apply(this,arguments),u){if(!u)throw Error(o(198));var g=s;u=!1,s=null,c||(c=!0,f=g)}}(r,t,void 0,e),e.currentTarget=null}var b=null,y={};function x(){if(b)for(var e in y){var t=y[e],n=b.indexOf(e);if(!(-1<n))throw Error(o(96,e));if(!w[n]){if(!t.extractEvents)throw Error(o(97,e));for(var r in w[n]=t,n=t.eventTypes){var i=void 0,a=n[r],l=t,u=r;if(k.hasOwnProperty(u))throw Error(o(99,u));k[u]=a;var s=a.phasedRegistrationNames;if(s){for(i in s)s.hasOwnProperty(i)&&_(s[i],l,u);i=!0}else a.registrationName?(_(a.registrationName,l,u),i=!0):i=!1;if(!i)throw Error(o(98,r,e))}}}}function _(e,t,n){if(S[e])throw Error(o(100,e));S[e]=t,T[e]=t.eventTypes[n].dependencies}var w=[],k={},S={},T={};function E(e){var t,n=!1;for(t in e)if(e.hasOwnProperty(t)){var r=e[t];if(!y.hasOwnProperty(t)||y[t]!==r){if(y[t])throw Error(o(102,t));y[t]=r,n=!0}}n&&x()}var C=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),P=null,M=null,O=null;function I(e){if(e=g(e)){if("function"!=typeof P)throw Error(o(280));var t=e.stateNode;t&&(t=p(t),P(e.stateNode,e.type,t))}}function A(e){M?O?O.push(e):O=[e]:M=e}function D(){if(M){var e=M,t=O;if(O=M=null,I(e),t)for(e=0;e<t.length;e++)I(t[e])}}function N(e,t){return e(t)}function z(e,t,n,r,i){return e(t,n,r,i)}function j(){}var R=N,F=!1,L=!1;function B(){null===M&&null===O||(j(),D())}function W(e,t,n){if(L)return e(t,n);L=!0;try{return R(e,t,n)}finally{L=!1,B()}}var V=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,U=Object.prototype.hasOwnProperty,H={},q={};function $(e,t,n,r,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a}var Q={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){Q[e]=new $(e,0,!1,e,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];Q[t]=new $(t,1,!1,e[1],null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){Q[e]=new $(e,2,!1,e.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){Q[e]=new $(e,2,!1,e,null,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){Q[e]=new $(e,3,!1,e.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(e){Q[e]=new $(e,3,!0,e,null,!1)})),["capture","download"].forEach((function(e){Q[e]=new $(e,4,!1,e,null,!1)})),["cols","rows","size","span"].forEach((function(e){Q[e]=new $(e,6,!1,e,null,!1)})),["rowSpan","start"].forEach((function(e){Q[e]=new $(e,5,!1,e.toLowerCase(),null,!1)}));var Y=/[\-:]([a-z])/g;function K(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(Y,K);Q[t]=new $(t,1,!1,e,null,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(Y,K);Q[t]=new $(t,1,!1,e,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(Y,K);Q[t]=new $(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(e){Q[e]=new $(e,1,!1,e.toLowerCase(),null,!1)})),Q.xlinkHref=new $("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(e){Q[e]=new $(e,1,!1,e.toLowerCase(),null,!0)}));var G=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function Z(e,t,n,r){var i=Q.hasOwnProperty(t)?Q[t]:null;(null!==i?0===i.type:!r&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!U.call(q,e)||!U.call(H,e)&&(V.test(e)?q[e]=!0:(H[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}G.hasOwnProperty("ReactCurrentDispatcher")||(G.ReactCurrentDispatcher={current:null}),G.hasOwnProperty("ReactCurrentBatchConfig")||(G.ReactCurrentBatchConfig={suspense:null});var X=/^(.*)[\\\/]/,J="function"==typeof Symbol&&Symbol.for,ee=J?Symbol.for("react.element"):60103,te=J?Symbol.for("react.portal"):60106,ne=J?Symbol.for("react.fragment"):60107,re=J?Symbol.for("react.strict_mode"):60108,ie=J?Symbol.for("react.profiler"):60114,ae=J?Symbol.for("react.provider"):60109,oe=J?Symbol.for("react.context"):60110,le=J?Symbol.for("react.concurrent_mode"):60111,ue=J?Symbol.for("react.forward_ref"):60112,se=J?Symbol.for("react.suspense"):60113,ce=J?Symbol.for("react.suspense_list"):60120,fe=J?Symbol.for("react.memo"):60115,de=J?Symbol.for("react.lazy"):60116,he=J?Symbol.for("react.block"):60121,pe="function"==typeof Symbol&&Symbol.iterator;function ge(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=pe&&e[pe]||e["@@iterator"])?e:null}function ve(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case ne:return"Fragment";case te:return"Portal";case ie:return"Profiler";case re:return"StrictMode";case se:return"Suspense";case ce:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case oe:return"Context.Consumer";case ae:return"Context.Provider";case ue:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case fe:return ve(e.type);case he:return ve(e.render);case de:if(e=1===e._status?e._result:null)return ve(e)}return null}function me(e){var t="";do{e:switch(e.tag){case 3:case 4:case 6:case 7:case 10:case 9:var n="";break e;default:var r=e._debugOwner,i=e._debugSource,a=ve(e.type);n=null,r&&(n=ve(r.type)),r=a,a="",i?a=" (at "+i.fileName.replace(X,"")+":"+i.lineNumber+")":n&&(a=" (created by "+n+")"),n="\n    in "+(r||"Unknown")+a}t+=n,e=e.return}while(e);return t}function be(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function ye(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function xe(e){e._valueTracker||(e._valueTracker=function(e){var t=ye(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function _e(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ye(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function we(e,t){var n=t.checked;return i({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function ke(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=be(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Se(e,t){null!=(t=t.checked)&&Z(e,"checked",t,!1)}function Te(e,t){Se(e,t);var n=be(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?Ce(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ce(e,t.type,be(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Ee(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function Ce(e,t,n){"number"===t&&e.ownerDocument.activeElement===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}function Pe(e,t){return e=i({children:void 0},t),(t=function(e){var t="";return r.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(t.children))&&(e.children=t),e}function Me(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+be(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function Oe(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return i({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:be(n)}}function Ae(e,t){var n=be(t.value),r=be(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function De(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var Ne="http://www.w3.org/1999/xhtml",ze="http://www.w3.org/2000/svg";function je(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Re(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?je(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var Fe,Le=function(e){return"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if(e.namespaceURI!==ze||"innerHTML"in e)e.innerHTML=t;else{for((Fe=Fe||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Fe.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function Be(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}function We(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ve={animationend:We("Animation","AnimationEnd"),animationiteration:We("Animation","AnimationIteration"),animationstart:We("Animation","AnimationStart"),transitionend:We("Transition","TransitionEnd")},Ue={},He={};function qe(e){if(Ue[e])return Ue[e];if(!Ve[e])return e;var t,n=Ve[e];for(t in n)if(n.hasOwnProperty(t)&&t in He)return Ue[e]=n[t];return e}C&&(He=document.createElement("div").style,"AnimationEvent"in window||(delete Ve.animationend.animation,delete Ve.animationiteration.animation,delete Ve.animationstart.animation),"TransitionEvent"in window||delete Ve.transitionend.transition);var $e=qe("animationend"),Qe=qe("animationiteration"),Ye=qe("animationstart"),Ke=qe("transitionend"),Ge="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ze=new("function"==typeof WeakMap?WeakMap:Map);function Xe(e){var t=Ze.get(e);return void 0===t&&(t=new Map,Ze.set(e,t)),t}function Je(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!=(1026&(t=e).effectTag)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function et(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function tt(e){if(Je(e)!==e)throw Error(o(188))}function nt(e){if(!(e=function(e){var t=e.alternate;if(!t){if(null===(t=Je(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return tt(i),e;if(a===r)return tt(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var l=!1,u=i.child;u;){if(u===n){l=!0,n=i,r=a;break}if(u===r){l=!0,r=i,n=a;break}u=u.sibling}if(!l){for(u=a.child;u;){if(u===n){l=!0,n=a,r=i;break}if(u===r){l=!0,r=a,n=i;break}u=u.sibling}if(!l)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function rt(e,t){if(null==t)throw Error(o(30));return null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function it(e,t,n){Array.isArray(e)?e.forEach(t,n):e&&t.call(n,e)}var at=null;function ot(e){if(e){var t=e._dispatchListeners,n=e._dispatchInstances;if(Array.isArray(t))for(var r=0;r<t.length&&!e.isPropagationStopped();r++)m(e,t[r],n[r]);else t&&m(e,t,n);e._dispatchListeners=null,e._dispatchInstances=null,e.isPersistent()||e.constructor.release(e)}}function lt(e){if(null!==e&&(at=rt(at,e)),e=at,at=null,e){if(it(e,ot),at)throw Error(o(95));if(c)throw e=f,c=!1,f=null,e}}function ut(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}function st(e){if(!C)return!1;var t=(e="on"+e)in document;return t||((t=document.createElement("div")).setAttribute(e,"return;"),t="function"==typeof t[e]),t}var ct=[];function ft(e){e.topLevelType=null,e.nativeEvent=null,e.targetInst=null,e.ancestors.length=0,10>ct.length&&ct.push(e)}function dt(e,t,n,r){if(ct.length){var i=ct.pop();return i.topLevelType=e,i.eventSystemFlags=r,i.nativeEvent=t,i.targetInst=n,i}return{topLevelType:e,eventSystemFlags:r,nativeEvent:t,targetInst:n,ancestors:[]}}function ht(e){var t=e.targetInst,n=t;do{if(!n){e.ancestors.push(n);break}var r=n;if(3===r.tag)r=r.stateNode.containerInfo;else{for(;r.return;)r=r.return;r=3!==r.tag?null:r.stateNode.containerInfo}if(!r)break;5!==(t=n.tag)&&6!==t||e.ancestors.push(n),n=Cn(r)}while(n);for(n=0;n<e.ancestors.length;n++){t=e.ancestors[n];var i=ut(e.nativeEvent);r=e.topLevelType;var a=e.nativeEvent,o=e.eventSystemFlags;0===n&&(o|=64);for(var l=null,u=0;u<w.length;u++){var s=w[u];s&&(s=s.extractEvents(r,t,a,i,o))&&(l=rt(l,s))}lt(l)}}function pt(e,t,n){if(!n.has(e)){switch(e){case"scroll":Yt(t,"scroll",!0);break;case"focus":case"blur":Yt(t,"focus",!0),Yt(t,"blur",!0),n.set("blur",null),n.set("focus",null);break;case"cancel":case"close":st(e)&&Yt(t,e,!0);break;case"invalid":case"submit":case"reset":break;default:-1===Ge.indexOf(e)&&Qt(e,t)}n.set(e,null)}}var gt,vt,mt,bt=!1,yt=[],xt=null,_t=null,wt=null,kt=new Map,St=new Map,Tt=[],Et="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),Ct="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" ");function Pt(e,t,n,r,i){return{blockedOn:e,topLevelType:t,eventSystemFlags:32|n,nativeEvent:i,container:r}}function Mt(e,t){switch(e){case"focus":case"blur":xt=null;break;case"dragenter":case"dragleave":_t=null;break;case"mouseover":case"mouseout":wt=null;break;case"pointerover":case"pointerout":kt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":St.delete(t.pointerId)}}function Ot(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e=Pt(t,n,r,i,a),null!==t&&(null!==(t=Pn(t))&&vt(t)),e):(e.eventSystemFlags|=r,e)}function It(e){var t=Cn(e.target);if(null!==t){var n=Je(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=et(n)))return e.blockedOn=t,void a.unstable_runWithPriority(e.priority,(function(){mt(n)}))}else if(3===t&&n.stateNode.hydrate)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function At(e){if(null!==e.blockedOn)return!1;var t=Xt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);if(null!==t){var n=Pn(t);return null!==n&&vt(n),e.blockedOn=t,!1}return!0}function Dt(e,t,n){At(e)&&n.delete(t)}function Nt(){for(bt=!1;0<yt.length;){var e=yt[0];if(null!==e.blockedOn){null!==(e=Pn(e.blockedOn))&&gt(e);break}var t=Xt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);null!==t?e.blockedOn=t:yt.shift()}null!==xt&&At(xt)&&(xt=null),null!==_t&&At(_t)&&(_t=null),null!==wt&&At(wt)&&(wt=null),kt.forEach(Dt),St.forEach(Dt)}function zt(e,t){e.blockedOn===t&&(e.blockedOn=null,bt||(bt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Nt)))}function jt(e){function t(t){return zt(t,e)}if(0<yt.length){zt(yt[0],e);for(var n=1;n<yt.length;n++){var r=yt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==xt&&zt(xt,e),null!==_t&&zt(_t,e),null!==wt&&zt(wt,e),kt.forEach(t),St.forEach(t),n=0;n<Tt.length;n++)(r=Tt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&null===(n=Tt[0]).blockedOn;)It(n),null===n.blockedOn&&Tt.shift()}var Rt={},Ft=new Map,Lt=new Map,Bt=["abort","abort",$e,"animationEnd",Qe,"animationIteration",Ye,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Ke,"transitionEnd","waiting","waiting"];function Wt(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1],a="on"+(i[0].toUpperCase()+i.slice(1));a={phasedRegistrationNames:{bubbled:a,captured:a+"Capture"},dependencies:[r],eventPriority:t},Lt.set(r,t),Ft.set(r,a),Rt[i]=a}}Wt("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),Wt("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),Wt(Bt,2);for(var Vt="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),Ut=0;Ut<Vt.length;Ut++)Lt.set(Vt[Ut],0);var Ht=a.unstable_UserBlockingPriority,qt=a.unstable_runWithPriority,$t=!0;function Qt(e,t){Yt(t,e,!1)}function Yt(e,t,n){var r=Lt.get(t);switch(void 0===r?2:r){case 0:r=Kt.bind(null,t,1,e);break;case 1:r=Gt.bind(null,t,1,e);break;default:r=Zt.bind(null,t,1,e)}n?e.addEventListener(t,r,!0):e.addEventListener(t,r,!1)}function Kt(e,t,n,r){F||j();var i=Zt,a=F;F=!0;try{z(i,e,t,n,r)}finally{(F=a)||B()}}function Gt(e,t,n,r){qt(Ht,Zt.bind(null,e,t,n,r))}function Zt(e,t,n,r){if($t)if(0<yt.length&&-1<Et.indexOf(e))e=Pt(null,e,t,n,r),yt.push(e);else{var i=Xt(e,t,n,r);if(null===i)Mt(e,r);else if(-1<Et.indexOf(e))e=Pt(i,e,t,n,r),yt.push(e);else if(!function(e,t,n,r,i){switch(t){case"focus":return xt=Ot(xt,e,t,n,r,i),!0;case"dragenter":return _t=Ot(_t,e,t,n,r,i),!0;case"mouseover":return wt=Ot(wt,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return kt.set(a,Ot(kt.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,St.set(a,Ot(St.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r)){Mt(e,r),e=dt(e,r,null,t);try{W(ht,e)}finally{ft(e)}}}}function Xt(e,t,n,r){if(null!==(n=Cn(n=ut(r)))){var i=Je(n);if(null===i)n=null;else{var a=i.tag;if(13===a){if(null!==(n=et(i)))return n;n=null}else if(3===a){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;n=null}else i!==n&&(n=null)}}e=dt(e,r,n,t);try{W(ht,e)}finally{ft(e)}return null}var Jt={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},en=["Webkit","ms","Moz","O"];function tn(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||Jt.hasOwnProperty(e)&&Jt[e]?(""+t).trim():t+"px"}function nn(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=tn(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(Jt).forEach((function(e){en.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Jt[t]=Jt[e]}))}));var rn=i({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function an(e,t){if(t){if(rn[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e,""));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(o(62,""))}}function on(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ln=Ne;function un(e,t){var n=Xe(e=9===e.nodeType||11===e.nodeType?e:e.ownerDocument);t=T[t];for(var r=0;r<t.length;r++)pt(t[r],e,n)}function sn(){}function cn(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function fn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dn(e,t){var n,r=fn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=fn(r)}}function hn(){for(var e=window,t=cn();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=cn((e=t.contentWindow).document)}return t}function pn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var gn=null,vn=null;function mn(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function bn(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var yn="function"==typeof setTimeout?setTimeout:void 0,xn="function"==typeof clearTimeout?clearTimeout:void 0;function _n(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function wn(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var kn=Math.random().toString(36).slice(2),Sn="__reactInternalInstance$"+kn,Tn="__reactEventHandlers$"+kn,En="__reactContainere$"+kn;function Cn(e){var t=e[Sn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[En]||n[Sn]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wn(e);null!==e;){if(n=e[Sn])return n;e=wn(e)}return t}n=(e=n).parentNode}return null}function Pn(e){return!(e=e[Sn]||e[En])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Mn(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function On(e){return e[Tn]||null}function In(e){do{e=e.return}while(e&&5!==e.tag);return e||null}function An(e,t){var n=e.stateNode;if(!n)return null;var r=p(n);if(!r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}function Dn(e,t,n){(t=An(e,n.dispatchConfig.phasedRegistrationNames[t]))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function Nn(e){if(e&&e.dispatchConfig.phasedRegistrationNames){for(var t=e._targetInst,n=[];t;)n.push(t),t=In(t);for(t=n.length;0<t--;)Dn(n[t],"captured",e);for(t=0;t<n.length;t++)Dn(n[t],"bubbled",e)}}function zn(e,t,n){e&&n&&n.dispatchConfig.registrationName&&(t=An(e,n.dispatchConfig.registrationName))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function jn(e){e&&e.dispatchConfig.registrationName&&zn(e._targetInst,null,e)}function Rn(e){it(e,Nn)}var Fn=null,Ln=null,Bn=null;function Wn(){if(Bn)return Bn;var e,t,n=Ln,r=n.length,i="value"in Fn?Fn.value:Fn.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return Bn=i.slice(e,1<t?1-t:void 0)}function Vn(){return!0}function Un(){return!1}function Hn(e,t,n,r){for(var i in this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=n,e=this.constructor.Interface)e.hasOwnProperty(i)&&((t=e[i])?this[i]=t(n):"target"===i?this.target=r:this[i]=n[i]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?Vn:Un,this.isPropagationStopped=Un,this}function qn(e,t,n,r){if(this.eventPool.length){var i=this.eventPool.pop();return this.call(i,e,t,n,r),i}return new this(e,t,n,r)}function $n(e){if(!(e instanceof this))throw Error(o(279));e.destructor(),10>this.eventPool.length&&this.eventPool.push(e)}function Qn(e){e.eventPool=[],e.getPooled=qn,e.release=$n}i(Hn.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vn)},persist:function(){this.isPersistent=Vn},isPersistent:Un,destructor:function(){var e,t=this.constructor.Interface;for(e in t)this[e]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=Un,this._dispatchInstances=this._dispatchListeners=null}}),Hn.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},Hn.extend=function(e){function t(){}function n(){return r.apply(this,arguments)}var r=this;t.prototype=r.prototype;var a=new t;return i(a,n.prototype),n.prototype=a,n.prototype.constructor=n,n.Interface=i({},r.Interface,e),n.extend=r.extend,Qn(n),n},Qn(Hn);var Yn=Hn.extend({data:null}),Kn=Hn.extend({data:null}),Gn=[9,13,27,32],Zn=C&&"CompositionEvent"in window,Xn=null;C&&"documentMode"in document&&(Xn=document.documentMode);var Jn=C&&"TextEvent"in window&&!Xn,er=C&&(!Zn||Xn&&8<Xn&&11>=Xn),tr=String.fromCharCode(32),nr={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},rr=!1;function ir(e,t){switch(e){case"keyup":return-1!==Gn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function ar(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var or=!1;var lr={eventTypes:nr,extractEvents:function(e,t,n,r){var i;if(Zn)e:{switch(e){case"compositionstart":var a=nr.compositionStart;break e;case"compositionend":a=nr.compositionEnd;break e;case"compositionupdate":a=nr.compositionUpdate;break e}a=void 0}else or?ir(e,n)&&(a=nr.compositionEnd):"keydown"===e&&229===n.keyCode&&(a=nr.compositionStart);return a?(er&&"ko"!==n.locale&&(or||a!==nr.compositionStart?a===nr.compositionEnd&&or&&(i=Wn()):(Ln="value"in(Fn=r)?Fn.value:Fn.textContent,or=!0)),a=Yn.getPooled(a,t,n,r),i?a.data=i:null!==(i=ar(n))&&(a.data=i),Rn(a),i=a):i=null,(e=Jn?function(e,t){switch(e){case"compositionend":return ar(t);case"keypress":return 32!==t.which?null:(rr=!0,tr);case"textInput":return(e=t.data)===tr&&rr?null:e;default:return null}}(e,n):function(e,t){if(or)return"compositionend"===e||!Zn&&ir(e,t)?(e=Wn(),Bn=Ln=Fn=null,or=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return er&&"ko"!==t.locale?null:t.data;default:return null}}(e,n))?((t=Kn.getPooled(nr.beforeInput,t,n,r)).data=e,Rn(t)):t=null,null===i?t:null===t?i:[i,t]}},ur={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function sr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!ur[e.type]:"textarea"===t}var cr={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function fr(e,t,n){return(e=Hn.getPooled(cr.change,e,t,n)).type="change",A(n),Rn(e),e}var dr=null,hr=null;function pr(e){lt(e)}function gr(e){if(_e(Mn(e)))return e}function vr(e,t){if("change"===e)return t}var mr=!1;function br(){dr&&(dr.detachEvent("onpropertychange",yr),hr=dr=null)}function yr(e){if("value"===e.propertyName&&gr(hr))if(e=fr(hr,e,ut(e)),F)lt(e);else{F=!0;try{N(pr,e)}finally{F=!1,B()}}}function xr(e,t,n){"focus"===e?(br(),hr=n,(dr=t).attachEvent("onpropertychange",yr)):"blur"===e&&br()}function _r(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return gr(hr)}function wr(e,t){if("click"===e)return gr(t)}function kr(e,t){if("input"===e||"change"===e)return gr(t)}C&&(mr=st("input")&&(!document.documentMode||9<document.documentMode));var Sr={eventTypes:cr,_isInputEventSupported:mr,extractEvents:function(e,t,n,r){var i=t?Mn(t):window,a=i.nodeName&&i.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===i.type)var o=vr;else if(sr(i))if(mr)o=kr;else{o=_r;var l=xr}else(a=i.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(o=wr);if(o&&(o=o(e,t)))return fr(o,n,r);l&&l(e,i,t),"blur"===e&&(e=i._wrapperState)&&e.controlled&&"number"===i.type&&Ce(i,"number",i.value)}},Tr=Hn.extend({view:null,detail:null}),Er={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cr(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Er[e])&&!!t[e]}function Pr(){return Cr}var Mr=0,Or=0,Ir=!1,Ar=!1,Dr=Tr.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Pr,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)},movementX:function(e){if("movementX"in e)return e.movementX;var t=Mr;return Mr=e.screenX,Ir?"mousemove"===e.type?e.screenX-t:0:(Ir=!0,0)},movementY:function(e){if("movementY"in e)return e.movementY;var t=Or;return Or=e.screenY,Ar?"mousemove"===e.type?e.screenY-t:0:(Ar=!0,0)}}),Nr=Dr.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),zr={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},jr={eventTypes:zr,extractEvents:function(e,t,n,r,i){var a="mouseover"===e||"pointerover"===e,o="mouseout"===e||"pointerout"===e;if(a&&0==(32&i)&&(n.relatedTarget||n.fromElement)||!o&&!a)return null;(a=r.window===r?r:(a=r.ownerDocument)?a.defaultView||a.parentWindow:window,o)?(o=t,null!==(t=(t=n.relatedTarget||n.toElement)?Cn(t):null)&&(t!==Je(t)||5!==t.tag&&6!==t.tag)&&(t=null)):o=null;if(o===t)return null;if("mouseout"===e||"mouseover"===e)var l=Dr,u=zr.mouseLeave,s=zr.mouseEnter,c="mouse";else"pointerout"!==e&&"pointerover"!==e||(l=Nr,u=zr.pointerLeave,s=zr.pointerEnter,c="pointer");if(e=null==o?a:Mn(o),a=null==t?a:Mn(t),(u=l.getPooled(u,o,n,r)).type=c+"leave",u.target=e,u.relatedTarget=a,(n=l.getPooled(s,t,n,r)).type=c+"enter",n.target=a,n.relatedTarget=e,c=t,(r=o)&&c)e:{for(s=c,o=0,e=l=r;e;e=In(e))o++;for(e=0,t=s;t;t=In(t))e++;for(;0<o-e;)l=In(l),o--;for(;0<e-o;)s=In(s),e--;for(;o--;){if(l===s||l===s.alternate)break e;l=In(l),s=In(s)}l=null}else l=null;for(s=l,l=[];r&&r!==s&&(null===(o=r.alternate)||o!==s);)l.push(r),r=In(r);for(r=[];c&&c!==s&&(null===(o=c.alternate)||o!==s);)r.push(c),c=In(c);for(c=0;c<l.length;c++)zn(l[c],"bubbled",u);for(c=r.length;0<c--;)zn(r[c],"captured",n);return 0==(64&i)?[u]:[u,n]}};var Rr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},Fr=Object.prototype.hasOwnProperty;function Lr(e,t){if(Rr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!Fr.call(t,n[r])||!Rr(e[n[r]],t[n[r]]))return!1;return!0}var Br=C&&"documentMode"in document&&11>=document.documentMode,Wr={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},Vr=null,Ur=null,Hr=null,qr=!1;function $r(e,t){var n=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;return qr||null==Vr||Vr!==cn(n)?null:("selectionStart"in(n=Vr)&&pn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},Hr&&Lr(Hr,n)?null:(Hr=n,(e=Hn.getPooled(Wr.select,Ur,e,t)).type="select",e.target=Vr,Rn(e),e))}var Qr={eventTypes:Wr,extractEvents:function(e,t,n,r,i,a){if(!(a=!(i=a||(r.window===r?r.document:9===r.nodeType?r:r.ownerDocument)))){e:{i=Xe(i),a=T.onSelect;for(var o=0;o<a.length;o++)if(!i.has(a[o])){i=!1;break e}i=!0}a=!i}if(a)return null;switch(i=t?Mn(t):window,e){case"focus":(sr(i)||"true"===i.contentEditable)&&(Vr=i,Ur=t,Hr=null);break;case"blur":Hr=Ur=Vr=null;break;case"mousedown":qr=!0;break;case"contextmenu":case"mouseup":case"dragend":return qr=!1,$r(n,r);case"selectionchange":if(Br)break;case"keydown":case"keyup":return $r(n,r)}return null}},Yr=Hn.extend({animationName:null,elapsedTime:null,pseudoElement:null}),Kr=Hn.extend({clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Gr=Tr.extend({relatedTarget:null});function Zr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}var Xr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ei=Tr.extend({key:function(e){if(e.key){var t=Xr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Zr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Jr[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Pr,charCode:function(e){return"keypress"===e.type?Zr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Zr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),ti=Dr.extend({dataTransfer:null}),ni=Tr.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Pr}),ri=Hn.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),ii=Dr.extend({deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null}),ai={eventTypes:Rt,extractEvents:function(e,t,n,r){var i=Ft.get(e);if(!i)return null;switch(e){case"keypress":if(0===Zr(n))return null;case"keydown":case"keyup":e=ei;break;case"blur":case"focus":e=Gr;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":e=Dr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":e=ti;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":e=ni;break;case $e:case Qe:case Ye:e=Yr;break;case Ke:e=ri;break;case"scroll":e=Tr;break;case"wheel":e=ii;break;case"copy":case"cut":case"paste":e=Kr;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":e=Nr;break;default:e=Hn}return Rn(t=e.getPooled(i,t,n,r)),t}};if(b)throw Error(o(101));b=Array.prototype.slice.call("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),x(),p=On,g=Pn,v=Mn,E({SimpleEventPlugin:ai,EnterLeaveEventPlugin:jr,ChangeEventPlugin:Sr,SelectEventPlugin:Qr,BeforeInputEventPlugin:lr});var oi=[],li=-1;function ui(e){0>li||(e.current=oi[li],oi[li]=null,li--)}function si(e,t){li++,oi[li]=e.current,e.current=t}var ci={},fi={current:ci},di={current:!1},hi=ci;function pi(e,t){var n=e.type.contextTypes;if(!n)return ci;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in n)a[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function gi(e){return null!=(e=e.childContextTypes)}function vi(){ui(di),ui(fi)}function mi(e,t,n){if(fi.current!==ci)throw Error(o(168));si(fi,t),si(di,n)}function bi(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in e))throw Error(o(108,ve(t)||"Unknown",a));return i({},n,{},r)}function yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ci,hi=fi.current,si(fi,e),si(di,di.current),!0}function xi(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=bi(e,t,hi),r.__reactInternalMemoizedMergedChildContext=e,ui(di),ui(fi),si(fi,e)):ui(di),si(di,n)}var _i=a.unstable_runWithPriority,wi=a.unstable_scheduleCallback,ki=a.unstable_cancelCallback,Si=a.unstable_requestPaint,Ti=a.unstable_now,Ei=a.unstable_getCurrentPriorityLevel,Ci=a.unstable_ImmediatePriority,Pi=a.unstable_UserBlockingPriority,Mi=a.unstable_NormalPriority,Oi=a.unstable_LowPriority,Ii=a.unstable_IdlePriority,Ai={},Di=a.unstable_shouldYield,Ni=void 0!==Si?Si:function(){},zi=null,ji=null,Ri=!1,Fi=Ti(),Li=1e4>Fi?Ti:function(){return Ti()-Fi};function Bi(){switch(Ei()){case Ci:return 99;case Pi:return 98;case Mi:return 97;case Oi:return 96;case Ii:return 95;default:throw Error(o(332))}}function Wi(e){switch(e){case 99:return Ci;case 98:return Pi;case 97:return Mi;case 96:return Oi;case 95:return Ii;default:throw Error(o(332))}}function Vi(e,t){return e=Wi(e),_i(e,t)}function Ui(e,t,n){return e=Wi(e),wi(e,t,n)}function Hi(e){return null===zi?(zi=[e],ji=wi(Ci,$i)):zi.push(e),Ai}function qi(){if(null!==ji){var e=ji;ji=null,ki(e)}$i()}function $i(){if(!Ri&&null!==zi){Ri=!0;var e=0;try{var t=zi;Vi(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),zi=null}catch(t){throw null!==zi&&(zi=zi.slice(e+1)),wi(Ci,qi),t}finally{Ri=!1}}}function Qi(e,t,n){return 1073741821-(1+((1073741821-e+t/10)/(n/=10)|0))*n}function Yi(e,t){if(e&&e.defaultProps)for(var n in t=i({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var Ki={current:null},Gi=null,Zi=null,Xi=null;function Ji(){Xi=Zi=Gi=null}function ea(e){var t=Ki.current;ui(Ki),e.type._context._currentValue=t}function ta(e,t){for(;null!==e;){var n=e.alternate;if(e.childExpirationTime<t)e.childExpirationTime=t,null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t);else{if(!(null!==n&&n.childExpirationTime<t))break;n.childExpirationTime=t}e=e.return}}function na(e,t){Gi=e,Xi=Zi=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(e.expirationTime>=t&&(Oo=!0),e.firstContext=null)}function ra(e,t){if(Xi!==e&&!1!==t&&0!==t)if("number"==typeof t&&1073741823!==t||(Xi=e,t=1073741823),t={context:e,observedBits:t,next:null},null===Zi){if(null===Gi)throw Error(o(308));Zi=t,Gi.dependencies={expirationTime:0,firstContext:t,responders:null}}else Zi=Zi.next=t;return e._currentValue}var ia=!1;function aa(e){e.updateQueue={baseState:e.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function oa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,baseQueue:e.baseQueue,shared:e.shared,effects:e.effects})}function la(e,t){return(e={expirationTime:e,suspenseConfig:t,tag:0,payload:null,callback:null,next:null}).next=e}function ua(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function sa(e,t){var n=e.alternate;null!==n&&oa(n,e),null===(n=(e=e.updateQueue).baseQueue)?(e.baseQueue=t.next=t,t.next=t):(t.next=n.next,n.next=t)}function ca(e,t,n,r){var a=e.updateQueue;ia=!1;var o=a.baseQueue,l=a.shared.pending;if(null!==l){if(null!==o){var u=o.next;o.next=l.next,l.next=u}o=l,a.shared.pending=null,null!==(u=e.alternate)&&(null!==(u=u.updateQueue)&&(u.baseQueue=l))}if(null!==o){u=o.next;var s=a.baseState,c=0,f=null,d=null,h=null;if(null!==u)for(var p=u;;){if((l=p.expirationTime)<r){var g={expirationTime:p.expirationTime,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null};null===h?(d=h=g,f=s):h=h.next=g,l>c&&(c=l)}else{null!==h&&(h=h.next={expirationTime:1073741823,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null}),au(l,p.suspenseConfig);e:{var v=e,m=p;switch(l=t,g=n,m.tag){case 1:if("function"==typeof(v=m.payload)){s=v.call(g,s,l);break e}s=v;break e;case 3:v.effectTag=-4097&v.effectTag|64;case 0:if(null==(l="function"==typeof(v=m.payload)?v.call(g,s,l):v))break e;s=i({},s,l);break e;case 2:ia=!0}}null!==p.callback&&(e.effectTag|=32,null===(l=a.effects)?a.effects=[p]:l.push(p))}if(null===(p=p.next)||p===u){if(null===(l=a.shared.pending))break;p=o.next=l.next,l.next=u,a.baseQueue=o=l,a.shared.pending=null}}null===h?f=s:h.next=d,a.baseState=f,a.baseQueue=h,ou(c),e.expirationTime=c,e.memoizedState=s}}function fa(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=i,i=n,"function"!=typeof r)throw Error(o(191,r));r.call(i)}}}var da=G.ReactCurrentBatchConfig,ha=(new r.Component).refs;function pa(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:i({},t,n),e.memoizedState=n,0===e.expirationTime&&(e.updateQueue.baseState=n)}var ga={isMounted:function(e){return!!(e=e._reactInternalFiber)&&Je(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternalFiber;var r=$l(),i=da.suspense;(i=la(r=Ql(r,e,i),i)).payload=t,null!=n&&(i.callback=n),ua(e,i),Yl(e,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternalFiber;var r=$l(),i=da.suspense;(i=la(r=Ql(r,e,i),i)).tag=1,i.payload=t,null!=n&&(i.callback=n),ua(e,i),Yl(e,r)},enqueueForceUpdate:function(e,t){e=e._reactInternalFiber;var n=$l(),r=da.suspense;(r=la(n=Ql(n,e,r),r)).tag=2,null!=t&&(r.callback=t),ua(e,r),Yl(e,n)}};function va(e,t,n,r,i,a,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!Lr(n,r)||!Lr(i,a))}function ma(e,t,n){var r=!1,i=ci,a=t.contextType;return"object"==typeof a&&null!==a?a=ra(a):(i=gi(t)?hi:fi.current,a=(r=null!=(r=t.contextTypes))?pi(e,i):ci),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ga,e.stateNode=t,t._reactInternalFiber=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function ba(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ga.enqueueReplaceState(t,t.state,null)}function ya(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=ha,aa(e);var a=t.contextType;"object"==typeof a&&null!==a?i.context=ra(a):(a=gi(t)?hi:fi.current,i.context=pi(e,a)),ca(e,n,i,r),i.state=e.memoizedState,"function"==typeof(a=t.getDerivedStateFromProps)&&(pa(e,t,a,n),i.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(t=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ga.enqueueReplaceState(i,i.state,null),ca(e,n,i,r),i.state=e.memoizedState),"function"==typeof i.componentDidMount&&(e.effectTag|=4)}var xa=Array.isArray;function _a(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=r.refs;t===ha&&(t=r.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!=typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function wa(e,t){if("textarea"!==e.type)throw Error(o(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t,""))}function ka(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Eu(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.effectTag=2,n):r:(t.effectTag=2,n):n}function l(t){return e&&null===t.alternate&&(t.effectTag=2),t}function u(e,t,n,r){return null===t||6!==t.tag?((t=Mu(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function s(e,t,n,r){return null!==t&&t.elementType===n.type?((r=i(t,n.props)).ref=_a(e,t,n),r.return=e,r):((r=Cu(n.type,n.key,n.props,null,e.mode,r)).ref=_a(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ou(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function f(e,t,n,r,a){return null===t||7!==t.tag?((t=Pu(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t||"number"==typeof t)return(t=Mu(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case ee:return(n=Cu(t.type,t.key,t.props,null,e.mode,n)).ref=_a(e,null,t),n.return=e,n;case te:return(t=Ou(t,e.mode,n)).return=e,t}if(xa(t)||ge(t))return(t=Pu(t,e.mode,n,null)).return=e,t;wa(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"==typeof n||"number"==typeof n)return null!==i?null:u(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case ee:return n.key===i?n.type===ne?f(e,t,n.props.children,r,i):s(e,t,n,r):null;case te:return n.key===i?c(e,t,n,r):null}if(xa(n)||ge(n))return null!==i?null:f(e,t,n,r,null);wa(e,n)}return null}function p(e,t,n,r,i){if("string"==typeof r||"number"==typeof r)return u(t,e=e.get(n)||null,""+r,i);if("object"==typeof r&&null!==r){switch(r.$$typeof){case ee:return e=e.get(null===r.key?n:r.key)||null,r.type===ne?f(t,e,r.props.children,i,r.key):s(t,e,r,i);case te:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i)}if(xa(r)||ge(r))return f(t,e=e.get(n)||null,r,i,null);wa(t,r)}return null}function g(i,o,l,u){for(var s=null,c=null,f=o,g=o=0,v=null;null!==f&&g<l.length;g++){f.index>g?(v=f,f=null):v=f.sibling;var m=h(i,f,l[g],u);if(null===m){null===f&&(f=v);break}e&&f&&null===m.alternate&&t(i,f),o=a(m,o,g),null===c?s=m:c.sibling=m,c=m,f=v}if(g===l.length)return n(i,f),s;if(null===f){for(;g<l.length;g++)null!==(f=d(i,l[g],u))&&(o=a(f,o,g),null===c?s=f:c.sibling=f,c=f);return s}for(f=r(i,f);g<l.length;g++)null!==(v=p(f,i,g,l[g],u))&&(e&&null!==v.alternate&&f.delete(null===v.key?g:v.key),o=a(v,o,g),null===c?s=v:c.sibling=v,c=v);return e&&f.forEach((function(e){return t(i,e)})),s}function v(i,l,u,s){var c=ge(u);if("function"!=typeof c)throw Error(o(150));if(null==(u=c.call(u)))throw Error(o(151));for(var f=c=null,g=l,v=l=0,m=null,b=u.next();null!==g&&!b.done;v++,b=u.next()){g.index>v?(m=g,g=null):m=g.sibling;var y=h(i,g,b.value,s);if(null===y){null===g&&(g=m);break}e&&g&&null===y.alternate&&t(i,g),l=a(y,l,v),null===f?c=y:f.sibling=y,f=y,g=m}if(b.done)return n(i,g),c;if(null===g){for(;!b.done;v++,b=u.next())null!==(b=d(i,b.value,s))&&(l=a(b,l,v),null===f?c=b:f.sibling=b,f=b);return c}for(g=r(i,g);!b.done;v++,b=u.next())null!==(b=p(g,i,v,b.value,s))&&(e&&null!==b.alternate&&g.delete(null===b.key?v:b.key),l=a(b,l,v),null===f?c=b:f.sibling=b,f=b);return e&&g.forEach((function(e){return t(i,e)})),c}return function(e,r,a,u){var s="object"==typeof a&&null!==a&&a.type===ne&&null===a.key;s&&(a=a.props.children);var c="object"==typeof a&&null!==a;if(c)switch(a.$$typeof){case ee:e:{for(c=a.key,s=r;null!==s;){if(s.key===c){switch(s.tag){case 7:if(a.type===ne){n(e,s.sibling),(r=i(s,a.props.children)).return=e,e=r;break e}break;default:if(s.elementType===a.type){n(e,s.sibling),(r=i(s,a.props)).ref=_a(e,s,a),r.return=e,e=r;break e}}n(e,s);break}t(e,s),s=s.sibling}a.type===ne?((r=Pu(a.props.children,e.mode,u,a.key)).return=e,e=r):((u=Cu(a.type,a.key,a.props,null,e.mode,u)).ref=_a(e,r,a),u.return=e,e=u)}return l(e);case te:e:{for(s=a.key;null!==r;){if(r.key===s){if(4===r.tag&&r.stateNode.containerInfo===a.containerInfo&&r.stateNode.implementation===a.implementation){n(e,r.sibling),(r=i(r,a.children||[])).return=e,e=r;break e}n(e,r);break}t(e,r),r=r.sibling}(r=Ou(a,e.mode,u)).return=e,e=r}return l(e)}if("string"==typeof a||"number"==typeof a)return a=""+a,null!==r&&6===r.tag?(n(e,r.sibling),(r=i(r,a)).return=e,e=r):(n(e,r),(r=Mu(a,e.mode,u)).return=e,e=r),l(e);if(xa(a))return g(e,r,a,u);if(ge(a))return v(e,r,a,u);if(c&&wa(e,a),void 0===a&&!s)switch(e.tag){case 1:case 0:throw e=e.type,Error(o(152,e.displayName||e.name||"Component"))}return n(e,r)}}var Sa=ka(!0),Ta=ka(!1),Ea={},Ca={current:Ea},Pa={current:Ea},Ma={current:Ea};function Oa(e){if(e===Ea)throw Error(o(174));return e}function Ia(e,t){switch(si(Ma,t),si(Pa,e),si(Ca,Ea),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Re(null,"");break;default:t=Re(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ui(Ca),si(Ca,t)}function Aa(){ui(Ca),ui(Pa),ui(Ma)}function Da(e){Oa(Ma.current);var t=Oa(Ca.current),n=Re(t,e.type);t!==n&&(si(Pa,e),si(Ca,n))}function Na(e){Pa.current===e&&(ui(Ca),ui(Pa))}var za={current:0};function ja(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(64&t.effectTag))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ra(e,t){return{responder:e,props:t}}var Fa=G.ReactCurrentDispatcher,La=G.ReactCurrentBatchConfig,Ba=0,Wa=null,Va=null,Ua=null,Ha=!1;function qa(){throw Error(o(321))}function $a(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Rr(e[n],t[n]))return!1;return!0}function Qa(e,t,n,r,i,a){if(Ba=a,Wa=t,t.memoizedState=null,t.updateQueue=null,t.expirationTime=0,Fa.current=null===e||null===e.memoizedState?mo:bo,e=n(r,i),t.expirationTime===Ba){a=0;do{if(t.expirationTime=0,!(25>a))throw Error(o(301));a+=1,Ua=Va=null,t.updateQueue=null,Fa.current=yo,e=n(r,i)}while(t.expirationTime===Ba)}if(Fa.current=vo,t=null!==Va&&null!==Va.next,Ba=0,Ua=Va=Wa=null,Ha=!1,t)throw Error(o(300));return e}function Ya(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Ua?Wa.memoizedState=Ua=e:Ua=Ua.next=e,Ua}function Ka(){if(null===Va){var e=Wa.alternate;e=null!==e?e.memoizedState:null}else e=Va.next;var t=null===Ua?Wa.memoizedState:Ua.next;if(null!==t)Ua=t,Va=e;else{if(null===e)throw Error(o(310));e={memoizedState:(Va=e).memoizedState,baseState:Va.baseState,baseQueue:Va.baseQueue,queue:Va.queue,next:null},null===Ua?Wa.memoizedState=Ua=e:Ua=Ua.next=e}return Ua}function Ga(e,t){return"function"==typeof t?t(e):t}function Za(e){var t=Ka(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=Va,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var l=i.next;i.next=a.next,a.next=l}r.baseQueue=i=a,n.pending=null}if(null!==i){i=i.next,r=r.baseState;var u=l=a=null,s=i;do{var c=s.expirationTime;if(c<Ba){var f={expirationTime:s.expirationTime,suspenseConfig:s.suspenseConfig,action:s.action,eagerReducer:s.eagerReducer,eagerState:s.eagerState,next:null};null===u?(l=u=f,a=r):u=u.next=f,c>Wa.expirationTime&&(Wa.expirationTime=c,ou(c))}else null!==u&&(u=u.next={expirationTime:1073741823,suspenseConfig:s.suspenseConfig,action:s.action,eagerReducer:s.eagerReducer,eagerState:s.eagerState,next:null}),au(c,s.suspenseConfig),r=s.eagerReducer===e?s.eagerState:e(r,s.action);s=s.next}while(null!==s&&s!==i);null===u?a=r:u.next=l,Rr(r,t.memoizedState)||(Oo=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=u,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function Xa(e){var t=Ka(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var l=i=i.next;do{a=e(a,l.action),l=l.next}while(l!==i);Rr(a,t.memoizedState)||(Oo=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Ja(e){var t=Ya();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:Ga,lastRenderedState:e}).dispatch=go.bind(null,Wa,e),[t.memoizedState,e]}function eo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=Wa.updateQueue)?(t={lastEffect:null},Wa.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function to(){return Ka().memoizedState}function no(e,t,n,r){var i=Ya();Wa.effectTag|=e,i.memoizedState=eo(1|t,n,void 0,void 0===r?null:r)}function ro(e,t,n,r){var i=Ka();r=void 0===r?null:r;var a=void 0;if(null!==Va){var o=Va.memoizedState;if(a=o.destroy,null!==r&&$a(r,o.deps))return void eo(t,n,a,r)}Wa.effectTag|=e,i.memoizedState=eo(1|t,n,a,r)}function io(e,t){return no(516,4,e,t)}function ao(e,t){return ro(516,4,e,t)}function oo(e,t){return ro(4,2,e,t)}function lo(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function uo(e,t,n){return n=null!=n?n.concat([e]):null,ro(4,2,lo.bind(null,t,e),n)}function so(){}function co(e,t){return Ya().memoizedState=[e,void 0===t?null:t],e}function fo(e,t){var n=Ka();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&$a(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ho(e,t){var n=Ka();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&$a(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function po(e,t,n){var r=Bi();Vi(98>r?98:r,(function(){e(!0)})),Vi(97<r?97:r,(function(){var r=La.suspense;La.suspense=void 0===t?null:t;try{e(!1),n()}finally{La.suspense=r}}))}function go(e,t,n){var r=$l(),i=da.suspense;i={expirationTime:r=Ql(r,e,i),suspenseConfig:i,action:n,eagerReducer:null,eagerState:null,next:null};var a=t.pending;if(null===a?i.next=i:(i.next=a.next,a.next=i),t.pending=i,a=e.alternate,e===Wa||null!==a&&a===Wa)Ha=!0,i.expirationTime=Ba,Wa.expirationTime=Ba;else{if(0===e.expirationTime&&(null===a||0===a.expirationTime)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,l=a(o,n);if(i.eagerReducer=a,i.eagerState=l,Rr(l,o))return}catch(e){}Yl(e,r)}}var vo={readContext:ra,useCallback:qa,useContext:qa,useEffect:qa,useImperativeHandle:qa,useLayoutEffect:qa,useMemo:qa,useReducer:qa,useRef:qa,useState:qa,useDebugValue:qa,useResponder:qa,useDeferredValue:qa,useTransition:qa},mo={readContext:ra,useCallback:co,useContext:ra,useEffect:io,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,no(4,2,lo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return no(4,2,e,t)},useMemo:function(e,t){var n=Ya();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ya();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=go.bind(null,Wa,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ya().memoizedState=e},useState:Ja,useDebugValue:so,useResponder:Ra,useDeferredValue:function(e,t){var n=Ja(e),r=n[0],i=n[1];return io((function(){var n=La.suspense;La.suspense=void 0===t?null:t;try{i(e)}finally{La.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Ja(!1),n=t[0];return t=t[1],[co(po.bind(null,t,e),[t,e]),n]}},bo={readContext:ra,useCallback:fo,useContext:ra,useEffect:ao,useImperativeHandle:uo,useLayoutEffect:oo,useMemo:ho,useReducer:Za,useRef:to,useState:function(){return Za(Ga)},useDebugValue:so,useResponder:Ra,useDeferredValue:function(e,t){var n=Za(Ga),r=n[0],i=n[1];return ao((function(){var n=La.suspense;La.suspense=void 0===t?null:t;try{i(e)}finally{La.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Za(Ga),n=t[0];return t=t[1],[fo(po.bind(null,t,e),[t,e]),n]}},yo={readContext:ra,useCallback:fo,useContext:ra,useEffect:ao,useImperativeHandle:uo,useLayoutEffect:oo,useMemo:ho,useReducer:Xa,useRef:to,useState:function(){return Xa(Ga)},useDebugValue:so,useResponder:Ra,useDeferredValue:function(e,t){var n=Xa(Ga),r=n[0],i=n[1];return ao((function(){var n=La.suspense;La.suspense=void 0===t?null:t;try{i(e)}finally{La.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Xa(Ga),n=t[0];return t=t[1],[fo(po.bind(null,t,e),[t,e]),n]}},xo=null,_o=null,wo=!1;function ko(e,t){var n=Su(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.effectTag=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function So(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);case 13:default:return!1}}function To(e){if(wo){var t=_o;if(t){var n=t;if(!So(e,t)){if(!(t=_n(n.nextSibling))||!So(e,t))return e.effectTag=-1025&e.effectTag|2,wo=!1,void(xo=e);ko(xo,n)}xo=e,_o=_n(t.firstChild)}else e.effectTag=-1025&e.effectTag|2,wo=!1,xo=e}}function Eo(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;xo=e}function Co(e){if(e!==xo)return!1;if(!wo)return Eo(e),wo=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!bn(t,e.memoizedProps))for(t=_o;t;)ko(e,t),t=_n(t.nextSibling);if(Eo(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){_o=_n(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}_o=null}}else _o=xo?_n(e.stateNode.nextSibling):null;return!0}function Po(){_o=xo=null,wo=!1}var Mo=G.ReactCurrentOwner,Oo=!1;function Io(e,t,n,r){t.child=null===e?Ta(t,null,n,r):Sa(t,e.child,n,r)}function Ao(e,t,n,r,i){n=n.render;var a=t.ref;return na(t,i),r=Qa(e,t,n,r,a,i),null===e||Oo?(t.effectTag|=1,Io(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Yo(e,t,i))}function Do(e,t,n,r,i,a){if(null===e){var o=n.type;return"function"!=typeof o||Tu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Cu(n.type,null,r,null,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,No(e,t,o,r,i,a))}return o=e.child,i<a&&(i=o.memoizedProps,(n=null!==(n=n.compare)?n:Lr)(i,r)&&e.ref===t.ref)?Yo(e,t,a):(t.effectTag|=1,(e=Eu(o,r)).ref=t.ref,e.return=t,t.child=e)}function No(e,t,n,r,i,a){return null!==e&&Lr(e.memoizedProps,r)&&e.ref===t.ref&&(Oo=!1,i<a)?(t.expirationTime=e.expirationTime,Yo(e,t,a)):jo(e,t,n,r,a)}function zo(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.effectTag|=128)}function jo(e,t,n,r,i){var a=gi(n)?hi:fi.current;return a=pi(t,a),na(t,i),n=Qa(e,t,n,r,a,i),null===e||Oo?(t.effectTag|=1,Io(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Yo(e,t,i))}function Ro(e,t,n,r,i){if(gi(n)){var a=!0;yi(t)}else a=!1;if(na(t,i),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),ma(t,n,r),ya(t,n,r,i),r=!0;else if(null===e){var o=t.stateNode,l=t.memoizedProps;o.props=l;var u=o.context,s=n.contextType;"object"==typeof s&&null!==s?s=ra(s):s=pi(t,s=gi(n)?hi:fi.current);var c=n.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof o.getSnapshotBeforeUpdate;f||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(l!==r||u!==s)&&ba(t,o,r,s),ia=!1;var d=t.memoizedState;o.state=d,ca(t,r,o,i),u=t.memoizedState,l!==r||d!==u||di.current||ia?("function"==typeof c&&(pa(t,n,c,r),u=t.memoizedState),(l=ia||va(t,n,l,r,d,u,s))?(f||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||("function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"==typeof o.componentDidMount&&(t.effectTag|=4)):("function"==typeof o.componentDidMount&&(t.effectTag|=4),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=s,r=l):("function"==typeof o.componentDidMount&&(t.effectTag|=4),r=!1)}else o=t.stateNode,oa(e,t),l=t.memoizedProps,o.props=t.type===t.elementType?l:Yi(t.type,l),u=o.context,"object"==typeof(s=n.contextType)&&null!==s?s=ra(s):s=pi(t,s=gi(n)?hi:fi.current),(f="function"==typeof(c=n.getDerivedStateFromProps)||"function"==typeof o.getSnapshotBeforeUpdate)||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(l!==r||u!==s)&&ba(t,o,r,s),ia=!1,u=t.memoizedState,o.state=u,ca(t,r,o,i),d=t.memoizedState,l!==r||u!==d||di.current||ia?("function"==typeof c&&(pa(t,n,c,r),d=t.memoizedState),(c=ia||va(t,n,l,r,u,d,s))?(f||"function"!=typeof o.UNSAFE_componentWillUpdate&&"function"!=typeof o.componentWillUpdate||("function"==typeof o.componentWillUpdate&&o.componentWillUpdate(r,d,s),"function"==typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,d,s)),"function"==typeof o.componentDidUpdate&&(t.effectTag|=4),"function"==typeof o.getSnapshotBeforeUpdate&&(t.effectTag|=256)):("function"!=typeof o.componentDidUpdate||l===e.memoizedProps&&u===e.memoizedState||(t.effectTag|=4),"function"!=typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&u===e.memoizedState||(t.effectTag|=256),t.memoizedProps=r,t.memoizedState=d),o.props=r,o.state=d,o.context=s,r=c):("function"!=typeof o.componentDidUpdate||l===e.memoizedProps&&u===e.memoizedState||(t.effectTag|=4),"function"!=typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&u===e.memoizedState||(t.effectTag|=256),r=!1);return Fo(e,t,n,r,a,i)}function Fo(e,t,n,r,i,a){zo(e,t);var o=0!=(64&t.effectTag);if(!r&&!o)return i&&xi(t,n,!1),Yo(e,t,a);r=t.stateNode,Mo.current=t;var l=o&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.effectTag|=1,null!==e&&o?(t.child=Sa(t,e.child,null,a),t.child=Sa(t,null,l,a)):Io(e,t,l,a),t.memoizedState=r.state,i&&xi(t,n,!0),t.child}function Lo(e){var t=e.stateNode;t.pendingContext?mi(0,t.pendingContext,t.pendingContext!==t.context):t.context&&mi(0,t.context,!1),Ia(e,t.containerInfo)}var Bo,Wo,Vo,Uo={dehydrated:null,retryTime:0};function Ho(e,t,n){var r,i=t.mode,a=t.pendingProps,o=za.current,l=!1;if((r=0!=(64&t.effectTag))||(r=0!=(2&o)&&(null===e||null!==e.memoizedState)),r?(l=!0,t.effectTag&=-65):null!==e&&null===e.memoizedState||void 0===a.fallback||!0===a.unstable_avoidThisFallback||(o|=1),si(za,1&o),null===e){if(void 0!==a.fallback&&To(t),l){if(l=a.fallback,(a=Pu(null,i,0,null)).return=t,0==(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=Pu(l,i,n,null)).return=t,a.sibling=n,t.memoizedState=Uo,t.child=a,n}return i=a.children,t.memoizedState=null,t.child=Ta(t,null,i,n)}if(null!==e.memoizedState){if(i=(e=e.child).sibling,l){if(a=a.fallback,(n=Eu(e,e.pendingProps)).return=t,0==(2&t.mode)&&(l=null!==t.memoizedState?t.child.child:t.child)!==e.child)for(n.child=l;null!==l;)l.return=n,l=l.sibling;return(i=Eu(i,a)).return=t,n.sibling=i,n.childExpirationTime=0,t.memoizedState=Uo,t.child=n,i}return n=Sa(t,e.child,a.children,n),t.memoizedState=null,t.child=n}if(e=e.child,l){if(l=a.fallback,(a=Pu(null,i,0,null)).return=t,a.child=e,null!==e&&(e.return=a),0==(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=Pu(l,i,n,null)).return=t,a.sibling=n,n.effectTag|=2,a.childExpirationTime=0,t.memoizedState=Uo,t.child=a,n}return t.memoizedState=null,t.child=Sa(t,e,a.children,n)}function qo(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t),ta(e.return,t)}function $o(e,t,n,r,i,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailExpiration:0,tailMode:i,lastEffect:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailExpiration=0,o.tailMode=i,o.lastEffect=a)}function Qo(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(Io(e,t,r.children,n),0!=(2&(r=za.current)))r=1&r|2,t.effectTag|=64;else{if(null!==e&&0!=(64&e.effectTag))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&qo(e,n);else if(19===e.tag)qo(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(si(za,r),0==(2&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===ja(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),$o(t,!1,i,n,a,t.lastEffect);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===ja(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}$o(t,!0,n,null,a,t.lastEffect);break;case"together":$o(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Yo(e,t,n){null!==e&&(t.dependencies=e.dependencies);var r=t.expirationTime;if(0!==r&&ou(r),t.childExpirationTime<n)return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Eu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Eu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ko(e,t){switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Go(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return gi(t.type)&&vi(),null;case 3:return Aa(),ui(di),ui(fi),(n=t.stateNode).pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||!Co(t)||(t.effectTag|=4),null;case 5:Na(t),n=Oa(Ma.current);var a=t.type;if(null!==e&&null!=t.stateNode)Wo(e,t,a,r,n),e.ref!==t.ref&&(t.effectTag|=128);else{if(!r){if(null===t.stateNode)throw Error(o(166));return null}if(e=Oa(Ca.current),Co(t)){r=t.stateNode,a=t.type;var l=t.memoizedProps;switch(r[Sn]=t,r[Tn]=l,a){case"iframe":case"object":case"embed":Qt("load",r);break;case"video":case"audio":for(e=0;e<Ge.length;e++)Qt(Ge[e],r);break;case"source":Qt("error",r);break;case"img":case"image":case"link":Qt("error",r),Qt("load",r);break;case"form":Qt("reset",r),Qt("submit",r);break;case"details":Qt("toggle",r);break;case"input":ke(r,l),Qt("invalid",r),un(n,"onChange");break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Qt("invalid",r),un(n,"onChange");break;case"textarea":Ie(r,l),Qt("invalid",r),un(n,"onChange")}for(var u in an(a,l),e=null,l)if(l.hasOwnProperty(u)){var s=l[u];"children"===u?"string"==typeof s?r.textContent!==s&&(e=["children",s]):"number"==typeof s&&r.textContent!==""+s&&(e=["children",""+s]):S.hasOwnProperty(u)&&null!=s&&un(n,u)}switch(a){case"input":xe(r),Ee(r,l,!0);break;case"textarea":xe(r),De(r);break;case"select":case"option":break;default:"function"==typeof l.onClick&&(r.onclick=sn)}n=e,t.updateQueue=n,null!==n&&(t.effectTag|=4)}else{switch(u=9===n.nodeType?n:n.ownerDocument,e===ln&&(e=je(a)),e===ln?"script"===a?((e=u.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=u.createElement(a,{is:r.is}):(e=u.createElement(a),"select"===a&&(u=e,r.multiple?u.multiple=!0:r.size&&(u.size=r.size))):e=u.createElementNS(e,a),e[Sn]=t,e[Tn]=r,Bo(e,t),t.stateNode=e,u=on(a,r),a){case"iframe":case"object":case"embed":Qt("load",e),s=r;break;case"video":case"audio":for(s=0;s<Ge.length;s++)Qt(Ge[s],e);s=r;break;case"source":Qt("error",e),s=r;break;case"img":case"image":case"link":Qt("error",e),Qt("load",e),s=r;break;case"form":Qt("reset",e),Qt("submit",e),s=r;break;case"details":Qt("toggle",e),s=r;break;case"input":ke(e,r),s=we(e,r),Qt("invalid",e),un(n,"onChange");break;case"option":s=Pe(e,r);break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=i({},r,{value:void 0}),Qt("invalid",e),un(n,"onChange");break;case"textarea":Ie(e,r),s=Oe(e,r),Qt("invalid",e),un(n,"onChange");break;default:s=r}an(a,s);var c=s;for(l in c)if(c.hasOwnProperty(l)){var f=c[l];"style"===l?nn(e,f):"dangerouslySetInnerHTML"===l?null!=(f=f?f.__html:void 0)&&Le(e,f):"children"===l?"string"==typeof f?("textarea"!==a||""!==f)&&Be(e,f):"number"==typeof f&&Be(e,""+f):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(S.hasOwnProperty(l)?null!=f&&un(n,l):null!=f&&Z(e,l,f,u))}switch(a){case"input":xe(e),Ee(e,r,!1);break;case"textarea":xe(e),De(e);break;case"option":null!=r.value&&e.setAttribute("value",""+be(r.value));break;case"select":e.multiple=!!r.multiple,null!=(n=r.value)?Me(e,!!r.multiple,n,!1):null!=r.defaultValue&&Me(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof s.onClick&&(e.onclick=sn)}mn(a,r)&&(t.effectTag|=4)}null!==t.ref&&(t.effectTag|=128)}return null;case 6:if(e&&null!=t.stateNode)Vo(0,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));n=Oa(Ma.current),Oa(Ca.current),Co(t)?(n=t.stateNode,r=t.memoizedProps,n[Sn]=t,n.nodeValue!==r&&(t.effectTag|=4)):((n=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Sn]=t,t.stateNode=n)}return null;case 13:return ui(za),r=t.memoizedState,0!=(64&t.effectTag)?(t.expirationTime=n,t):(n=null!==r,r=!1,null===e?void 0!==t.memoizedProps.fallback&&Co(t):(r=null!==(a=e.memoizedState),n||null===a||null!==(a=e.child.sibling)&&(null!==(l=t.firstEffect)?(t.firstEffect=a,a.nextEffect=l):(t.firstEffect=t.lastEffect=a,a.nextEffect=null),a.effectTag=8)),n&&!r&&0!=(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!=(1&za.current)?Cl===xl&&(Cl=_l):(Cl!==xl&&Cl!==_l||(Cl=wl),0!==Al&&null!==Sl&&(Du(Sl,El),Nu(Sl,Al)))),(n||r)&&(t.effectTag|=4),null);case 4:return Aa(),null;case 10:return ea(t),null;case 17:return gi(t.type)&&vi(),null;case 19:if(ui(za),null===(r=t.memoizedState))return null;if(a=0!=(64&t.effectTag),null===(l=r.rendering)){if(a)Ko(r,!1);else if(Cl!==xl||null!==e&&0!=(64&e.effectTag))for(l=t.child;null!==l;){if(null!==(e=ja(l))){for(t.effectTag|=64,Ko(r,!1),null!==(a=e.updateQueue)&&(t.updateQueue=a,t.effectTag|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,r=t.child;null!==r;)l=n,(a=r).effectTag&=2,a.nextEffect=null,a.firstEffect=null,a.lastEffect=null,null===(e=a.alternate)?(a.childExpirationTime=0,a.expirationTime=l,a.child=null,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null):(a.childExpirationTime=e.childExpirationTime,a.expirationTime=e.expirationTime,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,l=e.dependencies,a.dependencies=null===l?null:{expirationTime:l.expirationTime,firstContext:l.firstContext,responders:l.responders}),r=r.sibling;return si(za,1&za.current|2),t.child}l=l.sibling}}else{if(!a)if(null!==(e=ja(l))){if(t.effectTag|=64,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.effectTag|=4),Ko(r,!0),null===r.tail&&"hidden"===r.tailMode&&!l.alternate)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*Li()-r.renderingStartTime>r.tailExpiration&&1<n&&(t.effectTag|=64,a=!0,Ko(r,!1),t.expirationTime=t.childExpirationTime=n-1);r.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=r.last)?n.sibling=l:t.child=l,r.last=l)}return null!==r.tail?(0===r.tailExpiration&&(r.tailExpiration=Li()+500),n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=Li(),n.sibling=null,t=za.current,si(za,a?1&t|2:1&t),n):null}throw Error(o(156,t.tag))}function Zo(e){switch(e.tag){case 1:gi(e.type)&&vi();var t=e.effectTag;return 4096&t?(e.effectTag=-4097&t|64,e):null;case 3:if(Aa(),ui(di),ui(fi),0!=(64&(t=e.effectTag)))throw Error(o(285));return e.effectTag=-4097&t|64,e;case 5:return Na(e),null;case 13:return ui(za),4096&(t=e.effectTag)?(e.effectTag=-4097&t|64,e):null;case 19:return ui(za),null;case 4:return Aa(),null;case 10:return ea(e),null;default:return null}}function Xo(e,t){return{value:e,source:t,stack:me(t)}}Bo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Wo=function(e,t,n,r,a){var o=e.memoizedProps;if(o!==r){var l,u,s=t.stateNode;switch(Oa(Ca.current),e=null,n){case"input":o=we(s,o),r=we(s,r),e=[];break;case"option":o=Pe(s,o),r=Pe(s,r),e=[];break;case"select":o=i({},o,{value:void 0}),r=i({},r,{value:void 0}),e=[];break;case"textarea":o=Oe(s,o),r=Oe(s,r),e=[];break;default:"function"!=typeof o.onClick&&"function"==typeof r.onClick&&(s.onclick=sn)}for(l in an(n,r),n=null,o)if(!r.hasOwnProperty(l)&&o.hasOwnProperty(l)&&null!=o[l])if("style"===l)for(u in s=o[l])s.hasOwnProperty(u)&&(n||(n={}),n[u]="");else"dangerouslySetInnerHTML"!==l&&"children"!==l&&"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(S.hasOwnProperty(l)?e||(e=[]):(e=e||[]).push(l,null));for(l in r){var c=r[l];if(s=null!=o?o[l]:void 0,r.hasOwnProperty(l)&&c!==s&&(null!=c||null!=s))if("style"===l)if(s){for(u in s)!s.hasOwnProperty(u)||c&&c.hasOwnProperty(u)||(n||(n={}),n[u]="");for(u in c)c.hasOwnProperty(u)&&s[u]!==c[u]&&(n||(n={}),n[u]=c[u])}else n||(e||(e=[]),e.push(l,n)),n=c;else"dangerouslySetInnerHTML"===l?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(e=e||[]).push(l,c)):"children"===l?s===c||"string"!=typeof c&&"number"!=typeof c||(e=e||[]).push(l,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&(S.hasOwnProperty(l)?(null!=c&&un(a,l),e||s===c||(e=[])):(e=e||[]).push(l,c))}n&&(e=e||[]).push("style",n),a=e,(t.updateQueue=a)&&(t.effectTag|=4)}},Vo=function(e,t,n,r){n!==r&&(t.effectTag|=4)};var Jo="function"==typeof WeakSet?WeakSet:Set;function el(e,t){var n=t.source,r=t.stack;null===r&&null!==n&&(r=me(n)),null!==n&&ve(n.type),t=t.value,null!==e&&1===e.tag&&ve(e.type);try{console.error(t)}catch(e){setTimeout((function(){throw e}))}}function tl(e){var t=e.ref;if(null!==t)if("function"==typeof t)try{t(null)}catch(t){bu(e,t)}else t.current=null}function nl(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.effectTag&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:Yi(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:case 5:case 6:case 4:case 17:return}throw Error(o(163))}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.destroy;n.destroy=void 0,void 0!==r&&r()}n=n.next}while(n!==t)}}function il(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:return void il(3,n);case 1:if(e=n.stateNode,4&n.effectTag)if(null===t)e.componentDidMount();else{var r=n.elementType===n.type?t.memoizedProps:Yi(n.type,t.memoizedProps);e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}return void(null!==(t=n.updateQueue)&&fa(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}fa(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.effectTag&&mn(n.type,n.memoizedProps)&&e.focus());case 6:case 4:case 12:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&jt(n)))));case 19:case 17:case 20:case 21:return}throw Error(o(163))}function ol(e,t,n){switch("function"==typeof wu&&wu(t),t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var r=e.next;Vi(97<n?97:n,(function(){var e=r;do{var n=e.destroy;if(void 0!==n){var i=t;try{n()}catch(e){bu(i,e)}}e=e.next}while(e!==r)}))}break;case 1:tl(t),"function"==typeof(n=t.stateNode).componentWillUnmount&&function(e,t){try{t.props=e.memoizedProps,t.state=e.memoizedState,t.componentWillUnmount()}catch(t){bu(e,t)}}(t,n);break;case 5:tl(t);break;case 4:cl(e,t,n)}}function ll(e){var t=e.alternate;e.return=null,e.child=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.alternate=null,e.firstEffect=null,e.lastEffect=null,e.pendingProps=null,e.memoizedProps=null,e.stateNode=null,null!==t&&ll(t)}function ul(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:{for(var t=e.return;null!==t;){if(ul(t)){var n=t;break e}t=t.return}throw Error(o(160))}switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(o(161))}16&n.effectTag&&(Be(t,""),n.effectTag&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||ul(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.effectTag)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.effectTag)){n=n.stateNode;break e}}r?function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?8===r.nodeType?r.parentNode.insertBefore(t,n):r.insertBefore(t,n):(8===r.nodeType?(n=r.parentNode).insertBefore(t,r):(n=r).appendChild(t),null!==(r=r._reactRootContainer)&&void 0!==r||null!==n.onclick||(n.onclick=sn));else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t):function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?r.insertBefore(t,n):r.appendChild(t);else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t)}function cl(e,t,n){for(var r,i,a=t,l=!1;;){if(!l){l=a.return;e:for(;;){if(null===l)throw Error(o(160));switch(r=l.stateNode,l.tag){case 5:i=!1;break e;case 3:case 4:r=r.containerInfo,i=!0;break e}l=l.return}l=!0}if(5===a.tag||6===a.tag){e:for(var u=e,s=a,c=n,f=s;;)if(ol(u,f,c),null!==f.child&&4!==f.tag)f.child.return=f,f=f.child;else{if(f===s)break e;for(;null===f.sibling;){if(null===f.return||f.return===s)break e;f=f.return}f.sibling.return=f.return,f=f.sibling}i?(u=r,s=a.stateNode,8===u.nodeType?u.parentNode.removeChild(s):u.removeChild(s)):r.removeChild(a.stateNode)}else if(4===a.tag){if(null!==a.child){r=a.stateNode.containerInfo,i=!0,a.child.return=a,a=a.child;continue}}else if(ol(e,a,n),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(l=!1)}a.sibling.return=a.return,a=a.sibling}}function fl(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void rl(3,t);case 1:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps,i=null!==e?e.memoizedProps:r;e=t.type;var a=t.updateQueue;if(t.updateQueue=null,null!==a){for(n[Tn]=r,"input"===e&&"radio"===r.type&&null!=r.name&&Se(n,r),on(e,i),t=on(e,r),i=0;i<a.length;i+=2){var l=a[i],u=a[i+1];"style"===l?nn(n,u):"dangerouslySetInnerHTML"===l?Le(n,u):"children"===l?Be(n,u):Z(n,l,u,t)}switch(e){case"input":Te(n,r);break;case"textarea":Ae(n,r);break;case"select":t=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(e=r.value)?Me(n,!!r.multiple,e,!1):t!==!!r.multiple&&(null!=r.defaultValue?Me(n,!!r.multiple,r.defaultValue,!0):Me(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(o(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((t=t.stateNode).hydrate&&(t.hydrate=!1,jt(t.containerInfo)));case 12:return;case 13:if(n=t,null===t.memoizedState?r=!1:(r=!0,n=t.child,Nl=Li()),null!==n)e:for(e=n;;){if(5===e.tag)a=e.stateNode,r?"function"==typeof(a=a.style).setProperty?a.setProperty("display","none","important"):a.display="none":(a=e.stateNode,i=null!=(i=e.memoizedProps.style)&&i.hasOwnProperty("display")?i.display:null,a.style.display=tn("display",i));else if(6===e.tag)e.stateNode.nodeValue=r?"":e.memoizedProps;else{if(13===e.tag&&null!==e.memoizedState&&null===e.memoizedState.dehydrated){(a=e.child.sibling).return=e,e=a;continue}if(null!==e.child){e.child.return=e,e=e.child;continue}}if(e===n)break;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}return void dl(t);case 19:return void dl(t);case 17:return}throw Error(o(163))}function dl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jo),t.forEach((function(t){var r=xu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}var hl="function"==typeof WeakMap?WeakMap:Map;function pl(e,t,n){(n=la(n,null)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){jl||(jl=!0,Rl=r),el(e,t)},n}function gl(e,t,n){(n=la(n,null)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var i=t.value;n.payload=function(){return el(e,t),r(i)}}var a=e.stateNode;return null!==a&&"function"==typeof a.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===Fl?Fl=new Set([this]):Fl.add(this),el(e,t));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}var vl,ml=Math.ceil,bl=G.ReactCurrentDispatcher,yl=G.ReactCurrentOwner,xl=0,_l=3,wl=4,kl=0,Sl=null,Tl=null,El=0,Cl=xl,Pl=null,Ml=1073741823,Ol=1073741823,Il=null,Al=0,Dl=!1,Nl=0,zl=null,jl=!1,Rl=null,Fl=null,Ll=!1,Bl=null,Wl=90,Vl=null,Ul=0,Hl=null,ql=0;function $l(){return 0!=(48&kl)?1073741821-(Li()/10|0):0!==ql?ql:ql=1073741821-(Li()/10|0)}function Ql(e,t,n){if(0==(2&(t=t.mode)))return 1073741823;var r=Bi();if(0==(4&t))return 99===r?1073741823:1073741822;if(0!=(16&kl))return El;if(null!==n)e=Qi(e,0|n.timeoutMs||5e3,250);else switch(r){case 99:e=1073741823;break;case 98:e=Qi(e,150,100);break;case 97:case 96:e=Qi(e,5e3,250);break;case 95:e=2;break;default:throw Error(o(326))}return null!==Sl&&e===El&&--e,e}function Yl(e,t){if(50<Ul)throw Ul=0,Hl=null,Error(o(185));if(null!==(e=Kl(e,t))){var n=Bi();1073741823===t?0!=(8&kl)&&0==(48&kl)?Jl(e):(Zl(e),0===kl&&qi()):Zl(e),0==(4&kl)||98!==n&&99!==n||(null===Vl?Vl=new Map([[e,t]]):(void 0===(n=Vl.get(e))||n>t)&&Vl.set(e,t))}}function Kl(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t);var r=e.return,i=null;if(null===r&&3===e.tag)i=e.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<t&&(r.childExpirationTime=t),null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t),null===r.return&&3===r.tag){i=r.stateNode;break}r=r.return}return null!==i&&(Sl===i&&(ou(t),Cl===wl&&Du(i,El)),Nu(i,t)),i}function Gl(e){var t=e.lastExpiredTime;if(0!==t)return t;if(!Au(e,t=e.firstPendingTime))return t;var n=e.lastPingedTime;return 2>=(e=n>(e=e.nextKnownPendingLevel)?n:e)&&t!==e?0:e}function Zl(e){if(0!==e.lastExpiredTime)e.callbackExpirationTime=1073741823,e.callbackPriority=99,e.callbackNode=Hi(Jl.bind(null,e));else{var t=Gl(e),n=e.callbackNode;if(0===t)null!==n&&(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90);else{var r=$l();if(1073741823===t?r=99:1===t||2===t?r=95:r=0>=(r=10*(1073741821-t)-10*(1073741821-r))?99:250>=r?98:5250>=r?97:95,null!==n){var i=e.callbackPriority;if(e.callbackExpirationTime===t&&i>=r)return;n!==Ai&&ki(n)}e.callbackExpirationTime=t,e.callbackPriority=r,t=1073741823===t?Hi(Jl.bind(null,e)):Ui(r,Xl.bind(null,e),{timeout:10*(1073741821-t)-Li()}),e.callbackNode=t}}}function Xl(e,t){if(ql=0,t)return zu(e,t=$l()),Zl(e),null;var n=Gl(e);if(0!==n){if(t=e.callbackNode,0!=(48&kl))throw Error(o(327));if(gu(),e===Sl&&n===El||nu(e,n),null!==Tl){var r=kl;kl|=16;for(var i=iu();;)try{uu();break}catch(t){ru(e,t)}if(Ji(),kl=r,bl.current=i,1===Cl)throw t=Pl,nu(e,n),Du(e,n),Zl(e),t;if(null===Tl)switch(i=e.finishedWork=e.current.alternate,e.finishedExpirationTime=n,r=Cl,Sl=null,r){case xl:case 1:throw Error(o(345));case 2:zu(e,2<n?2:n);break;case _l:if(Du(e,n),n===(r=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=fu(i)),1073741823===Ml&&10<(i=Nl+500-Li())){if(Dl){var a=e.lastPingedTime;if(0===a||a>=n){e.lastPingedTime=n,nu(e,n);break}}if(0!==(a=Gl(e))&&a!==n)break;if(0!==r&&r!==n){e.lastPingedTime=r;break}e.timeoutHandle=yn(du.bind(null,e),i);break}du(e);break;case wl:if(Du(e,n),n===(r=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=fu(i)),Dl&&(0===(i=e.lastPingedTime)||i>=n)){e.lastPingedTime=n,nu(e,n);break}if(0!==(i=Gl(e))&&i!==n)break;if(0!==r&&r!==n){e.lastPingedTime=r;break}if(1073741823!==Ol?r=10*(1073741821-Ol)-Li():1073741823===Ml?r=0:(r=10*(1073741821-Ml)-5e3,0>(r=(i=Li())-r)&&(r=0),(n=10*(1073741821-n)-i)<(r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ml(r/1960))-r)&&(r=n)),10<r){e.timeoutHandle=yn(du.bind(null,e),r);break}du(e);break;case 5:if(1073741823!==Ml&&null!==Il){a=Ml;var l=Il;if(0>=(r=0|l.busyMinDurationMs)?r=0:(i=0|l.busyDelayMs,r=(a=Li()-(10*(1073741821-a)-(0|l.timeoutMs||5e3)))<=i?0:i+r-a),10<r){Du(e,n),e.timeoutHandle=yn(du.bind(null,e),r);break}}du(e);break;default:throw Error(o(329))}if(Zl(e),e.callbackNode===t)return Xl.bind(null,e)}}return null}function Jl(e){var t=e.lastExpiredTime;if(t=0!==t?t:1073741823,0!=(48&kl))throw Error(o(327));if(gu(),e===Sl&&t===El||nu(e,t),null!==Tl){var n=kl;kl|=16;for(var r=iu();;)try{lu();break}catch(t){ru(e,t)}if(Ji(),kl=n,bl.current=r,1===Cl)throw n=Pl,nu(e,t),Du(e,t),Zl(e),n;if(null!==Tl)throw Error(o(261));e.finishedWork=e.current.alternate,e.finishedExpirationTime=t,Sl=null,du(e),Zl(e)}return null}function eu(e,t){var n=kl;kl|=1;try{return e(t)}finally{0===(kl=n)&&qi()}}function tu(e,t){var n=kl;kl&=-2,kl|=8;try{return e(t)}finally{0===(kl=n)&&qi()}}function nu(e,t){e.finishedWork=null,e.finishedExpirationTime=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,xn(n)),null!==Tl)for(n=Tl.return;null!==n;){var r=n;switch(r.tag){case 1:null!=(r=r.type.childContextTypes)&&vi();break;case 3:Aa(),ui(di),ui(fi);break;case 5:Na(r);break;case 4:Aa();break;case 13:case 19:ui(za);break;case 10:ea(r)}n=n.return}Sl=e,Tl=Eu(e.current,null),El=t,Cl=xl,Pl=null,Ol=Ml=1073741823,Il=null,Al=0,Dl=!1}function ru(e,t){for(;;){try{if(Ji(),Fa.current=vo,Ha)for(var n=Wa.memoizedState;null!==n;){var r=n.queue;null!==r&&(r.pending=null),n=n.next}if(Ba=0,Ua=Va=Wa=null,Ha=!1,null===Tl||null===Tl.return)return Cl=1,Pl=t,Tl=null;e:{var i=e,a=Tl.return,o=Tl,l=t;if(t=El,o.effectTag|=2048,o.firstEffect=o.lastEffect=null,null!==l&&"object"==typeof l&&"function"==typeof l.then){var u=l;if(0==(2&o.mode)){var s=o.alternate;s?(o.updateQueue=s.updateQueue,o.memoizedState=s.memoizedState,o.expirationTime=s.expirationTime):(o.updateQueue=null,o.memoizedState=null)}var c=0!=(1&za.current),f=a;do{var d;if(d=13===f.tag){var h=f.memoizedState;if(null!==h)d=null!==h.dehydrated;else{var p=f.memoizedProps;d=void 0!==p.fallback&&(!0!==p.unstable_avoidThisFallback||!c)}}if(d){var g=f.updateQueue;if(null===g){var v=new Set;v.add(u),f.updateQueue=v}else g.add(u);if(0==(2&f.mode)){if(f.effectTag|=64,o.effectTag&=-2981,1===o.tag)if(null===o.alternate)o.tag=17;else{var m=la(1073741823,null);m.tag=2,ua(o,m)}o.expirationTime=1073741823;break e}l=void 0,o=t;var b=i.pingCache;if(null===b?(b=i.pingCache=new hl,l=new Set,b.set(u,l)):void 0===(l=b.get(u))&&(l=new Set,b.set(u,l)),!l.has(o)){l.add(o);var y=yu.bind(null,i,u,o);u.then(y,y)}f.effectTag|=4096,f.expirationTime=t;break e}f=f.return}while(null!==f);l=Error((ve(o.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+me(o))}5!==Cl&&(Cl=2),l=Xo(l,o),f=a;do{switch(f.tag){case 3:u=l,f.effectTag|=4096,f.expirationTime=t,sa(f,pl(f,u,t));break e;case 1:u=l;var x=f.type,_=f.stateNode;if(0==(64&f.effectTag)&&("function"==typeof x.getDerivedStateFromError||null!==_&&"function"==typeof _.componentDidCatch&&(null===Fl||!Fl.has(_)))){f.effectTag|=4096,f.expirationTime=t,sa(f,gl(f,u,t));break e}}f=f.return}while(null!==f)}Tl=cu(Tl)}catch(e){t=e;continue}break}}function iu(){var e=bl.current;return bl.current=vo,null===e?vo:e}function au(e,t){e<Ml&&2<e&&(Ml=e),null!==t&&e<Ol&&2<e&&(Ol=e,Il=t)}function ou(e){e>Al&&(Al=e)}function lu(){for(;null!==Tl;)Tl=su(Tl)}function uu(){for(;null!==Tl&&!Di();)Tl=su(Tl)}function su(e){var t=vl(e.alternate,e,El);return e.memoizedProps=e.pendingProps,null===t&&(t=cu(e)),yl.current=null,t}function cu(e){Tl=e;do{var t=Tl.alternate;if(e=Tl.return,0==(2048&Tl.effectTag)){if(t=Go(t,Tl,El),1===El||1!==Tl.childExpirationTime){for(var n=0,r=Tl.child;null!==r;){var i=r.expirationTime,a=r.childExpirationTime;i>n&&(n=i),a>n&&(n=a),r=r.sibling}Tl.childExpirationTime=n}if(null!==t)return t;null!==e&&0==(2048&e.effectTag)&&(null===e.firstEffect&&(e.firstEffect=Tl.firstEffect),null!==Tl.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=Tl.firstEffect),e.lastEffect=Tl.lastEffect),1<Tl.effectTag&&(null!==e.lastEffect?e.lastEffect.nextEffect=Tl:e.firstEffect=Tl,e.lastEffect=Tl))}else{if(null!==(t=Zo(Tl)))return t.effectTag&=2047,t;null!==e&&(e.firstEffect=e.lastEffect=null,e.effectTag|=2048)}if(null!==(t=Tl.sibling))return t;Tl=e}while(null!==Tl);return Cl===xl&&(Cl=5),null}function fu(e){var t=e.expirationTime;return t>(e=e.childExpirationTime)?t:e}function du(e){var t=Bi();return Vi(99,hu.bind(null,e,t)),null}function hu(e,t){do{gu()}while(null!==Bl);if(0!=(48&kl))throw Error(o(327));var n=e.finishedWork,r=e.finishedExpirationTime;if(null===n)return null;if(e.finishedWork=null,e.finishedExpirationTime=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90,e.nextKnownPendingLevel=0;var i=fu(n);if(e.firstPendingTime=i,r<=e.lastSuspendedTime?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:r<=e.firstSuspendedTime&&(e.firstSuspendedTime=r-1),r<=e.lastPingedTime&&(e.lastPingedTime=0),r<=e.lastExpiredTime&&(e.lastExpiredTime=0),e===Sl&&(Tl=Sl=null,El=0),1<n.effectTag?null!==n.lastEffect?(n.lastEffect.nextEffect=n,i=n.firstEffect):i=n:i=n.firstEffect,null!==i){var a=kl;kl|=32,yl.current=null,gn=$t;var l=hn();if(pn(l)){if("selectionStart"in l)var u={start:l.selectionStart,end:l.selectionEnd};else e:{var s=(u=(u=l.ownerDocument)&&u.defaultView||window).getSelection&&u.getSelection();if(s&&0!==s.rangeCount){u=s.anchorNode;var c=s.anchorOffset,f=s.focusNode;s=s.focusOffset;try{u.nodeType,f.nodeType}catch(e){u=null;break e}var d=0,h=-1,p=-1,g=0,v=0,m=l,b=null;t:for(;;){for(var y;m!==u||0!==c&&3!==m.nodeType||(h=d+c),m!==f||0!==s&&3!==m.nodeType||(p=d+s),3===m.nodeType&&(d+=m.nodeValue.length),null!==(y=m.firstChild);)b=m,m=y;for(;;){if(m===l)break t;if(b===u&&++g===c&&(h=d),b===f&&++v===s&&(p=d),null!==(y=m.nextSibling))break;b=(m=b).parentNode}m=y}u=-1===h||-1===p?null:{start:h,end:p}}else u=null}u=u||{start:0,end:0}}else u=null;vn={activeElementDetached:null,focusedElem:l,selectionRange:u},$t=!1,zl=i;do{try{pu()}catch(e){if(null===zl)throw Error(o(330));bu(zl,e),zl=zl.nextEffect}}while(null!==zl);zl=i;do{try{for(l=e,u=t;null!==zl;){var x=zl.effectTag;if(16&x&&Be(zl.stateNode,""),128&x){var _=zl.alternate;if(null!==_){var w=_.ref;null!==w&&("function"==typeof w?w(null):w.current=null)}}switch(1038&x){case 2:sl(zl),zl.effectTag&=-3;break;case 6:sl(zl),zl.effectTag&=-3,fl(zl.alternate,zl);break;case 1024:zl.effectTag&=-1025;break;case 1028:zl.effectTag&=-1025,fl(zl.alternate,zl);break;case 4:fl(zl.alternate,zl);break;case 8:cl(l,c=zl,u),ll(c)}zl=zl.nextEffect}}catch(e){if(null===zl)throw Error(o(330));bu(zl,e),zl=zl.nextEffect}}while(null!==zl);if(w=vn,_=hn(),x=w.focusedElem,u=w.selectionRange,_!==x&&x&&x.ownerDocument&&function e(t,n){return!(!t||!n)&&(t===n||(!t||3!==t.nodeType)&&(n&&3===n.nodeType?e(t,n.parentNode):"contains"in t?t.contains(n):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(n))))}(x.ownerDocument.documentElement,x)){null!==u&&pn(x)&&(_=u.start,void 0===(w=u.end)&&(w=_),"selectionStart"in x?(x.selectionStart=_,x.selectionEnd=Math.min(w,x.value.length)):(w=(_=x.ownerDocument||document)&&_.defaultView||window).getSelection&&(w=w.getSelection(),c=x.textContent.length,l=Math.min(u.start,c),u=void 0===u.end?l:Math.min(u.end,c),!w.extend&&l>u&&(c=u,u=l,l=c),c=dn(x,l),f=dn(x,u),c&&f&&(1!==w.rangeCount||w.anchorNode!==c.node||w.anchorOffset!==c.offset||w.focusNode!==f.node||w.focusOffset!==f.offset)&&((_=_.createRange()).setStart(c.node,c.offset),w.removeAllRanges(),l>u?(w.addRange(_),w.extend(f.node,f.offset)):(_.setEnd(f.node,f.offset),w.addRange(_))))),_=[];for(w=x;w=w.parentNode;)1===w.nodeType&&_.push({element:w,left:w.scrollLeft,top:w.scrollTop});for("function"==typeof x.focus&&x.focus(),x=0;x<_.length;x++)(w=_[x]).element.scrollLeft=w.left,w.element.scrollTop=w.top}$t=!!gn,vn=gn=null,e.current=n,zl=i;do{try{for(x=e;null!==zl;){var k=zl.effectTag;if(36&k&&al(x,zl.alternate,zl),128&k){_=void 0;var S=zl.ref;if(null!==S){var T=zl.stateNode;switch(zl.tag){case 5:_=T;break;default:_=T}"function"==typeof S?S(_):S.current=_}}zl=zl.nextEffect}}catch(e){if(null===zl)throw Error(o(330));bu(zl,e),zl=zl.nextEffect}}while(null!==zl);zl=null,Ni(),kl=a}else e.current=n;if(Ll)Ll=!1,Bl=e,Wl=t;else for(zl=i;null!==zl;)t=zl.nextEffect,zl.nextEffect=null,zl=t;if(0===(t=e.firstPendingTime)&&(Fl=null),1073741823===t?e===Hl?Ul++:(Ul=0,Hl=e):Ul=0,"function"==typeof _u&&_u(n.stateNode,r),Zl(e),jl)throw jl=!1,e=Rl,Rl=null,e;return 0!=(8&kl)||qi(),null}function pu(){for(;null!==zl;){var e=zl.effectTag;0!=(256&e)&&nl(zl.alternate,zl),0==(512&e)||Ll||(Ll=!0,Ui(97,(function(){return gu(),null}))),zl=zl.nextEffect}}function gu(){if(90!==Wl){var e=97<Wl?97:Wl;return Wl=90,Vi(e,vu)}}function vu(){if(null===Bl)return!1;var e=Bl;if(Bl=null,0!=(48&kl))throw Error(o(331));var t=kl;for(kl|=32,e=e.current.firstEffect;null!==e;){try{var n=e;if(0!=(512&n.effectTag))switch(n.tag){case 0:case 11:case 15:case 22:rl(5,n),il(5,n)}}catch(t){if(null===e)throw Error(o(330));bu(e,t)}n=e.nextEffect,e.nextEffect=null,e=n}return kl=t,qi(),!0}function mu(e,t,n){ua(e,t=pl(e,t=Xo(n,t),1073741823)),null!==(e=Kl(e,1073741823))&&Zl(e)}function bu(e,t){if(3===e.tag)mu(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){mu(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Fl||!Fl.has(r))){ua(n,e=gl(n,e=Xo(t,e),1073741823)),null!==(n=Kl(n,1073741823))&&Zl(n);break}}n=n.return}}function yu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),Sl===e&&El===n?Cl===wl||Cl===_l&&1073741823===Ml&&Li()-Nl<500?nu(e,El):Dl=!0:Au(e,n)&&(0!==(t=e.lastPingedTime)&&t<n||(e.lastPingedTime=n,Zl(e)))}function xu(e,t){var n=e.stateNode;null!==n&&n.delete(t),0===(t=0)&&(t=Ql(t=$l(),e,null)),null!==(e=Kl(e,t))&&Zl(e)}vl=function(e,t,n){var r=t.expirationTime;if(null!==e){var i=t.pendingProps;if(e.memoizedProps!==i||di.current)Oo=!0;else{if(r<n){switch(Oo=!1,t.tag){case 3:Lo(t),Po();break;case 5:if(Da(t),4&t.mode&&1!==n&&i.hidden)return t.expirationTime=t.childExpirationTime=1,null;break;case 1:gi(t.type)&&yi(t);break;case 4:Ia(t,t.stateNode.containerInfo);break;case 10:r=t.memoizedProps.value,i=t.type._context,si(Ki,i._currentValue),i._currentValue=r;break;case 13:if(null!==t.memoizedState)return 0!==(r=t.child.childExpirationTime)&&r>=n?Ho(e,t,n):(si(za,1&za.current),null!==(t=Yo(e,t,n))?t.sibling:null);si(za,1&za.current);break;case 19:if(r=t.childExpirationTime>=n,0!=(64&e.effectTag)){if(r)return Qo(e,t,n);t.effectTag|=64}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null),si(za,za.current),!r)return null}return Yo(e,t,n)}Oo=!1}}else Oo=!1;switch(t.expirationTime=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,i=pi(t,fi.current),na(t,n),i=Qa(null,t,r,e,i,n),t.effectTag|=1,"object"==typeof i&&null!==i&&"function"==typeof i.render&&void 0===i.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,gi(r)){var a=!0;yi(t)}else a=!1;t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,aa(t);var l=r.getDerivedStateFromProps;"function"==typeof l&&pa(t,r,l,e),i.updater=ga,t.stateNode=i,i._reactInternalFiber=t,ya(t,r,e,n),t=Fo(null,t,r,!0,a,n)}else t.tag=0,Io(null,t,i,n),t=t.child;return t;case 16:e:{if(i=t.elementType,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,function(e){if(-1===e._status){e._status=0;var t=e._ctor;t=t(),e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}}(i),1!==i._status)throw i._result;switch(i=i._result,t.type=i,a=t.tag=function(e){if("function"==typeof e)return Tu(e)?1:0;if(null!=e){if((e=e.$$typeof)===ue)return 11;if(e===fe)return 14}return 2}(i),e=Yi(i,e),a){case 0:t=jo(null,t,i,e,n);break e;case 1:t=Ro(null,t,i,e,n);break e;case 11:t=Ao(null,t,i,e,n);break e;case 14:t=Do(null,t,i,Yi(i.type,e),r,n);break e}throw Error(o(306,i,""))}return t;case 0:return r=t.type,i=t.pendingProps,jo(e,t,r,i=t.elementType===r?i:Yi(r,i),n);case 1:return r=t.type,i=t.pendingProps,Ro(e,t,r,i=t.elementType===r?i:Yi(r,i),n);case 3:if(Lo(t),r=t.updateQueue,null===e||null===r)throw Error(o(282));if(r=t.pendingProps,i=null!==(i=t.memoizedState)?i.element:null,oa(e,t),ca(t,r,null,n),(r=t.memoizedState.element)===i)Po(),t=Yo(e,t,n);else{if((i=t.stateNode.hydrate)&&(_o=_n(t.stateNode.containerInfo.firstChild),xo=t,i=wo=!0),i)for(n=Ta(t,null,r,n),t.child=n;n;)n.effectTag=-3&n.effectTag|1024,n=n.sibling;else Io(e,t,r,n),Po();t=t.child}return t;case 5:return Da(t),null===e&&To(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,l=i.children,bn(r,i)?l=null:null!==a&&bn(r,a)&&(t.effectTag|=16),zo(e,t),4&t.mode&&1!==n&&i.hidden?(t.expirationTime=t.childExpirationTime=1,t=null):(Io(e,t,l,n),t=t.child),t;case 6:return null===e&&To(t),null;case 13:return Ho(e,t,n);case 4:return Ia(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Sa(t,null,r,n):Io(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,Ao(e,t,r,i=t.elementType===r?i:Yi(r,i),n);case 7:return Io(e,t,t.pendingProps,n),t.child;case 8:case 12:return Io(e,t,t.pendingProps.children,n),t.child;case 10:e:{r=t.type._context,i=t.pendingProps,l=t.memoizedProps,a=i.value;var u=t.type._context;if(si(Ki,u._currentValue),u._currentValue=a,null!==l)if(u=l.value,0===(a=Rr(u,a)?0:0|("function"==typeof r._calculateChangedBits?r._calculateChangedBits(u,a):1073741823))){if(l.children===i.children&&!di.current){t=Yo(e,t,n);break e}}else for(null!==(u=t.child)&&(u.return=t);null!==u;){var s=u.dependencies;if(null!==s){l=u.child;for(var c=s.firstContext;null!==c;){if(c.context===r&&0!=(c.observedBits&a)){1===u.tag&&((c=la(n,null)).tag=2,ua(u,c)),u.expirationTime<n&&(u.expirationTime=n),null!==(c=u.alternate)&&c.expirationTime<n&&(c.expirationTime=n),ta(u.return,n),s.expirationTime<n&&(s.expirationTime=n);break}c=c.next}}else l=10===u.tag&&u.type===t.type?null:u.child;if(null!==l)l.return=u;else for(l=u;null!==l;){if(l===t){l=null;break}if(null!==(u=l.sibling)){u.return=l.return,l=u;break}l=l.return}u=l}Io(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=(a=t.pendingProps).children,na(t,n),r=r(i=ra(i,a.unstable_observedBits)),t.effectTag|=1,Io(e,t,r,n),t.child;case 14:return a=Yi(i=t.type,t.pendingProps),Do(e,t,i,a=Yi(i.type,a),r,n);case 15:return No(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Yi(r,i),null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),t.tag=1,gi(r)?(e=!0,yi(t)):e=!1,na(t,n),ma(t,r,i),ya(t,r,i,n),Fo(null,t,r,!0,e,n);case 19:return Qo(e,t,n)}throw Error(o(156,t.tag))};var _u=null,wu=null;function ku(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function Su(e,t,n,r){return new ku(e,t,n,r)}function Tu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Eu(e,t){var n=e.alternate;return null===n?((n=Su(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childExpirationTime=e.childExpirationTime,n.expirationTime=e.expirationTime,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{expirationTime:t.expirationTime,firstContext:t.firstContext,responders:t.responders},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Cu(e,t,n,r,i,a){var l=2;if(r=e,"function"==typeof e)Tu(e)&&(l=1);else if("string"==typeof e)l=5;else e:switch(e){case ne:return Pu(n.children,i,a,t);case le:l=8,i|=7;break;case re:l=8,i|=1;break;case ie:return(e=Su(12,n,t,8|i)).elementType=ie,e.type=ie,e.expirationTime=a,e;case se:return(e=Su(13,n,t,i)).type=se,e.elementType=se,e.expirationTime=a,e;case ce:return(e=Su(19,n,t,i)).elementType=ce,e.expirationTime=a,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case ae:l=10;break e;case oe:l=9;break e;case ue:l=11;break e;case fe:l=14;break e;case de:l=16,r=null;break e;case he:l=22;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Su(l,n,t,i)).elementType=e,t.type=r,t.expirationTime=a,t}function Pu(e,t,n,r){return(e=Su(7,e,r,t)).expirationTime=n,e}function Mu(e,t,n){return(e=Su(6,e,null,t)).expirationTime=n,e}function Ou(e,t,n){return(t=Su(4,null!==e.children?e.children:[],e.key,t)).expirationTime=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iu(e,t,n){this.tag=t,this.current=null,this.containerInfo=e,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=90,this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function Au(e,t){var n=e.firstSuspendedTime;return e=e.lastSuspendedTime,0!==n&&n>=t&&e<=t}function Du(e,t){var n=e.firstSuspendedTime,r=e.lastSuspendedTime;n<t&&(e.firstSuspendedTime=t),(r>t||0===n)&&(e.lastSuspendedTime=t),t<=e.lastPingedTime&&(e.lastPingedTime=0),t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}function Nu(e,t){t>e.firstPendingTime&&(e.firstPendingTime=t);var n=e.firstSuspendedTime;0!==n&&(t>=n?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t>=e.lastSuspendedTime&&(e.lastSuspendedTime=t+1),t>e.nextKnownPendingLevel&&(e.nextKnownPendingLevel=t))}function zu(e,t){var n=e.lastExpiredTime;(0===n||n>t)&&(e.lastExpiredTime=t)}function ju(e,t,n,r){var i=t.current,a=$l(),l=da.suspense;a=Ql(a,i,l);e:if(n){t:{if(Je(n=n._reactInternalFiber)!==n||1!==n.tag)throw Error(o(170));var u=n;do{switch(u.tag){case 3:u=u.stateNode.context;break t;case 1:if(gi(u.type)){u=u.stateNode.__reactInternalMemoizedMergedChildContext;break t}}u=u.return}while(null!==u);throw Error(o(171))}if(1===n.tag){var s=n.type;if(gi(s)){n=bi(n,s,u);break e}}n=u}else n=ci;return null===t.context?t.context=n:t.pendingContext=n,(t=la(a,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),ua(i,t),Yl(i,a),a}function Ru(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:default:return e.child.stateNode}}function Fu(e,t){null!==(e=e.memoizedState)&&null!==e.dehydrated&&e.retryTime<t&&(e.retryTime=t)}function Lu(e,t){Fu(e,t),(e=e.alternate)&&Fu(e,t)}function Bu(e,t,n){var r=new Iu(e,t,n=null!=n&&!0===n.hydrate),i=Su(3,null,null,2===t?7:1===t?3:0);r.current=i,i.stateNode=r,aa(i),e[En]=r.current,n&&0!==t&&function(e,t){var n=Xe(t);Et.forEach((function(e){pt(e,t,n)})),Ct.forEach((function(e){pt(e,t,n)}))}(0,9===e.nodeType?e:e.ownerDocument),this._internalRoot=r}function Wu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Vu(e,t,n,r,i){var a=n._reactRootContainer;if(a){var o=a._internalRoot;if("function"==typeof i){var l=i;i=function(){var e=Ru(o);l.call(e)}}ju(t,o,e,i)}else{if(a=n._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var n;n=e.lastChild;)e.removeChild(n);return new Bu(e,0,t?{hydrate:!0}:void 0)}(n,r),o=a._internalRoot,"function"==typeof i){var u=i;i=function(){var e=Ru(o);u.call(e)}}tu((function(){ju(t,o,e,i)}))}return Ru(o)}function Uu(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:te,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}function Hu(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Wu(t))throw Error(o(200));return Uu(e,t,null,n)}Bu.prototype.render=function(e){ju(e,this._internalRoot,null,null)},Bu.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;ju(null,e,null,(function(){t[En]=null}))},gt=function(e){if(13===e.tag){var t=Qi($l(),150,100);Yl(e,t),Lu(e,t)}},vt=function(e){13===e.tag&&(Yl(e,3),Lu(e,3))},mt=function(e){if(13===e.tag){var t=$l();Yl(e,t=Ql(t,e,null)),Lu(e,t)}},P=function(e,t,n){switch(t){case"input":if(Te(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=On(r);if(!i)throw Error(o(90));_e(r),Te(r,i)}}}break;case"textarea":Ae(e,n);break;case"select":null!=(t=n.value)&&Me(e,!!n.multiple,t,!1)}},N=eu,z=function(e,t,n,r,i){var a=kl;kl|=4;try{return Vi(98,e.bind(null,t,n,r,i))}finally{0===(kl=a)&&qi()}},j=function(){0==(49&kl)&&(function(){if(null!==Vl){var e=Vl;Vl=null,e.forEach((function(e,t){zu(t,e),Zl(t)})),qi()}}(),gu())},R=function(e,t){var n=kl;kl|=2;try{return e(t)}finally{0===(kl=n)&&qi()}};var qu,$u,Qu={Events:[Pn,Mn,On,E,k,Rn,function(e){it(e,jn)},A,D,Zt,lt,gu,{current:!1}]};$u=(qu={findFiberByHostInstance:Cn,bundleType:0,version:"16.13.1",rendererPackageName:"react-dom"}).findFiberByHostInstance,function(e){if("undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)return!0;try{var n=t.inject(e);_u=function(e){try{t.onCommitFiberRoot(n,e,void 0,64==(64&e.current.effectTag))}catch(e){}},wu=function(e){try{t.onCommitFiberUnmount(n,e)}catch(e){}}}catch(e){}}(i({},qu,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:G.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=nt(e))?null:e.stateNode},findFiberByHostInstance:function(e){return $u?$u(e):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null})),t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Qu,t.createPortal=Hu,t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternalFiber;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw Error(o(268,Object.keys(e)))}return e=null===(e=nt(t))?null:e.stateNode},t.flushSync=function(e,t){if(0!=(48&kl))throw Error(o(187));var n=kl;kl|=1;try{return Vi(99,e.bind(null,t))}finally{kl=n,qi()}},t.hydrate=function(e,t,n){if(!Wu(t))throw Error(o(200));return Vu(null,e,t,!0,n)},t.render=function(e,t,n){if(!Wu(t))throw Error(o(200));return Vu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Wu(e))throw Error(o(40));return!!e._reactRootContainer&&(tu((function(){Vu(null,null,e,!1,(function(){e._reactRootContainer=null,e[En]=null}))})),!0)},t.unstable_batchedUpdates=eu,t.unstable_createPortal=function(e,t){return Hu(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Wu(n))throw Error(o(200));if(null==e||void 0===e._reactInternalFiber)throw Error(o(38));return Vu(e,t,n,!1,r)},t.version="16.13.1"},function(e,t,n){"use strict";e.exports=n(51)},function(e,t,n){"use strict";
/** @license React v0.19.1
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r,i,a,o,l;if("undefined"==typeof window||"function"!=typeof MessageChannel){var u=null,s=null,c=function(){if(null!==u)try{var e=t.unstable_now();u(!0,e),u=null}catch(e){throw setTimeout(c,0),e}},f=Date.now();t.unstable_now=function(){return Date.now()-f},r=function(e){null!==u?setTimeout(r,0,e):(u=e,setTimeout(c,0))},i=function(e,t){s=setTimeout(e,t)},a=function(){clearTimeout(s)},o=function(){return!1},l=t.unstable_forceFrameRate=function(){}}else{var d=window.performance,h=window.Date,p=window.setTimeout,g=window.clearTimeout;if("undefined"!=typeof console){var v=window.cancelAnimationFrame;"function"!=typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!=typeof v&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"==typeof d&&"function"==typeof d.now)t.unstable_now=function(){return d.now()};else{var m=h.now();t.unstable_now=function(){return h.now()-m}}var b=!1,y=null,x=-1,_=5,w=0;o=function(){return t.unstable_now()>=w},l=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):_=0<e?Math.floor(1e3/e):5};var k=new MessageChannel,S=k.port2;k.port1.onmessage=function(){if(null!==y){var e=t.unstable_now();w=e+_;try{y(!0,e)?S.postMessage(null):(b=!1,y=null)}catch(e){throw S.postMessage(null),e}}else b=!1},r=function(e){y=e,b||(b=!0,S.postMessage(null))},i=function(e,n){x=p((function(){e(t.unstable_now())}),n)},a=function(){g(x),x=-1}}function T(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,i=e[r];if(!(void 0!==i&&0<P(i,t)))break e;e[r]=t,e[n]=i,n=r}}function E(e){return void 0===(e=e[0])?null:e}function C(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length;r<i;){var a=2*(r+1)-1,o=e[a],l=a+1,u=e[l];if(void 0!==o&&0>P(o,n))void 0!==u&&0>P(u,o)?(e[r]=u,e[l]=n,r=l):(e[r]=o,e[a]=n,r=a);else{if(!(void 0!==u&&0>P(u,n)))break e;e[r]=u,e[l]=n,r=l}}}return t}return null}function P(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var M=[],O=[],I=1,A=null,D=3,N=!1,z=!1,j=!1;function R(e){for(var t=E(O);null!==t;){if(null===t.callback)C(O);else{if(!(t.startTime<=e))break;C(O),t.sortIndex=t.expirationTime,T(M,t)}t=E(O)}}function F(e){if(j=!1,R(e),!z)if(null!==E(M))z=!0,r(L);else{var t=E(O);null!==t&&i(F,t.startTime-e)}}function L(e,n){z=!1,j&&(j=!1,a()),N=!0;var r=D;try{for(R(n),A=E(M);null!==A&&(!(A.expirationTime>n)||e&&!o());){var l=A.callback;if(null!==l){A.callback=null,D=A.priorityLevel;var u=l(A.expirationTime<=n);n=t.unstable_now(),"function"==typeof u?A.callback=u:A===E(M)&&C(M),R(n)}else C(M);A=E(M)}if(null!==A)var s=!0;else{var c=E(O);null!==c&&i(F,c.startTime-n),s=!1}return s}finally{A=null,D=r,N=!1}}function B(e){switch(e){case 1:return-1;case 2:return 250;case 5:return 1073741823;case 4:return 1e4;default:return 5e3}}var W=l;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){z||N||(z=!0,r(L))},t.unstable_getCurrentPriorityLevel=function(){return D},t.unstable_getFirstCallbackNode=function(){return E(M)},t.unstable_next=function(e){switch(D){case 1:case 2:case 3:var t=3;break;default:t=D}var n=D;D=t;try{return e()}finally{D=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=W,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=D;D=e;try{return t()}finally{D=n}},t.unstable_scheduleCallback=function(e,n,o){var l=t.unstable_now();if("object"==typeof o&&null!==o){var u=o.delay;u="number"==typeof u&&0<u?l+u:l,o="number"==typeof o.timeout?o.timeout:B(e)}else o=B(e),u=l;return e={id:I++,callback:n,priorityLevel:e,startTime:u,expirationTime:o=u+o,sortIndex:-1},u>l?(e.sortIndex=u,T(O,e),null===E(M)&&e===E(O)&&(j?a():j=!0,i(F,u-l))):(e.sortIndex=o,T(M,e),z||N||(z=!0,r(L))),e},t.unstable_shouldYield=function(){var e=t.unstable_now();R(e);var n=E(M);return n!==A&&null!==A&&null!==n&&null!==n.callback&&n.startTime<=e&&n.expirationTime<A.expirationTime||o()},t.unstable_wrapCallback=function(e){var t=D;return function(){var n=D;D=t;try{return e.apply(this,arguments)}finally{D=n}}}},function(e,t,n){(t=e.exports=n(25)(!1)).push([e.i,'.EmLIa_wVWIt8wmTs18w-U {\n  width: 100%;\n  height: 100%;\n}\n\n._2ZTL8-kLGsjUk-gE3ZHsk- {\n}\n\n.dC7EjkUOwriYnod5o2vJZ {\n  font-size: 1.5em;\n  font-weight: bold;\n  padding: 16px 32px;\n  text-transform: uppercase;\n}\n\n.TmQ8PGI5X9VnKQu_iEOb3 {\n  border-bottom: solid 1px #ccd0d5;\n  padding-left: 32px;\n  background-color: white;\n  padding-top: 4px;\n}\n\n.TmQ8PGI5X9VnKQu_iEOb3 ul {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n._1_1yIeyboIEpGlqr_OxVp3 {\n  color: #606770;\n  display: inline-block;\n  font-size: 1.2em;\n  line-height: 2em;\n  margin: 0 8px;\n  padding: 0 12px;\n}\n\n.pUk3whxiq3zwaLv0nbAE {\n  color: black;\n  font-weight: 600;\n  border-bottom: solid 4px #ee4c2c;\n}\n\n._28wOm8iUKcYpBvDeP08PSY {\n  background-color: white;\n  padding: 0 24px;\n}\n\n._28wOm8iUKcYpBvDeP08PSY,\n._36OrXjsxEXp-wmzlVQWmzr {\n  align-content: space-between;\n  display: flex;\n  flex-direction: row;\n}\n\n._3Hm_oXX3Q3lbh-pQWG1mdZ {\n  width: 33%;\n  padding: 12px 8px;\n}\n\n.cubo-RLJcxrQG1vT6C8FG {\n  font-weight: bold;\n  color: #1c1e21;\n  padding-bottom: 12px;\n}\n\n._3WHY45awLkW-NF38iYuZZm {\n  color: #606770;\n}\n\n._1mDG_HcTpVJtfkiFw0571p {\n  flex-grow: 1;\n  align-self: center;\n  width: auto;\n}\n\n.gZAUh-2PI6gBZmgs_omEV {\n  background: none;\n  border: none;\n  border-radius: 0;\n  text-align-last: center;\n  padding: 0 8px;\n  margin: 0 4px;\n  border-bottom: solid 1px #1c1e21;\n  font-size: 1em;\n  appearance: none;\n  color: #1c1e21;\n}\n\n._5Nxm8HOP5KncFZHxqidKS {\n  background: none;\n  box-shadow: none;\n  border: none;\n  font-size: 1em;\n  border-bottom: solid 1px #1c1e21;\n  text-align-last: center;\n  padding: 0;\n  margin: 0;\n}\n\n._22-T0dg7AHKfzKndKm51_u {\n  width: 100px;\n}\n\n._3xecd8WdQkArX60zoBoav6 {\n  display: block;\n}\n\n._1GtsmOrEFNypnh_cburSmO {\n  margin: 8px 0;\n}\n\n._2QUgGtWJ8exi7DnXH2nDDf {\n  border: solid 1px #ee4c2c;\n  background: white;\n  text-align: center;\n  font-weight: 600;\n  font-size: 1em;\n  border-radius: 4px;\n  padding: 6px 8px;\n  display: inline-block;\n  cursor: pointer;\n}\n\n._3kl8FFTNNz26NlcM9IiaMl {\n  font-size: 1.1em;\n  padding: 8px 10px;\n}\n\n._2bVylrL0eFyMIhahGdhUo5 {\n  color: #ee4c2c;\n}\n\n._2bVylrL0eFyMIhahGdhUo5:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n  border: solid 1px #ee4c2c;\n}\n\n._3_ZOrmFJwPMrHhT0to-f1p {\n  background-color: #ee4c2c;\n  color: white;\n}\n\n._3_ZOrmFJwPMrHhT0to-f1p:hover {\n  background-color: #d7725e;\n}\n\n._16_V-89OJI2Qfea8ZXkrNB {\n  display: block;\n}\n\n._3tG-5LdAfCaY2e3Ie9qqL- {\n  margin-top: 150px;\n  position: absolute;\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n  display: flex;\n}\n\n._3PpY5qX71t0QwGChxIgtUb {\n  margin: 16px;\n  padding: 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.18);\n  transition: opacity 0.2s; /* for loading */\n  overflow-y: scroll;\n}\n\n._3N1t_k-WyWyp6FPnGR4xy0 {\n  font-weight: 700;\n  border-bottom: 2px solid #c1c1c1;\n  color: #1c1e21;\n  padding-bottom: 2px;\n  margin-bottom: 15px;\n}\n\n._1rhUf8JTF1ExmnIRAvF5f8 {\n  opacity: 0.5;\n  pointer-events: none; /* disables all interactions inside panel */\n}\n\n.jQ5-8qHl9HddQrsBiJ3SP {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.V0VkMm_bdLRP5Hxvq1SJh {\n  padding: 0 8px;\n  flex: 1;\n}\n\n.lRmz6X4Iotro6NWp32dSq {\n  flex-grow: 3;\n}\n\n._104bmvRbfsxQuLVD1JrnaM {\n  display: flex;\n}\n\n.QRhgBAqi2cQVjETm5oMIJ + .QRhgBAqi2cQVjETm5oMIJ {\n  padding: 0 8px;\n}\n\n.OqZ7mfgqS7e9Uwdr-rXRo img {\n  height: 200px;\n  width: auto;\n}\n\n.bXsmVI2i0AsYeYi0h8Z0r {\n  text-align: center;\n}\n\n.lRMgNZrdWi1iJRAPY3XIK {\n  padding: 2px 0;\n  display: flex;\n}\n\n._3ZQEznitQ3EvQLcxaPIpr_ {\n  width: 10px;\n  border-radius: 2px;\n  flex-shrink: 0;\n}\n\n._2T6g5BfVnwDMr-syraZiT7 {\n  padding-left: 8px;\n}\n\n._1OlYc8FtuUr4l_oJ31XyA5 {\n  background-color: #80aaff;\n}\n\n._3HaUd3icirPZ27HnIA_m8d {\n  background-color: rgba(128, 170, 255, 0.6);\n}\n\n._34iGz0iygpjncEtYd1-2Za {\n  background-color: #e79c8d;\n}\n\n._3rU-9PcwBWZzMv3RZxKzBr {\n  background-color: #d45c43;\n}\n\n.uGhYK0Kboyop9nAMscYnp {\n  background: #c6c6c6;\n}\n\n._16EWPZkzNAuwBwHoujaXAH {\n  background-color: auto;\n}\n\n._3XDBu6iLE7Ylyx-CzIas7v {\n  position: relative;\n  display: inline-block;\n  border-radius: 2px;\n  padding: 2px;\n}\n\n._1UBBTipFfHdzcG6ggqBwZQ {\n  z-index: 999999;\n  max-width: 200px;\n  background: rgba(0, 0, 0, 0.75);\n  position: absolute;\n  text-align: center;\n  border-radius: 4px;\n  color: white;\n  padding: 4px;\n  font-size: 1.1em;\n  font-weight: 600;\n  visibility: hidden;\n  width: 80px;\n  bottom: 100%;\n  left: 50%;\n  margin-left: -40px;\n}\n\n._1UBBTipFfHdzcG6ggqBwZQ::after {\n  z-index: 999999;\n  content: " ";\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -6px;\n  border-style: solid;\n  border-width: 6px;\n  border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;\n}\n\n._3XDBu6iLE7Ylyx-CzIas7v:hover ._1UBBTipFfHdzcG6ggqBwZQ {\n  visibility: visible;\n}\n\n._3uz04AH7KJehvP1MuGV9vo {\n  display: inline-flex;\n  justify-content: space-between;\n  width: 20%;\n  padding-right: 8px;\n}\n\n._1RZJtt2RqxVkeUWp9uDBmD {\n  font-weight: 600;\n}\n\n._3hUkSxm05saPTknAcWenGn {\n  display: inline-block;\n}\n\n._3CeirjNhJ92_t1HmUAfFel {\n  display: inline-block;\n  width: 70%;\n}\n._2EzLs-9pjvm2rjBPZA6rxe {\n  display: inline-block;\n  border-radius: 4px;\n  height: 12px;\n}\n\n._2ebPTzGTGmULr8Lk8TwNR5 {\n  background: #80aaff;\n}\n\n.lMGpwS0jv9H3GDr42g14E {\n  background: #d45c43;\n}\n\n._11TUp-BCWLn6oCaXqJlrZW {\n  display: inline-block;\n  width: 64px;\n  height: 64px;\n}\n\n._11TUp-BCWLn6oCaXqJlrZW:after {\n  content: " ";\n  display: block;\n  width: 46px;\n  height: 46px;\n  margin: 1px;\n  border-radius: 50%;\n  border: 5px solid #ee4c2c;\n  border-color: #ee4c2c transparent #ee4c2c transparent;\n  animation: _11TUp-BCWLn6oCaXqJlrZW 1.2s linear infinite;\n}\n\n@keyframes _11TUp-BCWLn6oCaXqJlrZW {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n._17AgJYsvEJfVivi-DotPFo {\n  display: flex;\n}\n\n._2maC4Qg5ZzqH-d_3q58iMW {\n  display: block;\n  height: 2em;\n  font-size: 16px;\n  font-weight: 800;\n}\n\n._2y9fc55zZrUgjeYLLXwJt6 {\n  display: block;\n  height: 2em;\n}\n\n._2Sji9Z_pHe_9BIh3gRHDX_ {\n  width: 100%;\n  border-bottom: 2px solid #c1c1c1;\n  margin: 10px 0px;\n}\n',""]),t.locals={app:"EmLIa_wVWIt8wmTs18w-U",header:"_2ZTL8-kLGsjUk-gE3ZHsk-",header__name:"dC7EjkUOwriYnod5o2vJZ",header__nav:"TmQ8PGI5X9VnKQu_iEOb3",header__nav__item:"_1_1yIeyboIEpGlqr_OxVp3","header__nav__item--active":"pUk3whxiq3zwaLv0nbAE","filter-panel":"_28wOm8iUKcYpBvDeP08PSY",viz__panel:"_36OrXjsxEXp-wmzlVQWmzr","filter-panel__column":"_3Hm_oXX3Q3lbh-pQWG1mdZ","filter-panel__column__title":"cubo-RLJcxrQG1vT6C8FG","filter-panel__column__body":"_3WHY45awLkW-NF38iYuZZm","filter-panel__column--end":"_1mDG_HcTpVJtfkiFw0571p",select:"gZAUh-2PI6gBZmgs_omEV",input:"_5Nxm8HOP5KncFZHxqidKS","input--narrow":"_22-T0dg7AHKfzKndKm51_u",row:"_3xecd8WdQkArX60zoBoav6","row--padding":"_1GtsmOrEFNypnh_cburSmO",btn:"_2QUgGtWJ8exi7DnXH2nDDf","btn--large":"_3kl8FFTNNz26NlcM9IiaMl","btn--outline":"_2bVylrL0eFyMIhahGdhUo5","btn--solid":"_3_ZOrmFJwPMrHhT0to-f1p",viz:"_16_V-89OJI2Qfea8ZXkrNB",loading:"_3tG-5LdAfCaY2e3Ie9qqL-",panel:"_3PpY5qX71t0QwGChxIgtUb",panel__column__title:"_3N1t_k-WyWyp6FPnGR4xy0","panel--loading":"_1rhUf8JTF1ExmnIRAvF5f8","panel--center":"jQ5-8qHl9HddQrsBiJ3SP",panel__column:"V0VkMm_bdLRP5Hxvq1SJh","panel__column--stretch":"lRmz6X4Iotro6NWp32dSq",gallery:"_104bmvRbfsxQuLVD1JrnaM",gallery__item:"QRhgBAqi2cQVjETm5oMIJ",gallery__item__image:"OqZ7mfgqS7e9Uwdr-rXRo",gallery__item__description:"bXsmVI2i0AsYeYi0h8Z0r","bar-chart__group":"lRMgNZrdWi1iJRAPY3XIK","bar-chart__group__bar":"_3ZQEznitQ3EvQLcxaPIpr_","bar-chart__group__title":"_2T6g5BfVnwDMr-syraZiT7","percentage-blue":"_1OlYc8FtuUr4l_oJ31XyA5","percentage-light-blue":"_3HaUd3icirPZ27HnIA_m8d","percentage-light-red":"_34iGz0iygpjncEtYd1-2Za","percentage-red":"_3rU-9PcwBWZzMv3RZxKzBr","percentage-gray":"uGhYK0Kboyop9nAMscYnp","percentage-white":"_16EWPZkzNAuwBwHoujaXAH","text-feature-word":"_3XDBu6iLE7Ylyx-CzIas7v",tooltip__label:"_1UBBTipFfHdzcG6ggqBwZQ","general-feature__label-container":"_3uz04AH7KJehvP1MuGV9vo","general-feature__label":"_1RZJtt2RqxVkeUWp9uDBmD","general-feature__percent":"_3hUkSxm05saPTknAcWenGn","general-feature__bar-container":"_3CeirjNhJ92_t1HmUAfFel","general-feature__bar":"_2EzLs-9pjvm2rjBPZA6rxe","general-feature__bar__positive":"_2ebPTzGTGmULr8Lk8TwNR5","general-feature__bar__negative":"lMGpwS0jv9H3GDr42g14E",spinner:"_11TUp-BCWLn6oCaXqJlrZW","visualization-container":"_17AgJYsvEJfVivi-DotPFo","model-number":"_2maC4Qg5ZzqH-d_3q58iMW","model-number-spacer":"_2y9fc55zZrUgjeYLLXwJt6","model-separator":"_2Sji9Z_pHe_9BIh3gRHDX_"}},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var i,a=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(a)?e:(i=0===a.indexOf("//")?a:0===a.indexOf("/")?n+a:r+a.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")}))}},function(e,t,n){"use strict";var r=n(55);function i(){}function a(){}a.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,a,o){if(o!==r){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:i};return n.PropTypes=n,n}},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){"use strict";var r=n(0);e.exports=function(e){return r.createElement("button",{type:"button",className:e.classNames.selectedTag,title:"Click to remove tag",onClick:e.onDelete},r.createElement("span",{className:e.classNames.selectedTagName},e.tag.name))}},function(e,t,n){"use strict";var r=n(0),i={position:"absolute",width:0,height:0,visibility:"hidden",overflow:"scroll",whiteSpace:"pre"},a=["fontSize","fontFamily","fontWeight","fontStyle","letterSpacing","textTransform"],o=function(e){function t(t){e.call(this,t),this.state={inputWidth:null}}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.componentDidMount=function(){this.props.autoresize&&(this.copyInputStyles(),this.updateInputWidth()),this.props.autofocus&&this.input.focus()},t.prototype.componentDidUpdate=function(e){var t=e.query,n=e.placeholder;t===this.props.query&&n===this.props.placeholder||this.updateInputWidth()},t.prototype.copyInputStyles=function(){var e=this,t=window.getComputedStyle(this.input);a.forEach((function(n){e.sizer.style[n]=t[n]}))},t.prototype.updateInputWidth=function(){var e;this.props.autoresize&&(e=Math.ceil(this.sizer.scrollWidth)+2),e!==this.state.inputWidth&&this.setState({inputWidth:e})},t.prototype.render=function(){var e=this,t=this.props,n=t.inputAttributes,a=t.inputEventHandlers,o=t.query,l=t.placeholder,u=t.expandable,s=t.listboxId,c=t.selectedIndex;return r.createElement("div",{className:this.props.classNames.searchInput},r.createElement("input",Object.assign({},n,a,{ref:function(t){e.input=t},value:o,placeholder:l,role:"combobox","aria-autocomplete":"list","aria-label":l,"aria-owns":s,"aria-activedescendant":c>-1?s+"-"+c:null,"aria-expanded":u,style:{width:this.state.inputWidth}})),r.createElement("div",{ref:function(t){e.sizer=t},style:i},o||l))},t}(r.Component);e.exports=o},function(e,t,n){"use strict";var r=n(0);function i(e){return e.replace(/[-\\^$*+?.()|[\]{}]/g,"\\$&")}function a(e,t){if(t){var n=RegExp(i(t),"gi");e=e.replace(n,"<mark>$&</mark>")}return{__html:e}}function o(e,t,n,r,a){if(!r){var o=new RegExp("(?:^|\\s)"+i(e),"i");r=function(e){return o.test(e.name)}}var l=t.filter((function(t){return r(t,e)})).slice(0,n);return 0===l.length&&a&&l.push({id:0,name:a,disabled:!0,disableMarkIt:!0}),l}var l=function(e){function t(t){e.call(this,t),this.state={options:o(this.props.query,this.props.suggestions,this.props.maxSuggestionsLength,this.props.suggestionsFilter,this.props.noSuggestionsText)}}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.componentWillReceiveProps=function(e){this.setState({options:o(e.query,e.suggestions,e.maxSuggestionsLength,e.suggestionsFilter,e.noSuggestionsText)})},t.prototype.handleMouseDown=function(e,t){t.preventDefault(),this.props.addTag(e)},t.prototype.render=function(){var e=this;if(!this.props.expandable||!this.state.options.length)return null;var t=this.state.options.map((function(t,n){var i=e.props.listboxId+"-"+n,o=[];return e.props.selectedIndex===n&&o.push(e.props.classNames.suggestionActive),t.disabled&&o.push(e.props.classNames.suggestionDisabled),r.createElement("li",{id:i,key:i,role:"option",className:o.join(" "),"aria-disabled":!0===t.disabled,onMouseDown:e.handleMouseDown.bind(e,t)},t.disableMarkIt?t.name:r.createElement("span",{dangerouslySetInnerHTML:a(t.name,e.props.query,t.markInput)}))}));return r.createElement("div",{className:this.props.classNames.suggestions},r.createElement("ul",{role:"listbox",id:this.props.listboxId},t))},t}(r.Component);e.exports=l},function(e,t){var n,r,i=e.exports={};function a(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function l(e){if(n===setTimeout)return setTimeout(e,0);if((n===a||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:a}catch(e){n=a}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(e){r=o}}();var u,s=[],c=!1,f=-1;function d(){c&&u&&(c=!1,u.length?s=u.concat(s):f=-1,s.length&&h())}function h(){if(!c){var e=l(d);c=!0;for(var t=s.length;t;){for(u=s,s=[];++f<t;)u&&u[f].run();f=-1,t=s.length}u=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function g(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];s.push(new p(e,t)),1!==s.length||c||l(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){
/*!
 * Chart.js v2.9.4
 * https://www.chartjs.org
 * (c) 2020 Chart.js Contributors
 * Released under the MIT License
 */
e.exports=function(e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;var t={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},n=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e){var n={};for(var r in t)t.hasOwnProperty(r)&&(n[t[r]]=r);var i=e.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var a in i)if(i.hasOwnProperty(a)){if(!("channels"in i[a]))throw new Error("missing channels property: "+a);if(!("labels"in i[a]))throw new Error("missing channel labels property: "+a);if(i[a].labels.length!==i[a].channels)throw new Error("channel and label counts mismatch: "+a);var o=i[a].channels,l=i[a].labels;delete i[a].channels,delete i[a].labels,Object.defineProperty(i[a],"channels",{value:o}),Object.defineProperty(i[a],"labels",{value:l})}i.rgb.hsl=function(e){var t,n,r=e[0]/255,i=e[1]/255,a=e[2]/255,o=Math.min(r,i,a),l=Math.max(r,i,a),u=l-o;return l===o?t=0:r===l?t=(i-a)/u:i===l?t=2+(a-r)/u:a===l&&(t=4+(r-i)/u),(t=Math.min(60*t,360))<0&&(t+=360),n=(o+l)/2,[t,100*(l===o?0:n<=.5?u/(l+o):u/(2-l-o)),100*n]},i.rgb.hsv=function(e){var t,n,r,i,a,o=e[0]/255,l=e[1]/255,u=e[2]/255,s=Math.max(o,l,u),c=s-Math.min(o,l,u),f=function(e){return(s-e)/6/c+.5};return 0===c?i=a=0:(a=c/s,t=f(o),n=f(l),r=f(u),o===s?i=r-n:l===s?i=1/3+t-r:u===s&&(i=2/3+n-t),i<0?i+=1:i>1&&(i-=1)),[360*i,100*a,100*s]},i.rgb.hwb=function(e){var t=e[0],n=e[1],r=e[2];return[i.rgb.hsl(e)[0],1/255*Math.min(t,Math.min(n,r))*100,100*(r=1-1/255*Math.max(t,Math.max(n,r)))]},i.rgb.cmyk=function(e){var t,n=e[0]/255,r=e[1]/255,i=e[2]/255;return[100*((1-n-(t=Math.min(1-n,1-r,1-i)))/(1-t)||0),100*((1-r-t)/(1-t)||0),100*((1-i-t)/(1-t)||0),100*t]},i.rgb.keyword=function(e){var r=n[e];if(r)return r;var i,a,o,l=1/0;for(var u in t)if(t.hasOwnProperty(u)){var s=t[u],c=(a=e,o=s,Math.pow(a[0]-o[0],2)+Math.pow(a[1]-o[1],2)+Math.pow(a[2]-o[2],2));c<l&&(l=c,i=u)}return i},i.keyword.rgb=function(e){return t[e]},i.rgb.xyz=function(e){var t=e[0]/255,n=e[1]/255,r=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)),100*(.2126*t+.7152*n+.0722*r),100*(.0193*t+.1192*n+.9505*r)]},i.rgb.lab=function(e){var t=i.rgb.xyz(e),n=t[0],r=t[1],a=t[2];return r/=100,a/=108.883,n=(n/=95.047)>.008856?Math.pow(n,1/3):7.787*n+16/116,[116*(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116)-16,500*(n-r),200*(r-(a=a>.008856?Math.pow(a,1/3):7.787*a+16/116))]},i.hsl.rgb=function(e){var t,n,r,i,a,o=e[0]/360,l=e[1]/100,u=e[2]/100;if(0===l)return[a=255*u,a,a];t=2*u-(n=u<.5?u*(1+l):u+l-u*l),i=[0,0,0];for(var s=0;s<3;s++)(r=o+1/3*-(s-1))<0&&r++,r>1&&r--,a=6*r<1?t+6*(n-t)*r:2*r<1?n:3*r<2?t+(n-t)*(2/3-r)*6:t,i[s]=255*a;return i},i.hsl.hsv=function(e){var t=e[0],n=e[1]/100,r=e[2]/100,i=n,a=Math.max(r,.01);return n*=(r*=2)<=1?r:2-r,i*=a<=1?a:2-a,[t,100*(0===r?2*i/(a+i):2*n/(r+n)),(r+n)/2*100]},i.hsv.rgb=function(e){var t=e[0]/60,n=e[1]/100,r=e[2]/100,i=Math.floor(t)%6,a=t-Math.floor(t),o=255*r*(1-n),l=255*r*(1-n*a),u=255*r*(1-n*(1-a));switch(r*=255,i){case 0:return[r,u,o];case 1:return[l,r,o];case 2:return[o,r,u];case 3:return[o,l,r];case 4:return[u,o,r];case 5:return[r,o,l]}},i.hsv.hsl=function(e){var t,n,r,i=e[0],a=e[1]/100,o=e[2]/100,l=Math.max(o,.01);return r=(2-a)*o,n=a*l,[i,100*(n=(n/=(t=(2-a)*l)<=1?t:2-t)||0),100*(r/=2)]},i.hwb.rgb=function(e){var t,n,r,i,a,o,l,u=e[0]/360,s=e[1]/100,c=e[2]/100,f=s+c;switch(f>1&&(s/=f,c/=f),r=6*u-(t=Math.floor(6*u)),0!=(1&t)&&(r=1-r),i=s+r*((n=1-c)-s),t){default:case 6:case 0:a=n,o=i,l=s;break;case 1:a=i,o=n,l=s;break;case 2:a=s,o=n,l=i;break;case 3:a=s,o=i,l=n;break;case 4:a=i,o=s,l=n;break;case 5:a=n,o=s,l=i}return[255*a,255*o,255*l]},i.cmyk.rgb=function(e){var t=e[0]/100,n=e[1]/100,r=e[2]/100,i=e[3]/100;return[255*(1-Math.min(1,t*(1-i)+i)),255*(1-Math.min(1,n*(1-i)+i)),255*(1-Math.min(1,r*(1-i)+i))]},i.xyz.rgb=function(e){var t,n,r,i=e[0]/100,a=e[1]/100,o=e[2]/100;return n=-.9689*i+1.8758*a+.0415*o,r=.0557*i+-.204*a+1.057*o,t=(t=3.2406*i+-1.5372*a+-.4986*o)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,[255*(t=Math.min(Math.max(0,t),1)),255*(n=Math.min(Math.max(0,n),1)),255*(r=Math.min(Math.max(0,r),1))]},i.xyz.lab=function(e){var t=e[0],n=e[1],r=e[2];return n/=100,r/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(t-n),200*(n-(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116))]},i.lab.xyz=function(e){var t,n,r,i=e[0];t=e[1]/500+(n=(i+16)/116),r=n-e[2]/200;var a=Math.pow(n,3),o=Math.pow(t,3),l=Math.pow(r,3);return n=a>.008856?a:(n-16/116)/7.787,t=o>.008856?o:(t-16/116)/7.787,r=l>.008856?l:(r-16/116)/7.787,[t*=95.047,n*=100,r*=108.883]},i.lab.lch=function(e){var t,n=e[0],r=e[1],i=e[2];return(t=360*Math.atan2(i,r)/2/Math.PI)<0&&(t+=360),[n,Math.sqrt(r*r+i*i),t]},i.lch.lab=function(e){var t,n=e[0],r=e[1];return t=e[2]/360*2*Math.PI,[n,r*Math.cos(t),r*Math.sin(t)]},i.rgb.ansi16=function(e){var t=e[0],n=e[1],r=e[2],a=1 in arguments?arguments[1]:i.rgb.hsv(e)[2];if(0===(a=Math.round(a/50)))return 30;var o=30+(Math.round(r/255)<<2|Math.round(n/255)<<1|Math.round(t/255));return 2===a&&(o+=60),o},i.hsv.ansi16=function(e){return i.rgb.ansi16(i.hsv.rgb(e),e[2])},i.rgb.ansi256=function(e){var t=e[0],n=e[1],r=e[2];return t===n&&n===r?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5)},i.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},i.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var n;return e-=16,[Math.floor(e/36)/5*255,Math.floor((n=e%36)/6)/5*255,n%6/5*255]},i.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},i.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var n=t[0];3===t[0].length&&(n=n.split("").map((function(e){return e+e})).join(""));var r=parseInt(n,16);return[r>>16&255,r>>8&255,255&r]},i.rgb.hcg=function(e){var t,n=e[0]/255,r=e[1]/255,i=e[2]/255,a=Math.max(Math.max(n,r),i),o=Math.min(Math.min(n,r),i),l=a-o;return t=l<=0?0:a===n?(r-i)/l%6:a===r?2+(i-n)/l:4+(n-r)/l+4,t/=6,[360*(t%=1),100*l,100*(l<1?o/(1-l):0)]},i.hsl.hcg=function(e){var t=e[1]/100,n=e[2]/100,r=1,i=0;return(r=n<.5?2*t*n:2*t*(1-n))<1&&(i=(n-.5*r)/(1-r)),[e[0],100*r,100*i]},i.hsv.hcg=function(e){var t=e[1]/100,n=e[2]/100,r=t*n,i=0;return r<1&&(i=(n-r)/(1-r)),[e[0],100*r,100*i]},i.hcg.rgb=function(e){var t=e[0]/360,n=e[1]/100,r=e[2]/100;if(0===n)return[255*r,255*r,255*r];var i,a=[0,0,0],o=t%1*6,l=o%1,u=1-l;switch(Math.floor(o)){case 0:a[0]=1,a[1]=l,a[2]=0;break;case 1:a[0]=u,a[1]=1,a[2]=0;break;case 2:a[0]=0,a[1]=1,a[2]=l;break;case 3:a[0]=0,a[1]=u,a[2]=1;break;case 4:a[0]=l,a[1]=0,a[2]=1;break;default:a[0]=1,a[1]=0,a[2]=u}return i=(1-n)*r,[255*(n*a[0]+i),255*(n*a[1]+i),255*(n*a[2]+i)]},i.hcg.hsv=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t),r=0;return n>0&&(r=t/n),[e[0],100*r,100*n]},i.hcg.hsl=function(e){var t=e[1]/100,n=e[2]/100*(1-t)+.5*t,r=0;return n>0&&n<.5?r=t/(2*n):n>=.5&&n<1&&(r=t/(2*(1-n))),[e[0],100*r,100*n]},i.hcg.hwb=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},i.hwb.hcg=function(e){var t=e[1]/100,n=1-e[2]/100,r=n-t,i=0;return r<1&&(i=(n-r)/(1-r)),[e[0],100*r,100*i]},i.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},i.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},i.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},i.gray.hsl=i.gray.hsv=function(e){return[0,0,e[0]]},i.gray.hwb=function(e){return[0,100,e[0]]},i.gray.cmyk=function(e){return[0,0,0,e[0]]},i.gray.lab=function(e){return[e[0],0,0]},i.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},i.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}}));function r(e){var t=function(){for(var e={},t=Object.keys(n),r=t.length,i=0;i<r;i++)e[t[i]]={distance:-1,parent:null};return e}(),r=[e];for(t[e].distance=0;r.length;)for(var i=r.pop(),a=Object.keys(n[i]),o=a.length,l=0;l<o;l++){var u=a[l],s=t[u];-1===s.distance&&(s.distance=t[i].distance+1,s.parent=i,r.unshift(u))}return t}function i(e,t){return function(n){return t(e(n))}}function a(e,t){for(var r=[t[e].parent,e],a=n[t[e].parent][e],o=t[e].parent;t[o].parent;)r.unshift(t[o].parent),a=i(n[t[o].parent][o],a),o=t[o].parent;return a.conversion=r,a}n.rgb,n.hsl,n.hsv,n.hwb,n.cmyk,n.xyz,n.lab,n.lch,n.hex,n.keyword,n.ansi16,n.ansi256,n.hcg,n.apple,n.gray;var o={};Object.keys(n).forEach((function(e){o[e]={},Object.defineProperty(o[e],"channels",{value:n[e].channels}),Object.defineProperty(o[e],"labels",{value:n[e].labels});var t=function(e){for(var t=r(e),n={},i=Object.keys(t),o=i.length,l=0;l<o;l++){var u=i[l];null!==t[u].parent&&(n[u]=a(u,t))}return n}(e);Object.keys(t).forEach((function(n){var r=t[n];o[e][n]=function(e){var t=function(t){if(null==t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if("object"==typeof n)for(var r=n.length,i=0;i<r;i++)n[i]=Math.round(n[i]);return n};return"conversion"in e&&(t.conversion=e.conversion),t}(r),o[e][n].raw=function(e){var t=function(t){return null==t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)}))}));var l=o,u={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},s={getRgba:c,getHsla:f,getRgb:function(e){var t=c(e);return t&&t.slice(0,3)},getHsl:function(e){var t=f(e);return t&&t.slice(0,3)},getHwb:d,getAlpha:function(e){var t=c(e);return t||(t=f(e))||(t=d(e))?t[3]:void 0},hexString:function(e,t){return t=void 0!==t&&3===e.length?t:e[3],"#"+m(e[0])+m(e[1])+m(e[2])+(t>=0&&t<1?m(Math.round(255*t)):"")},rgbString:function(e,t){return t<1||e[3]&&e[3]<1?h(e,t):"rgb("+e[0]+", "+e[1]+", "+e[2]+")"},rgbaString:h,percentString:function(e,t){if(t<1||e[3]&&e[3]<1)return p(e,t);var n=Math.round(e[0]/255*100),r=Math.round(e[1]/255*100),i=Math.round(e[2]/255*100);return"rgb("+n+"%, "+r+"%, "+i+"%)"},percentaString:p,hslString:function(e,t){return t<1||e[3]&&e[3]<1?g(e,t):"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)"},hslaString:g,hwbString:function(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+(void 0!==t&&1!==t?", "+t:"")+")"},keyword:function(e){return b[e.slice(0,3)]}};function c(e){if(e){var t=[0,0,0],n=1,r=e.match(/^#([a-fA-F0-9]{3,4})$/i),i="";if(r){i=(r=r[1])[3];for(var a=0;a<t.length;a++)t[a]=parseInt(r[a]+r[a],16);i&&(n=Math.round(parseInt(i+i,16)/255*100)/100)}else if(r=e.match(/^#([a-fA-F0-9]{6}([a-fA-F0-9]{2})?)$/i)){for(i=r[2],r=r[1],a=0;a<t.length;a++)t[a]=parseInt(r.slice(2*a,2*a+2),16);i&&(n=Math.round(parseInt(i,16)/255*100)/100)}else if(r=e.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/i)){for(a=0;a<t.length;a++)t[a]=parseInt(r[a+1]);n=parseFloat(r[4])}else if(r=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/i)){for(a=0;a<t.length;a++)t[a]=Math.round(2.55*parseFloat(r[a+1]));n=parseFloat(r[4])}else if(r=e.match(/(\w+)/)){if("transparent"==r[1])return[0,0,0,0];if(!(t=u[r[1]]))return}for(a=0;a<t.length;a++)t[a]=v(t[a],0,255);return n=n||0==n?v(n,0,1):1,t[3]=n,t}}function f(e){if(e){var t=e.match(/^hsla?\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[v(parseInt(t[1]),0,360),v(parseFloat(t[2]),0,100),v(parseFloat(t[3]),0,100),v(isNaN(n)?1:n,0,1)]}}}function d(e){if(e){var t=e.match(/^hwb\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[v(parseInt(t[1]),0,360),v(parseFloat(t[2]),0,100),v(parseFloat(t[3]),0,100),v(isNaN(n)?1:n,0,1)]}}}function h(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"rgba("+e[0]+", "+e[1]+", "+e[2]+", "+t+")"}function p(e,t){return"rgba("+Math.round(e[0]/255*100)+"%, "+Math.round(e[1]/255*100)+"%, "+Math.round(e[2]/255*100)+"%, "+(t||e[3]||1)+")"}function g(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+t+")"}function v(e,t,n){return Math.min(Math.max(t,e),n)}function m(e){var t=e.toString(16).toUpperCase();return t.length<2?"0"+t:t}var b={};for(var y in u)b[u[y]]=y;var x=function(e){return e instanceof x?e:this instanceof x?(this.valid=!1,this.values={rgb:[0,0,0],hsl:[0,0,0],hsv:[0,0,0],hwb:[0,0,0],cmyk:[0,0,0,0],alpha:1},void("string"==typeof e?(t=s.getRgba(e))?this.setValues("rgb",t):(t=s.getHsla(e))?this.setValues("hsl",t):(t=s.getHwb(e))&&this.setValues("hwb",t):"object"==typeof e&&(void 0!==(t=e).r||void 0!==t.red?this.setValues("rgb",t):void 0!==t.l||void 0!==t.lightness?this.setValues("hsl",t):void 0!==t.v||void 0!==t.value?this.setValues("hsv",t):void 0!==t.w||void 0!==t.whiteness?this.setValues("hwb",t):void 0===t.c&&void 0===t.cyan||this.setValues("cmyk",t)))):new x(e);var t};x.prototype={isValid:function(){return this.valid},rgb:function(){return this.setSpace("rgb",arguments)},hsl:function(){return this.setSpace("hsl",arguments)},hsv:function(){return this.setSpace("hsv",arguments)},hwb:function(){return this.setSpace("hwb",arguments)},cmyk:function(){return this.setSpace("cmyk",arguments)},rgbArray:function(){return this.values.rgb},hslArray:function(){return this.values.hsl},hsvArray:function(){return this.values.hsv},hwbArray:function(){var e=this.values;return 1!==e.alpha?e.hwb.concat([e.alpha]):e.hwb},cmykArray:function(){return this.values.cmyk},rgbaArray:function(){var e=this.values;return e.rgb.concat([e.alpha])},hslaArray:function(){var e=this.values;return e.hsl.concat([e.alpha])},alpha:function(e){return void 0===e?this.values.alpha:(this.setValues("alpha",e),this)},red:function(e){return this.setChannel("rgb",0,e)},green:function(e){return this.setChannel("rgb",1,e)},blue:function(e){return this.setChannel("rgb",2,e)},hue:function(e){return e&&(e=(e%=360)<0?360+e:e),this.setChannel("hsl",0,e)},saturation:function(e){return this.setChannel("hsl",1,e)},lightness:function(e){return this.setChannel("hsl",2,e)},saturationv:function(e){return this.setChannel("hsv",1,e)},whiteness:function(e){return this.setChannel("hwb",1,e)},blackness:function(e){return this.setChannel("hwb",2,e)},value:function(e){return this.setChannel("hsv",2,e)},cyan:function(e){return this.setChannel("cmyk",0,e)},magenta:function(e){return this.setChannel("cmyk",1,e)},yellow:function(e){return this.setChannel("cmyk",2,e)},black:function(e){return this.setChannel("cmyk",3,e)},hexString:function(){return s.hexString(this.values.rgb)},rgbString:function(){return s.rgbString(this.values.rgb,this.values.alpha)},rgbaString:function(){return s.rgbaString(this.values.rgb,this.values.alpha)},percentString:function(){return s.percentString(this.values.rgb,this.values.alpha)},hslString:function(){return s.hslString(this.values.hsl,this.values.alpha)},hslaString:function(){return s.hslaString(this.values.hsl,this.values.alpha)},hwbString:function(){return s.hwbString(this.values.hwb,this.values.alpha)},keyword:function(){return s.keyword(this.values.rgb,this.values.alpha)},rgbNumber:function(){var e=this.values.rgb;return e[0]<<16|e[1]<<8|e[2]},luminosity:function(){for(var e=this.values.rgb,t=[],n=0;n<e.length;n++){var r=e[n]/255;t[n]=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4)}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast:function(e){var t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level:function(e){var t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},dark:function(){var e=this.values.rgb;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},light:function(){return!this.dark()},negate:function(){for(var e=[],t=0;t<3;t++)e[t]=255-this.values.rgb[t];return this.setValues("rgb",e),this},lighten:function(e){var t=this.values.hsl;return t[2]+=t[2]*e,this.setValues("hsl",t),this},darken:function(e){var t=this.values.hsl;return t[2]-=t[2]*e,this.setValues("hsl",t),this},saturate:function(e){var t=this.values.hsl;return t[1]+=t[1]*e,this.setValues("hsl",t),this},desaturate:function(e){var t=this.values.hsl;return t[1]-=t[1]*e,this.setValues("hsl",t),this},whiten:function(e){var t=this.values.hwb;return t[1]+=t[1]*e,this.setValues("hwb",t),this},blacken:function(e){var t=this.values.hwb;return t[2]+=t[2]*e,this.setValues("hwb",t),this},greyscale:function(){var e=this.values.rgb,t=.3*e[0]+.59*e[1]+.11*e[2];return this.setValues("rgb",[t,t,t]),this},clearer:function(e){var t=this.values.alpha;return this.setValues("alpha",t-t*e),this},opaquer:function(e){var t=this.values.alpha;return this.setValues("alpha",t+t*e),this},rotate:function(e){var t=this.values.hsl,n=(t[0]+e)%360;return t[0]=n<0?360+n:n,this.setValues("hsl",t),this},mix:function(e,t){var n=e,r=void 0===t?.5:t,i=2*r-1,a=this.alpha()-n.alpha(),o=((i*a==-1?i:(i+a)/(1+i*a))+1)/2,l=1-o;return this.rgb(o*this.red()+l*n.red(),o*this.green()+l*n.green(),o*this.blue()+l*n.blue()).alpha(this.alpha()*r+n.alpha()*(1-r))},toJSON:function(){return this.rgb()},clone:function(){var e,t,n=new x,r=this.values,i=n.values;for(var a in r)r.hasOwnProperty(a)&&(e=r[a],"[object Array]"===(t={}.toString.call(e))?i[a]=e.slice(0):"[object Number]"===t?i[a]=e:console.error("unexpected color value:",e));return n}},x.prototype.spaces={rgb:["red","green","blue"],hsl:["hue","saturation","lightness"],hsv:["hue","saturation","value"],hwb:["hue","whiteness","blackness"],cmyk:["cyan","magenta","yellow","black"]},x.prototype.maxes={rgb:[255,255,255],hsl:[360,100,100],hsv:[360,100,100],hwb:[360,100,100],cmyk:[100,100,100,100]},x.prototype.getValues=function(e){for(var t=this.values,n={},r=0;r<e.length;r++)n[e.charAt(r)]=t[e][r];return 1!==t.alpha&&(n.a=t.alpha),n},x.prototype.setValues=function(e,t){var n,r,i=this.values,a=this.spaces,o=this.maxes,u=1;if(this.valid=!0,"alpha"===e)u=t;else if(t.length)i[e]=t.slice(0,e.length),u=t[e.length];else if(void 0!==t[e.charAt(0)]){for(n=0;n<e.length;n++)i[e][n]=t[e.charAt(n)];u=t.a}else if(void 0!==t[a[e][0]]){var s=a[e];for(n=0;n<e.length;n++)i[e][n]=t[s[n]];u=t.alpha}if(i.alpha=Math.max(0,Math.min(1,void 0===u?i.alpha:u)),"alpha"===e)return!1;for(n=0;n<e.length;n++)r=Math.max(0,Math.min(o[e][n],i[e][n])),i[e][n]=Math.round(r);for(var c in a)c!==e&&(i[c]=l[e][c](i[e]));return!0},x.prototype.setSpace=function(e,t){var n=t[0];return void 0===n?this.getValues(e):("number"==typeof n&&(n=Array.prototype.slice.call(t)),this.setValues(e,n),this)},x.prototype.setChannel=function(e,t,n){var r=this.values[e];return void 0===n?r[t]:(n===r[t]||(r[t]=n,this.setValues(e,r)),this)},"undefined"!=typeof window&&(window.Color=x);var _=x;function w(e){return-1===["__proto__","prototype","constructor"].indexOf(e)}var k,S={noop:function(){},uid:(k=0,function(){return k++}),isNullOrUndef:function(e){return null==e},isArray:function(e){if(Array.isArray&&Array.isArray(e))return!0;var t=Object.prototype.toString.call(e);return"[object"===t.substr(0,7)&&"Array]"===t.substr(-6)},isObject:function(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)},isFinite:function(e){return("number"==typeof e||e instanceof Number)&&isFinite(e)},valueOrDefault:function(e,t){return void 0===e?t:e},valueAtIndexOrDefault:function(e,t,n){return S.valueOrDefault(S.isArray(e)?e[t]:e,n)},callback:function(e,t,n){if(e&&"function"==typeof e.call)return e.apply(n,t)},each:function(e,t,n,r){var i,a,o;if(S.isArray(e))if(a=e.length,r)for(i=a-1;i>=0;i--)t.call(n,e[i],i);else for(i=0;i<a;i++)t.call(n,e[i],i);else if(S.isObject(e))for(a=(o=Object.keys(e)).length,i=0;i<a;i++)t.call(n,e[o[i]],o[i])},arrayEquals:function(e,t){var n,r,i,a;if(!e||!t||e.length!==t.length)return!1;for(n=0,r=e.length;n<r;++n)if(i=e[n],a=t[n],i instanceof Array&&a instanceof Array){if(!S.arrayEquals(i,a))return!1}else if(i!==a)return!1;return!0},clone:function(e){if(S.isArray(e))return e.map(S.clone);if(S.isObject(e)){for(var t=Object.create(e),n=Object.keys(e),r=n.length,i=0;i<r;++i)t[n[i]]=S.clone(e[n[i]]);return t}return e},_merger:function(e,t,n,r){if(w(e)){var i=t[e],a=n[e];S.isObject(i)&&S.isObject(a)?S.merge(i,a,r):t[e]=S.clone(a)}},_mergerIf:function(e,t,n){if(w(e)){var r=t[e],i=n[e];S.isObject(r)&&S.isObject(i)?S.mergeIf(r,i):t.hasOwnProperty(e)||(t[e]=S.clone(i))}},merge:function(e,t,n){var r,i,a,o,l,u=S.isArray(t)?t:[t],s=u.length;if(!S.isObject(e))return e;for(r=(n=n||{}).merger||S._merger,i=0;i<s;++i)if(t=u[i],S.isObject(t))for(l=0,o=(a=Object.keys(t)).length;l<o;++l)r(a[l],e,t,n);return e},mergeIf:function(e,t){return S.merge(e,t,{merger:S._mergerIf})},extend:Object.assign||function(e){return S.merge(e,[].slice.call(arguments,1),{merger:function(e,t,n){t[e]=n[e]}})},inherits:function(e){var t=this,n=e&&e.hasOwnProperty("constructor")?e.constructor:function(){return t.apply(this,arguments)},r=function(){this.constructor=n};return r.prototype=t.prototype,n.prototype=new r,n.extend=S.inherits,e&&S.extend(n.prototype,e),n.__super__=t.prototype,n},_deprecated:function(e,t,n,r){void 0!==t&&console.warn(e+': "'+n+'" is deprecated. Please use "'+r+'" instead')}},T=S;S.callCallback=S.callback,S.indexOf=function(e,t,n){return Array.prototype.indexOf.call(e,t,n)},S.getValueOrDefault=S.valueOrDefault,S.getValueAtIndexOrDefault=S.valueAtIndexOrDefault;var E={linear:function(e){return e},easeInQuad:function(e){return e*e},easeOutQuad:function(e){return-e*(e-2)},easeInOutQuad:function(e){return(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1)},easeInCubic:function(e){return e*e*e},easeOutCubic:function(e){return(e-=1)*e*e+1},easeInOutCubic:function(e){return(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2)},easeInQuart:function(e){return e*e*e*e},easeOutQuart:function(e){return-((e-=1)*e*e*e-1)},easeInOutQuart:function(e){return(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)},easeInQuint:function(e){return e*e*e*e*e},easeOutQuint:function(e){return(e-=1)*e*e*e*e+1},easeInOutQuint:function(e){return(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2)},easeInSine:function(e){return 1-Math.cos(e*(Math.PI/2))},easeOutSine:function(e){return Math.sin(e*(Math.PI/2))},easeInOutSine:function(e){return-.5*(Math.cos(Math.PI*e)-1)},easeInExpo:function(e){return 0===e?0:Math.pow(2,10*(e-1))},easeOutExpo:function(e){return 1===e?1:1-Math.pow(2,-10*e)},easeInOutExpo:function(e){return 0===e?0:1===e?1:(e/=.5)<1?.5*Math.pow(2,10*(e-1)):.5*(2-Math.pow(2,-10*--e))},easeInCirc:function(e){return e>=1?e:-(Math.sqrt(1-e*e)-1)},easeOutCirc:function(e){return Math.sqrt(1-(e-=1)*e)},easeInOutCirc:function(e){return(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)},easeInElastic:function(e){var t=1.70158,n=0,r=1;return 0===e?0:1===e?1:(n||(n=.3),r<1?(r=1,t=n/4):t=n/(2*Math.PI)*Math.asin(1/r),-r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n))},easeOutElastic:function(e){var t=1.70158,n=0,r=1;return 0===e?0:1===e?1:(n||(n=.3),r<1?(r=1,t=n/4):t=n/(2*Math.PI)*Math.asin(1/r),r*Math.pow(2,-10*e)*Math.sin((e-t)*(2*Math.PI)/n)+1)},easeInOutElastic:function(e){var t=1.70158,n=0,r=1;return 0===e?0:2==(e/=.5)?1:(n||(n=.45),r<1?(r=1,t=n/4):t=n/(2*Math.PI)*Math.asin(1/r),e<1?r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)*-.5:r*Math.pow(2,-10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)*.5+1)},easeInBack:function(e){var t=1.70158;return e*e*((t+1)*e-t)},easeOutBack:function(e){var t=1.70158;return(e-=1)*e*((t+1)*e+t)+1},easeInOutBack:function(e){var t=1.70158;return(e/=.5)<1?e*e*((1+(t*=1.525))*e-t)*.5:.5*((e-=2)*e*((1+(t*=1.525))*e+t)+2)},easeInBounce:function(e){return 1-E.easeOutBounce(1-e)},easeOutBounce:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:function(e){return e<.5?.5*E.easeInBounce(2*e):.5*E.easeOutBounce(2*e-1)+.5}},C={effects:E};T.easingEffects=E;var P=Math.PI,M=P/180,O=2*P,I=P/2,A=P/4,D=2*P/3,N={clear:function(e){e.ctx.clearRect(0,0,e.width,e.height)},roundedRect:function(e,t,n,r,i,a){if(a){var o=Math.min(a,i/2,r/2),l=t+o,u=n+o,s=t+r-o,c=n+i-o;e.moveTo(t,u),l<s&&u<c?(e.arc(l,u,o,-P,-I),e.arc(s,u,o,-I,0),e.arc(s,c,o,0,I),e.arc(l,c,o,I,P)):l<s?(e.moveTo(l,n),e.arc(s,u,o,-I,I),e.arc(l,u,o,I,P+I)):u<c?(e.arc(l,u,o,-P,0),e.arc(l,c,o,0,P)):e.arc(l,u,o,-P,P),e.closePath(),e.moveTo(t,n)}else e.rect(t,n,r,i)},drawPoint:function(e,t,n,r,i,a){var o,l,u,s,c,f=(a||0)*M;if(t&&"object"==typeof t&&("[object HTMLImageElement]"===(o=t.toString())||"[object HTMLCanvasElement]"===o))return e.save(),e.translate(r,i),e.rotate(f),e.drawImage(t,-t.width/2,-t.height/2,t.width,t.height),void e.restore();if(!(isNaN(n)||n<=0)){switch(e.beginPath(),t){default:e.arc(r,i,n,0,O),e.closePath();break;case"triangle":e.moveTo(r+Math.sin(f)*n,i-Math.cos(f)*n),f+=D,e.lineTo(r+Math.sin(f)*n,i-Math.cos(f)*n),f+=D,e.lineTo(r+Math.sin(f)*n,i-Math.cos(f)*n),e.closePath();break;case"rectRounded":s=n-(c=.516*n),l=Math.cos(f+A)*s,u=Math.sin(f+A)*s,e.arc(r-l,i-u,c,f-P,f-I),e.arc(r+u,i-l,c,f-I,f),e.arc(r+l,i+u,c,f,f+I),e.arc(r-u,i+l,c,f+I,f+P),e.closePath();break;case"rect":if(!a){s=Math.SQRT1_2*n,e.rect(r-s,i-s,2*s,2*s);break}f+=A;case"rectRot":l=Math.cos(f)*n,u=Math.sin(f)*n,e.moveTo(r-l,i-u),e.lineTo(r+u,i-l),e.lineTo(r+l,i+u),e.lineTo(r-u,i+l),e.closePath();break;case"crossRot":f+=A;case"cross":l=Math.cos(f)*n,u=Math.sin(f)*n,e.moveTo(r-l,i-u),e.lineTo(r+l,i+u),e.moveTo(r+u,i-l),e.lineTo(r-u,i+l);break;case"star":l=Math.cos(f)*n,u=Math.sin(f)*n,e.moveTo(r-l,i-u),e.lineTo(r+l,i+u),e.moveTo(r+u,i-l),e.lineTo(r-u,i+l),f+=A,l=Math.cos(f)*n,u=Math.sin(f)*n,e.moveTo(r-l,i-u),e.lineTo(r+l,i+u),e.moveTo(r+u,i-l),e.lineTo(r-u,i+l);break;case"line":l=Math.cos(f)*n,u=Math.sin(f)*n,e.moveTo(r-l,i-u),e.lineTo(r+l,i+u);break;case"dash":e.moveTo(r,i),e.lineTo(r+Math.cos(f)*n,i+Math.sin(f)*n)}e.fill(),e.stroke()}},_isPointInArea:function(e,t){return e.x>t.left-1e-6&&e.x<t.right+1e-6&&e.y>t.top-1e-6&&e.y<t.bottom+1e-6},clipArea:function(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()},unclipArea:function(e){e.restore()},lineTo:function(e,t,n,r){var i=n.steppedLine;if(i){if("middle"===i){var a=(t.x+n.x)/2;e.lineTo(a,r?n.y:t.y),e.lineTo(a,r?t.y:n.y)}else"after"===i&&!r||"after"!==i&&r?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}else n.tension?e.bezierCurveTo(r?t.controlPointPreviousX:t.controlPointNextX,r?t.controlPointPreviousY:t.controlPointNextY,r?n.controlPointNextX:n.controlPointPreviousX,r?n.controlPointNextY:n.controlPointPreviousY,n.x,n.y):e.lineTo(n.x,n.y)}},z=N;T.clear=N.clear,T.drawRoundedRectangle=function(e){e.beginPath(),N.roundedRect.apply(N,arguments)};var j={_set:function(e,t){return T.merge(this[e]||(this[e]={}),t)}};j._set("global",{defaultColor:"rgba(0,0,0,0.1)",defaultFontColor:"#666",defaultFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",defaultFontSize:12,defaultFontStyle:"normal",defaultLineHeight:1.2,showLines:!0});var R=j,F=T.valueOrDefault,L={toLineHeight:function(e,t){var n=(""+e).match(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/);if(!n||"normal"===n[1])return 1.2*t;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100}return t*e},toPadding:function(e){var t,n,r,i;return T.isObject(e)?(t=+e.top||0,n=+e.right||0,r=+e.bottom||0,i=+e.left||0):t=n=r=i=+e||0,{top:t,right:n,bottom:r,left:i,height:t+r,width:i+n}},_parseFont:function(e){var t=R.global,n=F(e.fontSize,t.defaultFontSize),r={family:F(e.fontFamily,t.defaultFontFamily),lineHeight:T.options.toLineHeight(F(e.lineHeight,t.defaultLineHeight),n),size:n,style:F(e.fontStyle,t.defaultFontStyle),weight:null,string:""};return r.string=function(e){return!e||T.isNullOrUndef(e.size)||T.isNullOrUndef(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}(r),r},resolve:function(e,t,n,r){var i,a,o,l=!0;for(i=0,a=e.length;i<a;++i)if(void 0!==(o=e[i])&&(void 0!==t&&"function"==typeof o&&(o=o(t),l=!1),void 0!==n&&T.isArray(o)&&(o=o[n],l=!1),void 0!==o))return r&&!l&&(r.cacheable=!1),o}},B={_factorize:function(e){var t,n=[],r=Math.sqrt(e);for(t=1;t<r;t++)e%t==0&&(n.push(t),n.push(e/t));return r===(0|r)&&n.push(r),n.sort((function(e,t){return e-t})).pop(),n},log10:Math.log10||function(e){var t=Math.log(e)*Math.LOG10E,n=Math.round(t);return e===Math.pow(10,n)?n:t}},W=B;T.log10=B.log10;var V=T,U=C,H=z,q=L,$=W,Q={getRtlAdapter:function(e,t,n){return e?function(e,t){return{x:function(n){return e+e+t-n},setWidth:function(e){t=e},textAlign:function(e){return"center"===e?e:"right"===e?"left":"right"},xPlus:function(e,t){return e-t},leftForLtr:function(e,t){return e-t}}}(t,n):{x:function(e){return e},setWidth:function(e){},textAlign:function(e){return e},xPlus:function(e,t){return e+t},leftForLtr:function(e,t){return e}}},overrideTextDirection:function(e,t){var n,r;"ltr"!==t&&"rtl"!==t||(r=[(n=e.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=r)},restoreTextDirection:function(e){var t=e.prevTextDirection;void 0!==t&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}};V.easing=U,V.canvas=H,V.options=q,V.math=$,V.rtl=Q;var Y=function(e){V.extend(this,e),this.initialize.apply(this,arguments)};V.extend(Y.prototype,{_type:void 0,initialize:function(){this.hidden=!1},pivot:function(){var e=this;return e._view||(e._view=V.extend({},e._model)),e._start={},e},transition:function(e){var t=this,n=t._model,r=t._start,i=t._view;return n&&1!==e?(i||(i=t._view={}),r||(r=t._start={}),function(e,t,n,r){var i,a,o,l,u,s,c,f,d,h=Object.keys(n);for(i=0,a=h.length;i<a;++i)if(s=n[o=h[i]],t.hasOwnProperty(o)||(t[o]=s),(l=t[o])!==s&&"_"!==o[0]){if(e.hasOwnProperty(o)||(e[o]=l),(c=typeof s)==typeof(u=e[o]))if("string"===c){if((f=_(u)).valid&&(d=_(s)).valid){t[o]=d.mix(f,r).rgbString();continue}}else if(V.isFinite(u)&&V.isFinite(s)){t[o]=u+(s-u)*r;continue}t[o]=s}}(r,i,n,e),t):(t._view=V.extend({},n),t._start=null,t)},tooltipPosition:function(){return{x:this._model.x,y:this._model.y}},hasValue:function(){return V.isNumber(this._model.x)&&V.isNumber(this._model.y)}}),Y.extend=V.inherits;var K=Y,G=K.extend({chart:null,currentStep:0,numSteps:60,easing:"",render:null,onAnimationProgress:null,onAnimationComplete:null}),Z=G;Object.defineProperty(G.prototype,"animationObject",{get:function(){return this}}),Object.defineProperty(G.prototype,"chartInstance",{get:function(){return this.chart},set:function(e){this.chart=e}}),R._set("global",{animation:{duration:1e3,easing:"easeOutQuart",onProgress:V.noop,onComplete:V.noop}});var X={animations:[],request:null,addAnimation:function(e,t,n,r){var i,a,o=this.animations;for(t.chart=e,t.startTime=Date.now(),t.duration=n,r||(e.animating=!0),i=0,a=o.length;i<a;++i)if(o[i].chart===e)return void(o[i]=t);o.push(t),1===o.length&&this.requestAnimationFrame()},cancelAnimation:function(e){var t=V.findIndex(this.animations,(function(t){return t.chart===e}));-1!==t&&(this.animations.splice(t,1),e.animating=!1)},requestAnimationFrame:function(){var e=this;null===e.request&&(e.request=V.requestAnimFrame.call(window,(function(){e.request=null,e.startDigest()})))},startDigest:function(){this.advance(),this.animations.length>0&&this.requestAnimationFrame()},advance:function(){for(var e,t,n,r,i=this.animations,a=0;a<i.length;)t=(e=i[a]).chart,n=e.numSteps,r=Math.floor((Date.now()-e.startTime)/e.duration*n)+1,e.currentStep=Math.min(r,n),V.callback(e.render,[t,e],t),V.callback(e.onAnimationProgress,[e],t),e.currentStep>=n?(V.callback(e.onAnimationComplete,[e],t),t.animating=!1,i.splice(a,1)):++a}},J=V.options.resolve,ee=["push","pop","shift","splice","unshift"];function te(e,t){var n=e._chartjs;if(n){var r=n.listeners,i=r.indexOf(t);-1!==i&&r.splice(i,1),r.length>0||(ee.forEach((function(t){delete e[t]})),delete e._chartjs)}}var ne=function(e,t){this.initialize(e,t)};V.extend(ne.prototype,{datasetElementType:null,dataElementType:null,_datasetElementOptions:["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth"],_dataElementOptions:["backgroundColor","borderColor","borderWidth","pointStyle"],initialize:function(e,t){var n=this;n.chart=e,n.index=t,n.linkScales(),n.addElements(),n._type=n.getMeta().type},updateIndex:function(e){this.index=e},linkScales:function(){var e=this.getMeta(),t=this.chart,n=t.scales,r=this.getDataset(),i=t.options.scales;null!==e.xAxisID&&e.xAxisID in n&&!r.xAxisID||(e.xAxisID=r.xAxisID||i.xAxes[0].id),null!==e.yAxisID&&e.yAxisID in n&&!r.yAxisID||(e.yAxisID=r.yAxisID||i.yAxes[0].id)},getDataset:function(){return this.chart.data.datasets[this.index]},getMeta:function(){return this.chart.getDatasetMeta(this.index)},getScaleForId:function(e){return this.chart.scales[e]},_getValueScaleId:function(){return this.getMeta().yAxisID},_getIndexScaleId:function(){return this.getMeta().xAxisID},_getValueScale:function(){return this.getScaleForId(this._getValueScaleId())},_getIndexScale:function(){return this.getScaleForId(this._getIndexScaleId())},reset:function(){this._update(!0)},destroy:function(){this._data&&te(this._data,this)},createMetaDataset:function(){var e=this.datasetElementType;return e&&new e({_chart:this.chart,_datasetIndex:this.index})},createMetaData:function(e){var t=this.dataElementType;return t&&new t({_chart:this.chart,_datasetIndex:this.index,_index:e})},addElements:function(){var e,t,n=this.getMeta(),r=this.getDataset().data||[],i=n.data;for(e=0,t=r.length;e<t;++e)i[e]=i[e]||this.createMetaData(e);n.dataset=n.dataset||this.createMetaDataset()},addElementAndReset:function(e){var t=this.createMetaData(e);this.getMeta().data.splice(e,0,t),this.updateElement(t,e,!0)},buildOrUpdateElements:function(){var e,t,n=this,r=n.getDataset(),i=r.data||(r.data=[]);n._data!==i&&(n._data&&te(n._data,n),i&&Object.isExtensible(i)&&(t=n,(e=i)._chartjs?e._chartjs.listeners.push(t):(Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),ee.forEach((function(t){var n="onData"+t.charAt(0).toUpperCase()+t.slice(1),r=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:function(){var t=Array.prototype.slice.call(arguments),i=r.apply(this,t);return V.each(e._chartjs.listeners,(function(e){"function"==typeof e[n]&&e[n].apply(e,t)})),i}})})))),n._data=i),n.resyncElements()},_configure:function(){this._config=V.merge(Object.create(null),[this.chart.options.datasets[this._type],this.getDataset()],{merger:function(e,t,n){"_meta"!==e&&"data"!==e&&V._merger(e,t,n)}})},_update:function(e){this._configure(),this._cachedDataOpts=null,this.update(e)},update:V.noop,transition:function(e){for(var t=this.getMeta(),n=t.data||[],r=n.length,i=0;i<r;++i)n[i].transition(e);t.dataset&&t.dataset.transition(e)},draw:function(){var e=this.getMeta(),t=e.data||[],n=t.length,r=0;for(e.dataset&&e.dataset.draw();r<n;++r)t[r].draw()},getStyle:function(e){var t,n=this.getMeta(),r=n.dataset;return this._configure(),r&&void 0===e?t=this._resolveDatasetElementOptions(r||{}):(e=e||0,t=this._resolveDataElementOptions(n.data[e]||{},e)),!1!==t.fill&&null!==t.fill||(t.backgroundColor=t.borderColor),t},_resolveDatasetElementOptions:function(e,t){var n,r,i,a,o=this,l=o.chart,u=o._config,s=e.custom||{},c=l.options.elements[o.datasetElementType.prototype._type]||{},f=o._datasetElementOptions,d={},h={chart:l,dataset:o.getDataset(),datasetIndex:o.index,hover:t};for(n=0,r=f.length;n<r;++n)i=f[n],a=t?"hover"+i.charAt(0).toUpperCase()+i.slice(1):i,d[i]=J([s[a],u[a],c[a]],h);return d},_resolveDataElementOptions:function(e,t){var n=this,r=e&&e.custom,i=n._cachedDataOpts;if(i&&!r)return i;var a,o,l,u,s=n.chart,c=n._config,f=s.options.elements[n.dataElementType.prototype._type]||{},d=n._dataElementOptions,h={},p={chart:s,dataIndex:t,dataset:n.getDataset(),datasetIndex:n.index},g={cacheable:!r};if(r=r||{},V.isArray(d))for(o=0,l=d.length;o<l;++o)h[u=d[o]]=J([r[u],c[u],f[u]],p,t,g);else for(o=0,l=(a=Object.keys(d)).length;o<l;++o)h[u=a[o]]=J([r[u],c[d[u]],c[u],f[u]],p,t,g);return g.cacheable&&(n._cachedDataOpts=Object.freeze(h)),h},removeHoverStyle:function(e){V.merge(e._model,e.$previousStyle||{}),delete e.$previousStyle},setHoverStyle:function(e){var t=this.chart.data.datasets[e._datasetIndex],n=e._index,r=e.custom||{},i=e._model,a=V.getHoverColor;e.$previousStyle={backgroundColor:i.backgroundColor,borderColor:i.borderColor,borderWidth:i.borderWidth},i.backgroundColor=J([r.hoverBackgroundColor,t.hoverBackgroundColor,a(i.backgroundColor)],void 0,n),i.borderColor=J([r.hoverBorderColor,t.hoverBorderColor,a(i.borderColor)],void 0,n),i.borderWidth=J([r.hoverBorderWidth,t.hoverBorderWidth,i.borderWidth],void 0,n)},_removeDatasetHoverStyle:function(){var e=this.getMeta().dataset;e&&this.removeHoverStyle(e)},_setDatasetHoverStyle:function(){var e,t,n,r,i,a,o=this.getMeta().dataset,l={};if(o){for(a=o._model,i=this._resolveDatasetElementOptions(o,!0),e=0,t=(r=Object.keys(i)).length;e<t;++e)l[n=r[e]]=a[n],a[n]=i[n];o.$previousStyle=l}},resyncElements:function(){var e=this.getMeta(),t=this.getDataset().data,n=e.data.length,r=t.length;r<n?e.data.splice(r,n-r):r>n&&this.insertElements(n,r-n)},insertElements:function(e,t){for(var n=0;n<t;++n)this.addElementAndReset(e+n)},onDataPush:function(){var e=arguments.length;this.insertElements(this.getDataset().data.length-e,e)},onDataPop:function(){this.getMeta().data.pop()},onDataShift:function(){this.getMeta().data.shift()},onDataSplice:function(e,t){this.getMeta().data.splice(e,t),this.insertElements(e,arguments.length-2)},onDataUnshift:function(){this.insertElements(0,arguments.length)}}),ne.extend=V.inherits;var re=ne,ie=2*Math.PI;function ae(e,t){var n=t.startAngle,r=t.endAngle,i=t.pixelMargin,a=i/t.outerRadius,o=t.x,l=t.y;e.beginPath(),e.arc(o,l,t.outerRadius,n-a,r+a),t.innerRadius>i?(a=i/t.innerRadius,e.arc(o,l,t.innerRadius-i,r+a,n-a,!0)):e.arc(o,l,i,r+Math.PI/2,n-Math.PI/2),e.closePath(),e.clip()}function oe(e,t,n){var r="inner"===t.borderAlign;r?(e.lineWidth=2*t.borderWidth,e.lineJoin="round"):(e.lineWidth=t.borderWidth,e.lineJoin="bevel"),n.fullCircles&&function(e,t,n,r){var i,a=n.endAngle;for(r&&(n.endAngle=n.startAngle+ie,ae(e,n),n.endAngle=a,n.endAngle===n.startAngle&&n.fullCircles&&(n.endAngle+=ie,n.fullCircles--)),e.beginPath(),e.arc(n.x,n.y,n.innerRadius,n.startAngle+ie,n.startAngle,!0),i=0;i<n.fullCircles;++i)e.stroke();for(e.beginPath(),e.arc(n.x,n.y,t.outerRadius,n.startAngle,n.startAngle+ie),i=0;i<n.fullCircles;++i)e.stroke()}(e,t,n,r),r&&ae(e,n),e.beginPath(),e.arc(n.x,n.y,t.outerRadius,n.startAngle,n.endAngle),e.arc(n.x,n.y,n.innerRadius,n.endAngle,n.startAngle,!0),e.closePath(),e.stroke()}R._set("global",{elements:{arc:{backgroundColor:R.global.defaultColor,borderColor:"#fff",borderWidth:2,borderAlign:"center"}}});var le=K.extend({_type:"arc",inLabelRange:function(e){var t=this._view;return!!t&&Math.pow(e-t.x,2)<Math.pow(t.radius+t.hoverRadius,2)},inRange:function(e,t){var n=this._view;if(n){for(var r=V.getAngleFromPoint(n,{x:e,y:t}),i=r.angle,a=r.distance,o=n.startAngle,l=n.endAngle;l<o;)l+=ie;for(;i>l;)i-=ie;for(;i<o;)i+=ie;var u=i>=o&&i<=l,s=a>=n.innerRadius&&a<=n.outerRadius;return u&&s}return!1},getCenterPoint:function(){var e=this._view,t=(e.startAngle+e.endAngle)/2,n=(e.innerRadius+e.outerRadius)/2;return{x:e.x+Math.cos(t)*n,y:e.y+Math.sin(t)*n}},getArea:function(){var e=this._view;return Math.PI*((e.endAngle-e.startAngle)/(2*Math.PI))*(Math.pow(e.outerRadius,2)-Math.pow(e.innerRadius,2))},tooltipPosition:function(){var e=this._view,t=e.startAngle+(e.endAngle-e.startAngle)/2,n=(e.outerRadius-e.innerRadius)/2+e.innerRadius;return{x:e.x+Math.cos(t)*n,y:e.y+Math.sin(t)*n}},draw:function(){var e,t=this._chart.ctx,n=this._view,r="inner"===n.borderAlign?.33:0,i={x:n.x,y:n.y,innerRadius:n.innerRadius,outerRadius:Math.max(n.outerRadius-r,0),pixelMargin:r,startAngle:n.startAngle,endAngle:n.endAngle,fullCircles:Math.floor(n.circumference/ie)};if(t.save(),t.fillStyle=n.backgroundColor,t.strokeStyle=n.borderColor,i.fullCircles){for(i.endAngle=i.startAngle+ie,t.beginPath(),t.arc(i.x,i.y,i.outerRadius,i.startAngle,i.endAngle),t.arc(i.x,i.y,i.innerRadius,i.endAngle,i.startAngle,!0),t.closePath(),e=0;e<i.fullCircles;++e)t.fill();i.endAngle=i.startAngle+n.circumference%ie}t.beginPath(),t.arc(i.x,i.y,i.outerRadius,i.startAngle,i.endAngle),t.arc(i.x,i.y,i.innerRadius,i.endAngle,i.startAngle,!0),t.closePath(),t.fill(),n.borderWidth&&oe(t,n,i),t.restore()}}),ue=V.valueOrDefault,se=R.global.defaultColor;R._set("global",{elements:{line:{tension:.4,backgroundColor:se,borderWidth:3,borderColor:se,borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",capBezierPoints:!0,fill:!0}}});var ce=K.extend({_type:"line",draw:function(){var e,t,n,r=this,i=r._view,a=r._chart.ctx,o=i.spanGaps,l=r._children.slice(),u=R.global,s=u.elements.line,c=-1,f=r._loop;if(l.length){if(r._loop){for(e=0;e<l.length;++e)if(t=V.previousItem(l,e),!l[e]._view.skip&&t._view.skip){l=l.slice(e).concat(l.slice(0,e)),f=o;break}f&&l.push(l[0])}for(a.save(),a.lineCap=i.borderCapStyle||s.borderCapStyle,a.setLineDash&&a.setLineDash(i.borderDash||s.borderDash),a.lineDashOffset=ue(i.borderDashOffset,s.borderDashOffset),a.lineJoin=i.borderJoinStyle||s.borderJoinStyle,a.lineWidth=ue(i.borderWidth,s.borderWidth),a.strokeStyle=i.borderColor||u.defaultColor,a.beginPath(),(n=l[0]._view).skip||(a.moveTo(n.x,n.y),c=0),e=1;e<l.length;++e)n=l[e]._view,t=-1===c?V.previousItem(l,e):l[c],n.skip||(c!==e-1&&!o||-1===c?a.moveTo(n.x,n.y):V.canvas.lineTo(a,t._view,n),c=e);f&&a.closePath(),a.stroke(),a.restore()}}}),fe=V.valueOrDefault,de=R.global.defaultColor;function he(e){var t=this._view;return!!t&&Math.abs(e-t.x)<t.radius+t.hitRadius}R._set("global",{elements:{point:{radius:3,pointStyle:"circle",backgroundColor:de,borderColor:de,borderWidth:1,hitRadius:1,hoverRadius:4,hoverBorderWidth:1}}});var pe=K.extend({_type:"point",inRange:function(e,t){var n=this._view;return!!n&&Math.pow(e-n.x,2)+Math.pow(t-n.y,2)<Math.pow(n.hitRadius+n.radius,2)},inLabelRange:he,inXRange:he,inYRange:function(e){var t=this._view;return!!t&&Math.abs(e-t.y)<t.radius+t.hitRadius},getCenterPoint:function(){var e=this._view;return{x:e.x,y:e.y}},getArea:function(){return Math.PI*Math.pow(this._view.radius,2)},tooltipPosition:function(){var e=this._view;return{x:e.x,y:e.y,padding:e.radius+e.borderWidth}},draw:function(e){var t=this._view,n=this._chart.ctx,r=t.pointStyle,i=t.rotation,a=t.radius,o=t.x,l=t.y,u=R.global,s=u.defaultColor;t.skip||(void 0===e||V.canvas._isPointInArea(t,e))&&(n.strokeStyle=t.borderColor||s,n.lineWidth=fe(t.borderWidth,u.elements.point.borderWidth),n.fillStyle=t.backgroundColor||s,V.canvas.drawPoint(n,r,a,o,l,i))}}),ge=R.global.defaultColor;function ve(e){return e&&void 0!==e.width}function me(e){var t,n,r,i,a;return ve(e)?(a=e.width/2,t=e.x-a,n=e.x+a,r=Math.min(e.y,e.base),i=Math.max(e.y,e.base)):(a=e.height/2,t=Math.min(e.x,e.base),n=Math.max(e.x,e.base),r=e.y-a,i=e.y+a),{left:t,top:r,right:n,bottom:i}}function be(e,t,n){return e===t?n:e===n?t:e}function ye(e,t,n){var r,i,a,o,l=e.borderWidth,u=function(e){var t=e.borderSkipped,n={};return t?(e.horizontal?e.base>e.x&&(t=be(t,"left","right")):e.base<e.y&&(t=be(t,"bottom","top")),n[t]=!0,n):n}(e);return V.isObject(l)?(r=+l.top||0,i=+l.right||0,a=+l.bottom||0,o=+l.left||0):r=i=a=o=+l||0,{t:u.top||r<0?0:r>n?n:r,r:u.right||i<0?0:i>t?t:i,b:u.bottom||a<0?0:a>n?n:a,l:u.left||o<0?0:o>t?t:o}}function xe(e,t,n){var r=null===t,i=null===n,a=!(!e||r&&i)&&me(e);return a&&(r||t>=a.left&&t<=a.right)&&(i||n>=a.top&&n<=a.bottom)}R._set("global",{elements:{rectangle:{backgroundColor:ge,borderColor:ge,borderSkipped:"bottom",borderWidth:0}}});var _e=K.extend({_type:"rectangle",draw:function(){var e=this._chart.ctx,t=this._view,n=function(e){var t=me(e),n=t.right-t.left,r=t.bottom-t.top,i=ye(e,n/2,r/2);return{outer:{x:t.left,y:t.top,w:n,h:r},inner:{x:t.left+i.l,y:t.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b}}}(t),r=n.outer,i=n.inner;e.fillStyle=t.backgroundColor,e.fillRect(r.x,r.y,r.w,r.h),r.w===i.w&&r.h===i.h||(e.save(),e.beginPath(),e.rect(r.x,r.y,r.w,r.h),e.clip(),e.fillStyle=t.borderColor,e.rect(i.x,i.y,i.w,i.h),e.fill("evenodd"),e.restore())},height:function(){var e=this._view;return e.base-e.y},inRange:function(e,t){return xe(this._view,e,t)},inLabelRange:function(e,t){var n=this._view;return ve(n)?xe(n,e,null):xe(n,null,t)},inXRange:function(e){return xe(this._view,e,null)},inYRange:function(e){return xe(this._view,null,e)},getCenterPoint:function(){var e,t,n=this._view;return ve(n)?(e=n.x,t=(n.y+n.base)/2):(e=(n.x+n.base)/2,t=n.y),{x:e,y:t}},getArea:function(){var e=this._view;return ve(e)?e.width*Math.abs(e.y-e.base):e.height*Math.abs(e.x-e.base)},tooltipPosition:function(){var e=this._view;return{x:e.x,y:e.y}}}),we={},ke=le,Se=ce,Te=pe,Ee=_e;we.Arc=ke,we.Line=Se,we.Point=Te,we.Rectangle=Ee;var Ce=V._deprecated,Pe=V.valueOrDefault;function Me(e,t,n){var r,i,a=n.barThickness,o=t.stackCount,l=t.pixels[e],u=V.isNullOrUndef(a)?function(e,t){var n,r,i,a,o=e._length;for(i=1,a=t.length;i<a;++i)o=Math.min(o,Math.abs(t[i]-t[i-1]));for(i=0,a=e.getTicks().length;i<a;++i)r=e.getPixelForTick(i),o=i>0?Math.min(o,Math.abs(r-n)):o,n=r;return o}(t.scale,t.pixels):-1;return V.isNullOrUndef(a)?(r=u*n.categoryPercentage,i=n.barPercentage):(r=a*o,i=1),{chunk:r/o,ratio:i,start:l-r/2}}R._set("bar",{hover:{mode:"label"},scales:{xAxes:[{type:"category",offset:!0,gridLines:{offsetGridLines:!0}}],yAxes:[{type:"linear"}]}}),R._set("global",{datasets:{bar:{categoryPercentage:.8,barPercentage:.9}}});var Oe=re.extend({dataElementType:we.Rectangle,_dataElementOptions:["backgroundColor","borderColor","borderSkipped","borderWidth","barPercentage","barThickness","categoryPercentage","maxBarThickness","minBarLength"],initialize:function(){var e,t,n=this;re.prototype.initialize.apply(n,arguments),(e=n.getMeta()).stack=n.getDataset().stack,e.bar=!0,t=n._getIndexScale().options,Ce("bar chart",t.barPercentage,"scales.[x/y]Axes.barPercentage","dataset.barPercentage"),Ce("bar chart",t.barThickness,"scales.[x/y]Axes.barThickness","dataset.barThickness"),Ce("bar chart",t.categoryPercentage,"scales.[x/y]Axes.categoryPercentage","dataset.categoryPercentage"),Ce("bar chart",n._getValueScale().options.minBarLength,"scales.[x/y]Axes.minBarLength","dataset.minBarLength"),Ce("bar chart",t.maxBarThickness,"scales.[x/y]Axes.maxBarThickness","dataset.maxBarThickness")},update:function(e){var t,n,r=this.getMeta().data;for(this._ruler=this.getRuler(),t=0,n=r.length;t<n;++t)this.updateElement(r[t],t,e)},updateElement:function(e,t,n){var r=this,i=r.getMeta(),a=r.getDataset(),o=r._resolveDataElementOptions(e,t);e._xScale=r.getScaleForId(i.xAxisID),e._yScale=r.getScaleForId(i.yAxisID),e._datasetIndex=r.index,e._index=t,e._model={backgroundColor:o.backgroundColor,borderColor:o.borderColor,borderSkipped:o.borderSkipped,borderWidth:o.borderWidth,datasetLabel:a.label,label:r.chart.data.labels[t]},V.isArray(a.data[t])&&(e._model.borderSkipped=null),r._updateElementGeometry(e,t,n,o),e.pivot()},_updateElementGeometry:function(e,t,n,r){var i=this,a=e._model,o=i._getValueScale(),l=o.getBasePixel(),u=o.isHorizontal(),s=i._ruler||i.getRuler(),c=i.calculateBarValuePixels(i.index,t,r),f=i.calculateBarIndexPixels(i.index,t,s,r);a.horizontal=u,a.base=n?l:c.base,a.x=u?n?l:c.head:f.center,a.y=u?f.center:n?l:c.head,a.height=u?f.size:void 0,a.width=u?void 0:f.size},_getStacks:function(e){var t,n,r=this._getIndexScale(),i=r._getMatchingVisibleMetas(this._type),a=r.options.stacked,o=i.length,l=[];for(t=0;t<o&&(n=i[t],(!1===a||-1===l.indexOf(n.stack)||void 0===a&&void 0===n.stack)&&l.push(n.stack),n.index!==e);++t);return l},getStackCount:function(){return this._getStacks().length},getStackIndex:function(e,t){var n=this._getStacks(e),r=void 0!==t?n.indexOf(t):-1;return-1===r?n.length-1:r},getRuler:function(){var e,t,n=this._getIndexScale(),r=[];for(e=0,t=this.getMeta().data.length;e<t;++e)r.push(n.getPixelForValue(null,e,this.index));return{pixels:r,start:n._startPixel,end:n._endPixel,stackCount:this.getStackCount(),scale:n}},calculateBarValuePixels:function(e,t,n){var r,i,a,o,l,u,s,c=this.chart,f=this._getValueScale(),d=f.isHorizontal(),h=c.data.datasets,p=f._getMatchingVisibleMetas(this._type),g=f._parseValue(h[e].data[t]),v=n.minBarLength,m=f.options.stacked,b=this.getMeta().stack,y=void 0===g.start?0:g.max>=0&&g.min>=0?g.min:g.max,x=void 0===g.start?g.end:g.max>=0&&g.min>=0?g.max-g.min:g.min-g.max,_=p.length;if(m||void 0===m&&void 0!==b)for(r=0;r<_&&(i=p[r]).index!==e;++r)i.stack===b&&(a=void 0===(s=f._parseValue(h[i.index].data[t])).start?s.end:s.min>=0&&s.max>=0?s.max:s.min,(g.min<0&&a<0||g.max>=0&&a>0)&&(y+=a));return o=f.getPixelForValue(y),u=(l=f.getPixelForValue(y+x))-o,void 0!==v&&Math.abs(u)<v&&(u=v,l=x>=0&&!d||x<0&&d?o-v:o+v),{size:u,base:o,head:l,center:l+u/2}},calculateBarIndexPixels:function(e,t,n,r){var i="flex"===r.barThickness?function(e,t,n){var r,i=t.pixels,a=i[e],o=e>0?i[e-1]:null,l=e<i.length-1?i[e+1]:null,u=n.categoryPercentage;return null===o&&(o=a-(null===l?t.end-t.start:l-a)),null===l&&(l=a+a-o),r=a-(a-Math.min(o,l))/2*u,{chunk:Math.abs(l-o)/2*u/t.stackCount,ratio:n.barPercentage,start:r}}(t,n,r):Me(t,n,r),a=this.getStackIndex(e,this.getMeta().stack),o=i.start+i.chunk*a+i.chunk/2,l=Math.min(Pe(r.maxBarThickness,1/0),i.chunk*i.ratio);return{base:o-l/2,head:o+l/2,center:o,size:l}},draw:function(){var e=this.chart,t=this._getValueScale(),n=this.getMeta().data,r=this.getDataset(),i=n.length,a=0;for(V.canvas.clipArea(e.ctx,e.chartArea);a<i;++a){var o=t._parseValue(r.data[a]);isNaN(o.min)||isNaN(o.max)||n[a].draw()}V.canvas.unclipArea(e.ctx)},_resolveDataElementOptions:function(){var e=this,t=V.extend({},re.prototype._resolveDataElementOptions.apply(e,arguments)),n=e._getIndexScale().options,r=e._getValueScale().options;return t.barPercentage=Pe(n.barPercentage,t.barPercentage),t.barThickness=Pe(n.barThickness,t.barThickness),t.categoryPercentage=Pe(n.categoryPercentage,t.categoryPercentage),t.maxBarThickness=Pe(n.maxBarThickness,t.maxBarThickness),t.minBarLength=Pe(r.minBarLength,t.minBarLength),t}}),Ie=V.valueOrDefault,Ae=V.options.resolve;R._set("bubble",{hover:{mode:"single"},scales:{xAxes:[{type:"linear",position:"bottom",id:"x-axis-0"}],yAxes:[{type:"linear",position:"left",id:"y-axis-0"}]},tooltips:{callbacks:{title:function(){return""},label:function(e,t){var n=t.datasets[e.datasetIndex].label||"",r=t.datasets[e.datasetIndex].data[e.index];return n+": ("+e.xLabel+", "+e.yLabel+", "+r.r+")"}}}});var De=re.extend({dataElementType:we.Point,_dataElementOptions:["backgroundColor","borderColor","borderWidth","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth","hoverRadius","hitRadius","pointStyle","rotation"],update:function(e){var t=this,n=t.getMeta().data;V.each(n,(function(n,r){t.updateElement(n,r,e)}))},updateElement:function(e,t,n){var r=this,i=r.getMeta(),a=e.custom||{},o=r.getScaleForId(i.xAxisID),l=r.getScaleForId(i.yAxisID),u=r._resolveDataElementOptions(e,t),s=r.getDataset().data[t],c=r.index,f=n?o.getPixelForDecimal(.5):o.getPixelForValue("object"==typeof s?s:NaN,t,c),d=n?l.getBasePixel():l.getPixelForValue(s,t,c);e._xScale=o,e._yScale=l,e._options=u,e._datasetIndex=c,e._index=t,e._model={backgroundColor:u.backgroundColor,borderColor:u.borderColor,borderWidth:u.borderWidth,hitRadius:u.hitRadius,pointStyle:u.pointStyle,rotation:u.rotation,radius:n?0:u.radius,skip:a.skip||isNaN(f)||isNaN(d),x:f,y:d},e.pivot()},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth,radius:t.radius},t.backgroundColor=Ie(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Ie(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Ie(n.hoverBorderWidth,n.borderWidth),t.radius=n.radius+n.hoverRadius},_resolveDataElementOptions:function(e,t){var n=this,r=n.chart,i=n.getDataset(),a=e.custom||{},o=i.data[t]||{},l=re.prototype._resolveDataElementOptions.apply(n,arguments),u={chart:r,dataIndex:t,dataset:i,datasetIndex:n.index};return n._cachedDataOpts===l&&(l=V.extend({},l)),l.radius=Ae([a.radius,o.r,n._config.radius,r.options.elements.point.radius],u,t),l}}),Ne=V.valueOrDefault,ze=Math.PI,je=2*ze,Re=ze/2;R._set("doughnut",{animation:{animateRotate:!0,animateScale:!1},hover:{mode:"single"},legendCallback:function(e){var t,n,r,i=document.createElement("ul"),a=e.data,o=a.datasets,l=a.labels;if(i.setAttribute("class",e.id+"-legend"),o.length)for(t=0,n=o[0].data.length;t<n;++t)(r=i.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=o[0].backgroundColor[t],l[t]&&r.appendChild(document.createTextNode(l[t]));return i.outerHTML},legend:{labels:{generateLabels:function(e){var t=e.data;return t.labels.length&&t.datasets.length?t.labels.map((function(n,r){var i=e.getDatasetMeta(0),a=i.controller.getStyle(r);return{text:n,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,lineWidth:a.borderWidth,hidden:isNaN(t.datasets[0].data[r])||i.data[r].hidden,index:r}})):[]}},onClick:function(e,t){var n,r,i,a=t.index,o=this.chart;for(n=0,r=(o.data.datasets||[]).length;n<r;++n)(i=o.getDatasetMeta(n)).data[a]&&(i.data[a].hidden=!i.data[a].hidden);o.update()}},cutoutPercentage:50,rotation:-Re,circumference:je,tooltips:{callbacks:{title:function(){return""},label:function(e,t){var n=t.labels[e.index],r=": "+t.datasets[e.datasetIndex].data[e.index];return V.isArray(n)?(n=n.slice())[0]+=r:n+=r,n}}}});var Fe=re.extend({dataElementType:we.Arc,linkScales:V.noop,_dataElementOptions:["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],getRingIndex:function(e){for(var t=0,n=0;n<e;++n)this.chart.isDatasetVisible(n)&&++t;return t},update:function(e){var t,n,r,i,a=this,o=a.chart,l=o.chartArea,u=o.options,s=1,c=1,f=0,d=0,h=a.getMeta(),p=h.data,g=u.cutoutPercentage/100||0,v=u.circumference,m=a._getRingWeight(a.index);if(v<je){var b=u.rotation%je,y=(b+=b>=ze?-je:b<-ze?je:0)+v,x=Math.cos(b),_=Math.sin(b),w=Math.cos(y),k=Math.sin(y),S=b<=0&&y>=0||y>=je,T=b<=Re&&y>=Re||y>=je+Re,E=b<=-Re&&y>=-Re||y>=ze+Re,C=b===-ze||y>=ze?-1:Math.min(x,x*g,w,w*g),P=E?-1:Math.min(_,_*g,k,k*g),M=S?1:Math.max(x,x*g,w,w*g),O=T?1:Math.max(_,_*g,k,k*g);s=(M-C)/2,c=(O-P)/2,f=-(M+C)/2,d=-(O+P)/2}for(r=0,i=p.length;r<i;++r)p[r]._options=a._resolveDataElementOptions(p[r],r);for(o.borderWidth=a.getMaxBorderWidth(),t=(l.right-l.left-o.borderWidth)/s,n=(l.bottom-l.top-o.borderWidth)/c,o.outerRadius=Math.max(Math.min(t,n)/2,0),o.innerRadius=Math.max(o.outerRadius*g,0),o.radiusLength=(o.outerRadius-o.innerRadius)/(a._getVisibleDatasetWeightTotal()||1),o.offsetX=f*o.outerRadius,o.offsetY=d*o.outerRadius,h.total=a.calculateTotal(),a.outerRadius=o.outerRadius-o.radiusLength*a._getRingWeightOffset(a.index),a.innerRadius=Math.max(a.outerRadius-o.radiusLength*m,0),r=0,i=p.length;r<i;++r)a.updateElement(p[r],r,e)},updateElement:function(e,t,n){var r=this,i=r.chart,a=i.chartArea,o=i.options,l=o.animation,u=(a.left+a.right)/2,s=(a.top+a.bottom)/2,c=o.rotation,f=o.rotation,d=r.getDataset(),h=n&&l.animateRotate||e.hidden?0:r.calculateCircumference(d.data[t])*(o.circumference/je),p=n&&l.animateScale?0:r.innerRadius,g=n&&l.animateScale?0:r.outerRadius,v=e._options||{};V.extend(e,{_datasetIndex:r.index,_index:t,_model:{backgroundColor:v.backgroundColor,borderColor:v.borderColor,borderWidth:v.borderWidth,borderAlign:v.borderAlign,x:u+i.offsetX,y:s+i.offsetY,startAngle:c,endAngle:f,circumference:h,outerRadius:g,innerRadius:p,label:V.valueAtIndexOrDefault(d.label,t,i.data.labels[t])}});var m=e._model;n&&l.animateRotate||(m.startAngle=0===t?o.rotation:r.getMeta().data[t-1]._model.endAngle,m.endAngle=m.startAngle+m.circumference),e.pivot()},calculateTotal:function(){var e,t=this.getDataset(),n=this.getMeta(),r=0;return V.each(n.data,(function(n,i){e=t.data[i],isNaN(e)||n.hidden||(r+=Math.abs(e))})),r},calculateCircumference:function(e){var t=this.getMeta().total;return t>0&&!isNaN(e)?je*(Math.abs(e)/t):0},getMaxBorderWidth:function(e){var t,n,r,i,a,o,l,u,s=0,c=this.chart;if(!e)for(t=0,n=c.data.datasets.length;t<n;++t)if(c.isDatasetVisible(t)){e=(r=c.getDatasetMeta(t)).data,t!==this.index&&(a=r.controller);break}if(!e)return 0;for(t=0,n=e.length;t<n;++t)i=e[t],a?(a._configure(),o=a._resolveDataElementOptions(i,t)):o=i._options,"inner"!==o.borderAlign&&(l=o.borderWidth,s=(u=o.hoverBorderWidth)>(s=l>s?l:s)?u:s);return s},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth},t.backgroundColor=Ne(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Ne(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Ne(n.hoverBorderWidth,n.borderWidth)},_getRingWeightOffset:function(e){for(var t=0,n=0;n<e;++n)this.chart.isDatasetVisible(n)&&(t+=this._getRingWeight(n));return t},_getRingWeight:function(e){return Math.max(Ne(this.chart.data.datasets[e].weight,1),0)},_getVisibleDatasetWeightTotal:function(){return this._getRingWeightOffset(this.chart.data.datasets.length)}});R._set("horizontalBar",{hover:{mode:"index",axis:"y"},scales:{xAxes:[{type:"linear",position:"bottom"}],yAxes:[{type:"category",position:"left",offset:!0,gridLines:{offsetGridLines:!0}}]},elements:{rectangle:{borderSkipped:"left"}},tooltips:{mode:"index",axis:"y"}}),R._set("global",{datasets:{horizontalBar:{categoryPercentage:.8,barPercentage:.9}}});var Le=Oe.extend({_getValueScaleId:function(){return this.getMeta().xAxisID},_getIndexScaleId:function(){return this.getMeta().yAxisID}}),Be=V.valueOrDefault,We=V.options.resolve,Ve=V.canvas._isPointInArea;function Ue(e,t){var n=e&&e.options.ticks||{},r=n.reverse,i=void 0===n.min?t:0,a=void 0===n.max?t:0;return{start:r?a:i,end:r?i:a}}function He(e,t,n){var r=n/2,i=Ue(e,r),a=Ue(t,r);return{top:a.end,right:i.end,bottom:a.start,left:i.start}}function qe(e){var t,n,r,i;return V.isObject(e)?(t=e.top,n=e.right,r=e.bottom,i=e.left):t=n=r=i=e,{top:t,right:n,bottom:r,left:i}}R._set("line",{showLines:!0,spanGaps:!1,hover:{mode:"label"},scales:{xAxes:[{type:"category",id:"x-axis-0"}],yAxes:[{type:"linear",id:"y-axis-0"}]}});var $e=re.extend({datasetElementType:we.Line,dataElementType:we.Point,_datasetElementOptions:["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth","cubicInterpolationMode","fill"],_dataElementOptions:{backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},update:function(e){var t,n,r=this,i=r.getMeta(),a=i.dataset,o=i.data||[],l=r.chart.options,u=r._config,s=r._showLine=Be(u.showLine,l.showLines);for(r._xScale=r.getScaleForId(i.xAxisID),r._yScale=r.getScaleForId(i.yAxisID),s&&(void 0!==u.tension&&void 0===u.lineTension&&(u.lineTension=u.tension),a._scale=r._yScale,a._datasetIndex=r.index,a._children=o,a._model=r._resolveDatasetElementOptions(a),a.pivot()),t=0,n=o.length;t<n;++t)r.updateElement(o[t],t,e);for(s&&0!==a._model.tension&&r.updateBezierControlPoints(),t=0,n=o.length;t<n;++t)o[t].pivot()},updateElement:function(e,t,n){var r,i,a=this,o=a.getMeta(),l=e.custom||{},u=a.getDataset(),s=a.index,c=u.data[t],f=a._xScale,d=a._yScale,h=o.dataset._model,p=a._resolveDataElementOptions(e,t);r=f.getPixelForValue("object"==typeof c?c:NaN,t,s),i=n?d.getBasePixel():a.calculatePointY(c,t,s),e._xScale=f,e._yScale=d,e._options=p,e._datasetIndex=s,e._index=t,e._model={x:r,y:i,skip:l.skip||isNaN(r)||isNaN(i),radius:p.radius,pointStyle:p.pointStyle,rotation:p.rotation,backgroundColor:p.backgroundColor,borderColor:p.borderColor,borderWidth:p.borderWidth,tension:Be(l.tension,h?h.tension:0),steppedLine:!!h&&h.steppedLine,hitRadius:p.hitRadius}},_resolveDatasetElementOptions:function(e){var t=this,n=t._config,r=e.custom||{},i=t.chart.options,a=i.elements.line,o=re.prototype._resolveDatasetElementOptions.apply(t,arguments);return o.spanGaps=Be(n.spanGaps,i.spanGaps),o.tension=Be(n.lineTension,a.tension),o.steppedLine=We([r.steppedLine,n.steppedLine,a.stepped]),o.clip=qe(Be(n.clip,He(t._xScale,t._yScale,o.borderWidth))),o},calculatePointY:function(e,t,n){var r,i,a,o,l,u,s,c=this.chart,f=this._yScale,d=0,h=0;if(f.options.stacked){for(l=+f.getRightValue(e),s=(u=c._getSortedVisibleDatasetMetas()).length,r=0;r<s&&(a=u[r]).index!==n;++r)i=c.data.datasets[a.index],"line"===a.type&&a.yAxisID===f.id&&((o=+f.getRightValue(i.data[t]))<0?h+=o||0:d+=o||0);return l<0?f.getPixelForValue(h+l):f.getPixelForValue(d+l)}return f.getPixelForValue(e)},updateBezierControlPoints:function(){var e,t,n,r,i=this.chart,a=this.getMeta(),o=a.dataset._model,l=i.chartArea,u=a.data||[];function s(e,t,n){return Math.max(Math.min(e,n),t)}if(o.spanGaps&&(u=u.filter((function(e){return!e._model.skip}))),"monotone"===o.cubicInterpolationMode)V.splineCurveMonotone(u);else for(e=0,t=u.length;e<t;++e)n=u[e]._model,r=V.splineCurve(V.previousItem(u,e)._model,n,V.nextItem(u,e)._model,o.tension),n.controlPointPreviousX=r.previous.x,n.controlPointPreviousY=r.previous.y,n.controlPointNextX=r.next.x,n.controlPointNextY=r.next.y;if(i.options.elements.line.capBezierPoints)for(e=0,t=u.length;e<t;++e)n=u[e]._model,Ve(n,l)&&(e>0&&Ve(u[e-1]._model,l)&&(n.controlPointPreviousX=s(n.controlPointPreviousX,l.left,l.right),n.controlPointPreviousY=s(n.controlPointPreviousY,l.top,l.bottom)),e<u.length-1&&Ve(u[e+1]._model,l)&&(n.controlPointNextX=s(n.controlPointNextX,l.left,l.right),n.controlPointNextY=s(n.controlPointNextY,l.top,l.bottom)))},draw:function(){var e,t=this.chart,n=this.getMeta(),r=n.data||[],i=t.chartArea,a=t.canvas,o=0,l=r.length;for(this._showLine&&(e=n.dataset._model.clip,V.canvas.clipArea(t.ctx,{left:!1===e.left?0:i.left-e.left,right:!1===e.right?a.width:i.right+e.right,top:!1===e.top?0:i.top-e.top,bottom:!1===e.bottom?a.height:i.bottom+e.bottom}),n.dataset.draw(),V.canvas.unclipArea(t.ctx));o<l;++o)r[o].draw(i)},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth,radius:t.radius},t.backgroundColor=Be(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Be(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Be(n.hoverBorderWidth,n.borderWidth),t.radius=Be(n.hoverRadius,n.radius)}}),Qe=V.options.resolve;R._set("polarArea",{scale:{type:"radialLinear",angleLines:{display:!1},gridLines:{circular:!0},pointLabels:{display:!1},ticks:{beginAtZero:!0}},animation:{animateRotate:!0,animateScale:!0},startAngle:-.5*Math.PI,legendCallback:function(e){var t,n,r,i=document.createElement("ul"),a=e.data,o=a.datasets,l=a.labels;if(i.setAttribute("class",e.id+"-legend"),o.length)for(t=0,n=o[0].data.length;t<n;++t)(r=i.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=o[0].backgroundColor[t],l[t]&&r.appendChild(document.createTextNode(l[t]));return i.outerHTML},legend:{labels:{generateLabels:function(e){var t=e.data;return t.labels.length&&t.datasets.length?t.labels.map((function(n,r){var i=e.getDatasetMeta(0),a=i.controller.getStyle(r);return{text:n,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,lineWidth:a.borderWidth,hidden:isNaN(t.datasets[0].data[r])||i.data[r].hidden,index:r}})):[]}},onClick:function(e,t){var n,r,i,a=t.index,o=this.chart;for(n=0,r=(o.data.datasets||[]).length;n<r;++n)(i=o.getDatasetMeta(n)).data[a].hidden=!i.data[a].hidden;o.update()}},tooltips:{callbacks:{title:function(){return""},label:function(e,t){return t.labels[e.index]+": "+e.yLabel}}}});var Ye=re.extend({dataElementType:we.Arc,linkScales:V.noop,_dataElementOptions:["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],_getIndexScaleId:function(){return this.chart.scale.id},_getValueScaleId:function(){return this.chart.scale.id},update:function(e){var t,n,r,i=this,a=i.getDataset(),o=i.getMeta(),l=i.chart.options.startAngle||0,u=i._starts=[],s=i._angles=[],c=o.data;for(i._updateRadius(),o.count=i.countVisibleElements(),t=0,n=a.data.length;t<n;t++)u[t]=l,r=i._computeAngle(t),s[t]=r,l+=r;for(t=0,n=c.length;t<n;++t)c[t]._options=i._resolveDataElementOptions(c[t],t),i.updateElement(c[t],t,e)},_updateRadius:function(){var e=this,t=e.chart,n=t.chartArea,r=t.options,i=Math.min(n.right-n.left,n.bottom-n.top);t.outerRadius=Math.max(i/2,0),t.innerRadius=Math.max(r.cutoutPercentage?t.outerRadius/100*r.cutoutPercentage:1,0),t.radiusLength=(t.outerRadius-t.innerRadius)/t.getVisibleDatasetCount(),e.outerRadius=t.outerRadius-t.radiusLength*e.index,e.innerRadius=e.outerRadius-t.radiusLength},updateElement:function(e,t,n){var r=this,i=r.chart,a=r.getDataset(),o=i.options,l=o.animation,u=i.scale,s=i.data.labels,c=u.xCenter,f=u.yCenter,d=o.startAngle,h=e.hidden?0:u.getDistanceFromCenterForValue(a.data[t]),p=r._starts[t],g=p+(e.hidden?0:r._angles[t]),v=l.animateScale?0:u.getDistanceFromCenterForValue(a.data[t]),m=e._options||{};V.extend(e,{_datasetIndex:r.index,_index:t,_scale:u,_model:{backgroundColor:m.backgroundColor,borderColor:m.borderColor,borderWidth:m.borderWidth,borderAlign:m.borderAlign,x:c,y:f,innerRadius:0,outerRadius:n?v:h,startAngle:n&&l.animateRotate?d:p,endAngle:n&&l.animateRotate?d:g,label:V.valueAtIndexOrDefault(s,t,s[t])}}),e.pivot()},countVisibleElements:function(){var e=this.getDataset(),t=this.getMeta(),n=0;return V.each(t.data,(function(t,r){isNaN(e.data[r])||t.hidden||n++})),n},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor,i=V.valueOrDefault;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth},t.backgroundColor=i(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=i(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=i(n.hoverBorderWidth,n.borderWidth)},_computeAngle:function(e){var t=this,n=this.getMeta().count,r=t.getDataset(),i=t.getMeta();if(isNaN(r.data[e])||i.data[e].hidden)return 0;var a={chart:t.chart,dataIndex:e,dataset:r,datasetIndex:t.index};return Qe([t.chart.options.elements.arc.angle,2*Math.PI/n],a,e)}});R._set("pie",V.clone(R.doughnut)),R._set("pie",{cutoutPercentage:0});var Ke=Fe,Ge=V.valueOrDefault;R._set("radar",{spanGaps:!1,scale:{type:"radialLinear"},elements:{line:{fill:"start",tension:0}}});var Ze=re.extend({datasetElementType:we.Line,dataElementType:we.Point,linkScales:V.noop,_datasetElementOptions:["backgroundColor","borderWidth","borderColor","borderCapStyle","borderDash","borderDashOffset","borderJoinStyle","fill"],_dataElementOptions:{backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},_getIndexScaleId:function(){return this.chart.scale.id},_getValueScaleId:function(){return this.chart.scale.id},update:function(e){var t,n,r=this,i=r.getMeta(),a=i.dataset,o=i.data||[],l=r.chart.scale,u=r._config;for(void 0!==u.tension&&void 0===u.lineTension&&(u.lineTension=u.tension),a._scale=l,a._datasetIndex=r.index,a._children=o,a._loop=!0,a._model=r._resolveDatasetElementOptions(a),a.pivot(),t=0,n=o.length;t<n;++t)r.updateElement(o[t],t,e);for(r.updateBezierControlPoints(),t=0,n=o.length;t<n;++t)o[t].pivot()},updateElement:function(e,t,n){var r=this,i=e.custom||{},a=r.getDataset(),o=r.chart.scale,l=o.getPointPositionForValue(t,a.data[t]),u=r._resolveDataElementOptions(e,t),s=r.getMeta().dataset._model,c=n?o.xCenter:l.x,f=n?o.yCenter:l.y;e._scale=o,e._options=u,e._datasetIndex=r.index,e._index=t,e._model={x:c,y:f,skip:i.skip||isNaN(c)||isNaN(f),radius:u.radius,pointStyle:u.pointStyle,rotation:u.rotation,backgroundColor:u.backgroundColor,borderColor:u.borderColor,borderWidth:u.borderWidth,tension:Ge(i.tension,s?s.tension:0),hitRadius:u.hitRadius}},_resolveDatasetElementOptions:function(){var e=this,t=e._config,n=e.chart.options,r=re.prototype._resolveDatasetElementOptions.apply(e,arguments);return r.spanGaps=Ge(t.spanGaps,n.spanGaps),r.tension=Ge(t.lineTension,n.elements.line.tension),r},updateBezierControlPoints:function(){var e,t,n,r,i=this.getMeta(),a=this.chart.chartArea,o=i.data||[];function l(e,t,n){return Math.max(Math.min(e,n),t)}for(i.dataset._model.spanGaps&&(o=o.filter((function(e){return!e._model.skip}))),e=0,t=o.length;e<t;++e)n=o[e]._model,r=V.splineCurve(V.previousItem(o,e,!0)._model,n,V.nextItem(o,e,!0)._model,n.tension),n.controlPointPreviousX=l(r.previous.x,a.left,a.right),n.controlPointPreviousY=l(r.previous.y,a.top,a.bottom),n.controlPointNextX=l(r.next.x,a.left,a.right),n.controlPointNextY=l(r.next.y,a.top,a.bottom)},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth,radius:t.radius},t.backgroundColor=Ge(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Ge(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Ge(n.hoverBorderWidth,n.borderWidth),t.radius=Ge(n.hoverRadius,n.radius)}});R._set("scatter",{hover:{mode:"single"},scales:{xAxes:[{id:"x-axis-1",type:"linear",position:"bottom"}],yAxes:[{id:"y-axis-1",type:"linear",position:"left"}]},tooltips:{callbacks:{title:function(){return""},label:function(e){return"("+e.xLabel+", "+e.yLabel+")"}}}}),R._set("global",{datasets:{scatter:{showLine:!1}}});var Xe={bar:Oe,bubble:De,doughnut:Fe,horizontalBar:Le,line:$e,polarArea:Ye,pie:Ke,radar:Ze,scatter:$e};function Je(e,t){return e.native?{x:e.x,y:e.y}:V.getRelativePosition(e,t)}function et(e,t){var n,r,i,a,o,l,u=e._getSortedVisibleDatasetMetas();for(r=0,a=u.length;r<a;++r)for(i=0,o=(n=u[r].data).length;i<o;++i)(l=n[i])._view.skip||t(l)}function tt(e,t){var n=[];return et(e,(function(e){e.inRange(t.x,t.y)&&n.push(e)})),n}function nt(e,t,n,r){var i=Number.POSITIVE_INFINITY,a=[];return et(e,(function(e){if(!n||e.inRange(t.x,t.y)){var o=e.getCenterPoint(),l=r(t,o);l<i?(a=[e],i=l):l===i&&a.push(e)}})),a}function rt(e){var t=-1!==e.indexOf("x"),n=-1!==e.indexOf("y");return function(e,r){var i=t?Math.abs(e.x-r.x):0,a=n?Math.abs(e.y-r.y):0;return Math.sqrt(Math.pow(i,2)+Math.pow(a,2))}}function it(e,t,n){var r=Je(t,e);n.axis=n.axis||"x";var i=rt(n.axis),a=n.intersect?tt(e,r):nt(e,r,!1,i),o=[];return a.length?(e._getSortedVisibleDatasetMetas().forEach((function(e){var t=e.data[a[0]._index];t&&!t._view.skip&&o.push(t)})),o):[]}var at={modes:{single:function(e,t){var n=Je(t,e),r=[];return et(e,(function(e){if(e.inRange(n.x,n.y))return r.push(e),r})),r.slice(0,1)},label:it,index:it,dataset:function(e,t,n){var r=Je(t,e);n.axis=n.axis||"xy";var i=rt(n.axis),a=n.intersect?tt(e,r):nt(e,r,!1,i);return a.length>0&&(a=e.getDatasetMeta(a[0]._datasetIndex).data),a},"x-axis":function(e,t){return it(e,t,{intersect:!1})},point:function(e,t){return tt(e,Je(t,e))},nearest:function(e,t,n){var r=Je(t,e);n.axis=n.axis||"xy";var i=rt(n.axis);return nt(e,r,n.intersect,i)},x:function(e,t,n){var r=Je(t,e),i=[],a=!1;return et(e,(function(e){e.inXRange(r.x)&&i.push(e),e.inRange(r.x,r.y)&&(a=!0)})),n.intersect&&!a&&(i=[]),i},y:function(e,t,n){var r=Je(t,e),i=[],a=!1;return et(e,(function(e){e.inYRange(r.y)&&i.push(e),e.inRange(r.x,r.y)&&(a=!0)})),n.intersect&&!a&&(i=[]),i}}},ot=V.extend;function lt(e,t){return V.where(e,(function(e){return e.pos===t}))}function ut(e,t){return e.sort((function(e,n){var r=t?n:e,i=t?e:n;return r.weight===i.weight?r.index-i.index:r.weight-i.weight}))}function st(e,t,n,r){return Math.max(e[n],t[n])+Math.max(e[r],t[r])}function ct(e,t,n){var r,i,a=n.box,o=e.maxPadding;if(n.size&&(e[n.pos]-=n.size),n.size=n.horizontal?a.height:a.width,e[n.pos]+=n.size,a.getPadding){var l=a.getPadding();o.top=Math.max(o.top,l.top),o.left=Math.max(o.left,l.left),o.bottom=Math.max(o.bottom,l.bottom),o.right=Math.max(o.right,l.right)}if(r=t.outerWidth-st(o,e,"left","right"),i=t.outerHeight-st(o,e,"top","bottom"),r!==e.w||i!==e.h){e.w=r,e.h=i;var u=n.horizontal?[r,e.w]:[i,e.h];return!(u[0]===u[1]||isNaN(u[0])&&isNaN(u[1]))}}function ft(e,t){var n=t.maxPadding;function r(e){var r={left:0,top:0,right:0,bottom:0};return e.forEach((function(e){r[e]=Math.max(t[e],n[e])})),r}return r(e?["left","right"]:["top","bottom"])}function dt(e,t,n){var r,i,a,o,l,u,s=[];for(r=0,i=e.length;r<i;++r)(o=(a=e[r]).box).update(a.width||t.w,a.height||t.h,ft(a.horizontal,t)),ct(t,n,a)&&(u=!0,s.length&&(l=!0)),o.fullWidth||s.push(a);return l&&dt(s,t,n)||u}function ht(e,t,n){var r,i,a,o,l=n.padding,u=t.x,s=t.y;for(r=0,i=e.length;r<i;++r)o=(a=e[r]).box,a.horizontal?(o.left=o.fullWidth?l.left:t.left,o.right=o.fullWidth?n.outerWidth-l.right:t.left+t.w,o.top=s,o.bottom=s+o.height,o.width=o.right-o.left,s=o.bottom):(o.left=u,o.right=u+o.width,o.top=t.top,o.bottom=t.top+t.h,o.height=o.bottom-o.top,u=o.right);t.x=u,t.y=s}R._set("global",{layout:{padding:{top:0,right:0,bottom:0,left:0}}});var pt,gt={defaults:{},addBox:function(e,t){e.boxes||(e.boxes=[]),t.fullWidth=t.fullWidth||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw:function(){t.draw.apply(t,arguments)}}]},e.boxes.push(t)},removeBox:function(e,t){var n=e.boxes?e.boxes.indexOf(t):-1;-1!==n&&e.boxes.splice(n,1)},configure:function(e,t,n){for(var r,i=["fullWidth","position","weight"],a=i.length,o=0;o<a;++o)r=i[o],n.hasOwnProperty(r)&&(t[r]=n[r])},update:function(e,t,n){if(e){var r=e.options.layout||{},i=V.options.toPadding(r.padding),a=t-i.width,o=n-i.height,l=function(e){var t=function(e){var t,n,r,i=[];for(t=0,n=(e||[]).length;t<n;++t)r=e[t],i.push({index:t,box:r,pos:r.position,horizontal:r.isHorizontal(),weight:r.weight});return i}(e),n=ut(lt(t,"left"),!0),r=ut(lt(t,"right")),i=ut(lt(t,"top"),!0),a=ut(lt(t,"bottom"));return{leftAndTop:n.concat(i),rightAndBottom:r.concat(a),chartArea:lt(t,"chartArea"),vertical:n.concat(r),horizontal:i.concat(a)}}(e.boxes),u=l.vertical,s=l.horizontal,c=Object.freeze({outerWidth:t,outerHeight:n,padding:i,availableWidth:a,vBoxMaxWidth:a/2/u.length,hBoxMaxHeight:o/2}),f=ot({maxPadding:ot({},i),w:a,h:o,x:i.left,y:i.top},i);!function(e,t){var n,r,i;for(n=0,r=e.length;n<r;++n)(i=e[n]).width=i.horizontal?i.box.fullWidth&&t.availableWidth:t.vBoxMaxWidth,i.height=i.horizontal&&t.hBoxMaxHeight}(u.concat(s),c),dt(u,f,c),dt(s,f,c)&&dt(u,f,c),function(e){var t=e.maxPadding;function n(n){var r=Math.max(t[n]-e[n],0);return e[n]+=r,r}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}(f),ht(l.leftAndTop,f,c),f.x+=f.w,f.y+=f.h,ht(l.rightAndBottom,f,c),e.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h},V.each(l.chartArea,(function(t){var n=t.box;ot(n,e.chartArea),n.update(f.w,f.h)}))}}},vt=(pt=Object.freeze({__proto__:null,default:"/*\r\n * DOM element rendering detection\r\n * https://davidwalsh.name/detect-node-insertion\r\n */\r\n@keyframes chartjs-render-animation {\r\n\tfrom { opacity: 0.99; }\r\n\tto { opacity: 1; }\r\n}\r\n\r\n.chartjs-render-monitor {\r\n\tanimation: chartjs-render-animation 0.001s;\r\n}\r\n\r\n/*\r\n * DOM element resizing detection\r\n * https://github.com/marcj/css-element-queries\r\n */\r\n.chartjs-size-monitor,\r\n.chartjs-size-monitor-expand,\r\n.chartjs-size-monitor-shrink {\r\n\tposition: absolute;\r\n\tdirection: ltr;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\toverflow: hidden;\r\n\tpointer-events: none;\r\n\tvisibility: hidden;\r\n\tz-index: -1;\r\n}\r\n\r\n.chartjs-size-monitor-expand > div {\r\n\tposition: absolute;\r\n\twidth: 1000000px;\r\n\theight: 1000000px;\r\n\tleft: 0;\r\n\ttop: 0;\r\n}\r\n\r\n.chartjs-size-monitor-shrink > div {\r\n\tposition: absolute;\r\n\twidth: 200%;\r\n\theight: 200%;\r\n\tleft: 0;\r\n\ttop: 0;\r\n}\r\n"}))&&pt.default||pt,mt=["animationstart","webkitAnimationStart"],bt={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"};function yt(e,t){var n=V.getStyle(e,t),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?Number(r[1]):void 0}var xt=!!function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});window.addEventListener("e",null,t)}catch(e){}return e}()&&{passive:!0};function _t(e,t,n){e.addEventListener(t,n,xt)}function wt(e,t,n){e.removeEventListener(t,n,xt)}function kt(e,t,n,r,i){return{type:e,chart:t,native:i||null,x:void 0!==n?n:null,y:void 0!==r?r:null}}function St(e){var t=document.createElement("div");return t.className=e||"",t}function Tt(e,t,n){var r,i,a,o,l=e.$chartjs||(e.$chartjs={}),u=l.resizer=function(e){var t=St("chartjs-size-monitor"),n=St("chartjs-size-monitor-expand"),r=St("chartjs-size-monitor-shrink");n.appendChild(St()),r.appendChild(St()),t.appendChild(n),t.appendChild(r),t._reset=function(){n.scrollLeft=1e6,n.scrollTop=1e6,r.scrollLeft=1e6,r.scrollTop=1e6};var i=function(){t._reset(),e()};return _t(n,"scroll",i.bind(n,"expand")),_t(r,"scroll",i.bind(r,"shrink")),t}((r=function(){if(l.resizer){var r=n.options.maintainAspectRatio&&e.parentNode,i=r?r.clientWidth:0;t(kt("resize",n)),r&&r.clientWidth<i&&n.canvas&&t(kt("resize",n))}},a=!1,o=[],function(){o=Array.prototype.slice.call(arguments),i=i||this,a||(a=!0,V.requestAnimFrame.call(window,(function(){a=!1,r.apply(i,o)})))}));!function(e,t){var n=e.$chartjs||(e.$chartjs={}),r=n.renderProxy=function(e){"chartjs-render-animation"===e.animationName&&t()};V.each(mt,(function(t){_t(e,t,r)})),n.reflow=!!e.offsetParent,e.classList.add("chartjs-render-monitor")}(e,(function(){if(l.resizer){var t=e.parentNode;t&&t!==u.parentNode&&t.insertBefore(u,t.firstChild),u._reset()}}))}function Et(e){var t=e.$chartjs||{},n=t.resizer;delete t.resizer,function(e){var t=e.$chartjs||{},n=t.renderProxy;n&&(V.each(mt,(function(t){wt(e,t,n)})),delete t.renderProxy),e.classList.remove("chartjs-render-monitor")}(e),n&&n.parentNode&&n.parentNode.removeChild(n)}var Ct={disableCSSInjection:!1,_enabled:"undefined"!=typeof window&&"undefined"!=typeof document,_ensureLoaded:function(e){if(!this.disableCSSInjection){var t=e.getRootNode?e.getRootNode():document;!function(e,t){var n=e.$chartjs||(e.$chartjs={});if(!n.containsStyles){n.containsStyles=!0,t="/* Chart.js */\n"+t;var r=document.createElement("style");r.setAttribute("type","text/css"),r.appendChild(document.createTextNode(t)),e.appendChild(r)}}(t.host?t:document.head,vt)}},acquireContext:function(e,t){"string"==typeof e?e=document.getElementById(e):e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas);var n=e&&e.getContext&&e.getContext("2d");return n&&n.canvas===e?(this._ensureLoaded(e),function(e,t){var n=e.style,r=e.getAttribute("height"),i=e.getAttribute("width");if(e.$chartjs={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",null===i||""===i){var a=yt(e,"width");void 0!==a&&(e.width=a)}if(null===r||""===r)if(""===e.style.height)e.height=e.width/(t.options.aspectRatio||2);else{var o=yt(e,"height");void 0!==a&&(e.height=o)}}(e,t),n):null},releaseContext:function(e){var t=e.canvas;if(t.$chartjs){var n=t.$chartjs.initial;["height","width"].forEach((function(e){var r=n[e];V.isNullOrUndef(r)?t.removeAttribute(e):t.setAttribute(e,r)})),V.each(n.style||{},(function(e,n){t.style[n]=e})),t.width=t.width,delete t.$chartjs}},addEventListener:function(e,t,n){var r=e.canvas;if("resize"!==t){var i=n.$chartjs||(n.$chartjs={});_t(r,t,(i.proxies||(i.proxies={}))[e.id+"_"+t]=function(t){n(function(e,t){var n=bt[e.type]||e.type,r=V.getRelativePosition(e,t);return kt(n,t,r.x,r.y,e)}(t,e))})}else Tt(r,n,e)},removeEventListener:function(e,t,n){var r=e.canvas;if("resize"!==t){var i=((n.$chartjs||{}).proxies||{})[e.id+"_"+t];i&&wt(r,t,i)}else Et(r)}};V.addEvent=_t,V.removeEvent=wt;var Pt=Ct._enabled?Ct:{acquireContext:function(e){return e&&e.canvas&&(e=e.canvas),e&&e.getContext("2d")||null}},Mt=V.extend({initialize:function(){},acquireContext:function(){},releaseContext:function(){},addEventListener:function(){},removeEventListener:function(){}},Pt);R._set("global",{plugins:{}});var Ot={_plugins:[],_cacheId:0,register:function(e){var t=this._plugins;[].concat(e).forEach((function(e){-1===t.indexOf(e)&&t.push(e)})),this._cacheId++},unregister:function(e){var t=this._plugins;[].concat(e).forEach((function(e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)})),this._cacheId++},clear:function(){this._plugins=[],this._cacheId++},count:function(){return this._plugins.length},getAll:function(){return this._plugins},notify:function(e,t,n){var r,i,a,o,l,u=this.descriptors(e),s=u.length;for(r=0;r<s;++r)if("function"==typeof(l=(a=(i=u[r]).plugin)[t])&&((o=[e].concat(n||[])).push(i.options),!1===l.apply(a,o)))return!1;return!0},descriptors:function(e){var t=e.$plugins||(e.$plugins={});if(t.id===this._cacheId)return t.descriptors;var n=[],r=[],i=e&&e.config||{},a=i.options&&i.options.plugins||{};return this._plugins.concat(i.plugins||[]).forEach((function(e){if(-1===n.indexOf(e)){var t=e.id,i=a[t];!1!==i&&(!0===i&&(i=V.clone(R.global.plugins[t])),n.push(e),r.push({plugin:e,options:i||{}}))}})),t.descriptors=r,t.id=this._cacheId,r},_invalidate:function(e){delete e.$plugins}},It={constructors:{},defaults:{},registerScaleType:function(e,t,n){this.constructors[e]=t,this.defaults[e]=V.clone(n)},getScaleConstructor:function(e){return this.constructors.hasOwnProperty(e)?this.constructors[e]:void 0},getScaleDefaults:function(e){return this.defaults.hasOwnProperty(e)?V.merge(Object.create(null),[R.scale,this.defaults[e]]):{}},updateScaleDefaults:function(e,t){this.defaults.hasOwnProperty(e)&&(this.defaults[e]=V.extend(this.defaults[e],t))},addScalesToLayout:function(e){V.each(e.scales,(function(t){t.fullWidth=t.options.fullWidth,t.position=t.options.position,t.weight=t.options.weight,gt.addBox(e,t)}))}},At=V.valueOrDefault,Dt=V.rtl.getRtlAdapter;R._set("global",{tooltips:{enabled:!0,custom:null,mode:"nearest",position:"average",intersect:!0,backgroundColor:"rgba(0,0,0,0.8)",titleFontStyle:"bold",titleSpacing:2,titleMarginBottom:6,titleFontColor:"#fff",titleAlign:"left",bodySpacing:2,bodyFontColor:"#fff",bodyAlign:"left",footerFontStyle:"bold",footerSpacing:2,footerMarginTop:6,footerFontColor:"#fff",footerAlign:"left",yPadding:6,xPadding:6,caretPadding:2,caretSize:5,cornerRadius:6,multiKeyBackground:"#fff",displayColors:!0,borderColor:"rgba(0,0,0,0)",borderWidth:0,callbacks:{beforeTitle:V.noop,title:function(e,t){var n="",r=t.labels,i=r?r.length:0;if(e.length>0){var a=e[0];a.label?n=a.label:a.xLabel?n=a.xLabel:i>0&&a.index<i&&(n=r[a.index])}return n},afterTitle:V.noop,beforeBody:V.noop,beforeLabel:V.noop,label:function(e,t){var n=t.datasets[e.datasetIndex].label||"";return n&&(n+=": "),V.isNullOrUndef(e.value)?n+=e.yLabel:n+=e.value,n},labelColor:function(e,t){var n=t.getDatasetMeta(e.datasetIndex).data[e.index]._view;return{borderColor:n.borderColor,backgroundColor:n.backgroundColor}},labelTextColor:function(){return this._options.bodyFontColor},afterLabel:V.noop,afterBody:V.noop,beforeFooter:V.noop,footer:V.noop,afterFooter:V.noop}}});var Nt={average:function(e){if(!e.length)return!1;var t,n,r=0,i=0,a=0;for(t=0,n=e.length;t<n;++t){var o=e[t];if(o&&o.hasValue()){var l=o.tooltipPosition();r+=l.x,i+=l.y,++a}}return{x:r/a,y:i/a}},nearest:function(e,t){var n,r,i,a=t.x,o=t.y,l=Number.POSITIVE_INFINITY;for(n=0,r=e.length;n<r;++n){var u=e[n];if(u&&u.hasValue()){var s=u.getCenterPoint(),c=V.distanceBetweenPoints(t,s);c<l&&(l=c,i=u)}}if(i){var f=i.tooltipPosition();a=f.x,o=f.y}return{x:a,y:o}}};function zt(e,t){return t&&(V.isArray(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function jt(e){return("string"==typeof e||e instanceof String)&&e.indexOf("\n")>-1?e.split("\n"):e}function Rt(e){var t=R.global;return{xPadding:e.xPadding,yPadding:e.yPadding,xAlign:e.xAlign,yAlign:e.yAlign,rtl:e.rtl,textDirection:e.textDirection,bodyFontColor:e.bodyFontColor,_bodyFontFamily:At(e.bodyFontFamily,t.defaultFontFamily),_bodyFontStyle:At(e.bodyFontStyle,t.defaultFontStyle),_bodyAlign:e.bodyAlign,bodyFontSize:At(e.bodyFontSize,t.defaultFontSize),bodySpacing:e.bodySpacing,titleFontColor:e.titleFontColor,_titleFontFamily:At(e.titleFontFamily,t.defaultFontFamily),_titleFontStyle:At(e.titleFontStyle,t.defaultFontStyle),titleFontSize:At(e.titleFontSize,t.defaultFontSize),_titleAlign:e.titleAlign,titleSpacing:e.titleSpacing,titleMarginBottom:e.titleMarginBottom,footerFontColor:e.footerFontColor,_footerFontFamily:At(e.footerFontFamily,t.defaultFontFamily),_footerFontStyle:At(e.footerFontStyle,t.defaultFontStyle),footerFontSize:At(e.footerFontSize,t.defaultFontSize),_footerAlign:e.footerAlign,footerSpacing:e.footerSpacing,footerMarginTop:e.footerMarginTop,caretSize:e.caretSize,cornerRadius:e.cornerRadius,backgroundColor:e.backgroundColor,opacity:0,legendColorBackground:e.multiKeyBackground,displayColors:e.displayColors,borderColor:e.borderColor,borderWidth:e.borderWidth}}function Ft(e,t){return"center"===t?e.x+e.width/2:"right"===t?e.x+e.width-e.xPadding:e.x+e.xPadding}function Lt(e){return zt([],jt(e))}var Bt=K.extend({initialize:function(){this._model=Rt(this._options),this._lastActive=[]},getTitle:function(){var e=this,t=e._options,n=t.callbacks,r=n.beforeTitle.apply(e,arguments),i=n.title.apply(e,arguments),a=n.afterTitle.apply(e,arguments),o=[];return o=zt(o,jt(r)),o=zt(o,jt(i)),o=zt(o,jt(a))},getBeforeBody:function(){return Lt(this._options.callbacks.beforeBody.apply(this,arguments))},getBody:function(e,t){var n=this,r=n._options.callbacks,i=[];return V.each(e,(function(e){var a={before:[],lines:[],after:[]};zt(a.before,jt(r.beforeLabel.call(n,e,t))),zt(a.lines,r.label.call(n,e,t)),zt(a.after,jt(r.afterLabel.call(n,e,t))),i.push(a)})),i},getAfterBody:function(){return Lt(this._options.callbacks.afterBody.apply(this,arguments))},getFooter:function(){var e=this,t=e._options.callbacks,n=t.beforeFooter.apply(e,arguments),r=t.footer.apply(e,arguments),i=t.afterFooter.apply(e,arguments),a=[];return a=zt(a,jt(n)),a=zt(a,jt(r)),a=zt(a,jt(i))},update:function(e){var t,n,r,i,a,o,l,u,s,c,f=this,d=f._options,h=f._model,p=f._model=Rt(d),g=f._active,v=f._data,m={xAlign:h.xAlign,yAlign:h.yAlign},b={x:h.x,y:h.y},y={width:h.width,height:h.height},x={x:h.caretX,y:h.caretY};if(g.length){p.opacity=1;var _=[],w=[];x=Nt[d.position].call(f,g,f._eventPosition);var k=[];for(t=0,n=g.length;t<n;++t)k.push((r=g[t],i=void 0,a=void 0,o=void 0,l=void 0,u=void 0,s=void 0,c=void 0,i=r._xScale,a=r._yScale||r._scale,o=r._index,l=r._datasetIndex,u=r._chart.getDatasetMeta(l).controller,s=u._getIndexScale(),c=u._getValueScale(),{xLabel:i?i.getLabelForIndex(o,l):"",yLabel:a?a.getLabelForIndex(o,l):"",label:s?""+s.getLabelForIndex(o,l):"",value:c?""+c.getLabelForIndex(o,l):"",index:o,datasetIndex:l,x:r._model.x,y:r._model.y}));d.filter&&(k=k.filter((function(e){return d.filter(e,v)}))),d.itemSort&&(k=k.sort((function(e,t){return d.itemSort(e,t,v)}))),V.each(k,(function(e){_.push(d.callbacks.labelColor.call(f,e,f._chart)),w.push(d.callbacks.labelTextColor.call(f,e,f._chart))})),p.title=f.getTitle(k,v),p.beforeBody=f.getBeforeBody(k,v),p.body=f.getBody(k,v),p.afterBody=f.getAfterBody(k,v),p.footer=f.getFooter(k,v),p.x=x.x,p.y=x.y,p.caretPadding=d.caretPadding,p.labelColors=_,p.labelTextColors=w,p.dataPoints=k,y=function(e,t){var n=e._chart.ctx,r=2*t.yPadding,i=0,a=t.body,o=a.reduce((function(e,t){return e+t.before.length+t.lines.length+t.after.length}),0);o+=t.beforeBody.length+t.afterBody.length;var l=t.title.length,u=t.footer.length,s=t.titleFontSize,c=t.bodyFontSize,f=t.footerFontSize;r+=l*s,r+=l?(l-1)*t.titleSpacing:0,r+=l?t.titleMarginBottom:0,r+=o*c,r+=o?(o-1)*t.bodySpacing:0,r+=u?t.footerMarginTop:0,r+=u*f,r+=u?(u-1)*t.footerSpacing:0;var d=0,h=function(e){i=Math.max(i,n.measureText(e).width+d)};return n.font=V.fontString(s,t._titleFontStyle,t._titleFontFamily),V.each(t.title,h),n.font=V.fontString(c,t._bodyFontStyle,t._bodyFontFamily),V.each(t.beforeBody.concat(t.afterBody),h),d=t.displayColors?c+2:0,V.each(a,(function(e){V.each(e.before,h),V.each(e.lines,h),V.each(e.after,h)})),d=0,n.font=V.fontString(f,t._footerFontStyle,t._footerFontFamily),V.each(t.footer,h),{width:i+=2*t.xPadding,height:r}}(this,p),b=function(e,t,n,r){var i=e.x,a=e.y,o=e.caretSize,l=e.caretPadding,u=e.cornerRadius,s=n.xAlign,c=n.yAlign,f=o+l,d=u+l;return"right"===s?i-=t.width:"center"===s&&((i-=t.width/2)+t.width>r.width&&(i=r.width-t.width),i<0&&(i=0)),"top"===c?a+=f:a-="bottom"===c?t.height+f:t.height/2,"center"===c?"left"===s?i+=f:"right"===s&&(i-=f):"left"===s?i-=d:"right"===s&&(i+=d),{x:i,y:a}}(p,y,m=function(e,t){var n,r,i,a,o,l=e._model,u=e._chart,s=e._chart.chartArea,c="center",f="center";l.y<t.height?f="top":l.y>u.height-t.height&&(f="bottom");var d=(s.left+s.right)/2,h=(s.top+s.bottom)/2;"center"===f?(n=function(e){return e<=d},r=function(e){return e>d}):(n=function(e){return e<=t.width/2},r=function(e){return e>=u.width-t.width/2}),i=function(e){return e+t.width+l.caretSize+l.caretPadding>u.width},a=function(e){return e-t.width-l.caretSize-l.caretPadding<0},o=function(e){return e<=h?"top":"bottom"},n(l.x)?(c="left",i(l.x)&&(c="center",f=o(l.y))):r(l.x)&&(c="right",a(l.x)&&(c="center",f=o(l.y)));var p=e._options;return{xAlign:p.xAlign?p.xAlign:c,yAlign:p.yAlign?p.yAlign:f}}(this,y),f._chart)}else p.opacity=0;return p.xAlign=m.xAlign,p.yAlign=m.yAlign,p.x=b.x,p.y=b.y,p.width=y.width,p.height=y.height,p.caretX=x.x,p.caretY=x.y,f._model=p,e&&d.custom&&d.custom.call(f,p),f},drawCaret:function(e,t){var n=this._chart.ctx,r=this._view,i=this.getCaretPosition(e,t,r);n.lineTo(i.x1,i.y1),n.lineTo(i.x2,i.y2),n.lineTo(i.x3,i.y3)},getCaretPosition:function(e,t,n){var r,i,a,o,l,u,s=n.caretSize,c=n.cornerRadius,f=n.xAlign,d=n.yAlign,h=e.x,p=e.y,g=t.width,v=t.height;if("center"===d)l=p+v/2,"left"===f?(i=(r=h)-s,a=r,o=l+s,u=l-s):(i=(r=h+g)+s,a=r,o=l-s,u=l+s);else if("left"===f?(r=(i=h+c+s)-s,a=i+s):"right"===f?(r=(i=h+g-c-s)-s,a=i+s):(r=(i=n.caretX)-s,a=i+s),"top"===d)l=(o=p)-s,u=o;else{l=(o=p+v)+s,u=o;var m=a;a=r,r=m}return{x1:r,x2:i,x3:a,y1:o,y2:l,y3:u}},drawTitle:function(e,t,n){var r,i,a,o=t.title,l=o.length;if(l){var u=Dt(t.rtl,t.x,t.width);for(e.x=Ft(t,t._titleAlign),n.textAlign=u.textAlign(t._titleAlign),n.textBaseline="middle",r=t.titleFontSize,i=t.titleSpacing,n.fillStyle=t.titleFontColor,n.font=V.fontString(r,t._titleFontStyle,t._titleFontFamily),a=0;a<l;++a)n.fillText(o[a],u.x(e.x),e.y+r/2),e.y+=r+i,a+1===l&&(e.y+=t.titleMarginBottom-i)}},drawBody:function(e,t,n){var r,i,a,o,l,u,s,c,f=t.bodyFontSize,d=t.bodySpacing,h=t._bodyAlign,p=t.body,g=t.displayColors,v=0,m=g?Ft(t,"left"):0,b=Dt(t.rtl,t.x,t.width),y=function(t){n.fillText(t,b.x(e.x+v),e.y+f/2),e.y+=f+d},x=b.textAlign(h);for(n.textAlign=h,n.textBaseline="middle",n.font=V.fontString(f,t._bodyFontStyle,t._bodyFontFamily),e.x=Ft(t,x),n.fillStyle=t.bodyFontColor,V.each(t.beforeBody,y),v=g&&"right"!==x?"center"===h?f/2+1:f+2:0,l=0,s=p.length;l<s;++l){for(r=p[l],i=t.labelTextColors[l],a=t.labelColors[l],n.fillStyle=i,V.each(r.before,y),u=0,c=(o=r.lines).length;u<c;++u){if(g){var _=b.x(m);n.fillStyle=t.legendColorBackground,n.fillRect(b.leftForLtr(_,f),e.y,f,f),n.lineWidth=1,n.strokeStyle=a.borderColor,n.strokeRect(b.leftForLtr(_,f),e.y,f,f),n.fillStyle=a.backgroundColor,n.fillRect(b.leftForLtr(b.xPlus(_,1),f-2),e.y+1,f-2,f-2),n.fillStyle=i}y(o[u])}V.each(r.after,y)}v=0,V.each(t.afterBody,y),e.y-=d},drawFooter:function(e,t,n){var r,i,a=t.footer,o=a.length;if(o){var l=Dt(t.rtl,t.x,t.width);for(e.x=Ft(t,t._footerAlign),e.y+=t.footerMarginTop,n.textAlign=l.textAlign(t._footerAlign),n.textBaseline="middle",r=t.footerFontSize,n.fillStyle=t.footerFontColor,n.font=V.fontString(r,t._footerFontStyle,t._footerFontFamily),i=0;i<o;++i)n.fillText(a[i],l.x(e.x),e.y+r/2),e.y+=r+t.footerSpacing}},drawBackground:function(e,t,n,r){n.fillStyle=t.backgroundColor,n.strokeStyle=t.borderColor,n.lineWidth=t.borderWidth;var i=t.xAlign,a=t.yAlign,o=e.x,l=e.y,u=r.width,s=r.height,c=t.cornerRadius;n.beginPath(),n.moveTo(o+c,l),"top"===a&&this.drawCaret(e,r),n.lineTo(o+u-c,l),n.quadraticCurveTo(o+u,l,o+u,l+c),"center"===a&&"right"===i&&this.drawCaret(e,r),n.lineTo(o+u,l+s-c),n.quadraticCurveTo(o+u,l+s,o+u-c,l+s),"bottom"===a&&this.drawCaret(e,r),n.lineTo(o+c,l+s),n.quadraticCurveTo(o,l+s,o,l+s-c),"center"===a&&"left"===i&&this.drawCaret(e,r),n.lineTo(o,l+c),n.quadraticCurveTo(o,l,o+c,l),n.closePath(),n.fill(),t.borderWidth>0&&n.stroke()},draw:function(){var e=this._chart.ctx,t=this._view;if(0!==t.opacity){var n={width:t.width,height:t.height},r={x:t.x,y:t.y},i=Math.abs(t.opacity<.001)?0:t.opacity,a=t.title.length||t.beforeBody.length||t.body.length||t.afterBody.length||t.footer.length;this._options.enabled&&a&&(e.save(),e.globalAlpha=i,this.drawBackground(r,t,e,n),r.y+=t.yPadding,V.rtl.overrideTextDirection(e,t.textDirection),this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),V.rtl.restoreTextDirection(e,t.textDirection),e.restore())}},handleEvent:function(e){var t,n=this,r=n._options;return n._lastActive=n._lastActive||[],"mouseout"===e.type?n._active=[]:(n._active=n._chart.getElementsAtEventForMode(e,r.mode,r),r.reverse&&n._active.reverse()),(t=!V.arrayEquals(n._active,n._lastActive))&&(n._lastActive=n._active,(r.enabled||r.custom)&&(n._eventPosition={x:e.x,y:e.y},n.update(!0),n.pivot())),t}}),Wt=Nt,Vt=Bt;Vt.positioners=Wt;var Ut=V.valueOrDefault;function Ht(){return V.merge(Object.create(null),[].slice.call(arguments),{merger:function(e,t,n,r){if("xAxes"===e||"yAxes"===e){var i,a,o,l=n[e].length;for(t[e]||(t[e]=[]),i=0;i<l;++i)o=n[e][i],a=Ut(o.type,"xAxes"===e?"category":"linear"),i>=t[e].length&&t[e].push({}),!t[e][i].type||o.type&&o.type!==t[e][i].type?V.merge(t[e][i],[It.getScaleDefaults(a),o]):V.merge(t[e][i],o)}else V._merger(e,t,n,r)}})}function qt(){return V.merge(Object.create(null),[].slice.call(arguments),{merger:function(e,t,n,r){var i=t[e]||Object.create(null),a=n[e];"scales"===e?t[e]=Ht(i,a):"scale"===e?t[e]=V.merge(i,[It.getScaleDefaults(a.type),a]):V._merger(e,t,n,r)}})}function $t(e){var t=e.options;V.each(e.scales,(function(t){gt.removeBox(e,t)})),t=qt(R.global,R[e.config.type],t),e.options=e.config.options=t,e.ensureScalesHaveIDs(),e.buildOrUpdateScales(),e.tooltip._options=t.tooltips,e.tooltip.initialize()}function Qt(e,t,n){var r,i=function(e){return e.id===r};do{r=t+n++}while(V.findIndex(e,i)>=0);return r}function Yt(e){return"top"===e||"bottom"===e}function Kt(e,t){return function(n,r){return n[e]===r[e]?n[t]-r[t]:n[e]-r[e]}}R._set("global",{elements:{},events:["mousemove","mouseout","click","touchstart","touchmove"],hover:{onHover:null,mode:"nearest",intersect:!0,animationDuration:400},onClick:null,maintainAspectRatio:!0,responsive:!0,responsiveAnimationDuration:0});var Gt=function(e,t){return this.construct(e,t),this};V.extend(Gt.prototype,{construct:function(e,t){var n=this;t=function(e){var t=(e=e||Object.create(null)).data=e.data||{};return t.datasets=t.datasets||[],t.labels=t.labels||[],e.options=qt(R.global,R[e.type],e.options||{}),e}(t);var r=Mt.acquireContext(e,t),i=r&&r.canvas,a=i&&i.height,o=i&&i.width;n.id=V.uid(),n.ctx=r,n.canvas=i,n.config=t,n.width=o,n.height=a,n.aspectRatio=a?o/a:null,n.options=t.options,n._bufferedRender=!1,n._layers=[],n.chart=n,n.controller=n,Gt.instances[n.id]=n,Object.defineProperty(n,"data",{get:function(){return n.config.data},set:function(e){n.config.data=e}}),r&&i?(n.initialize(),n.update()):console.error("Failed to create chart: can't acquire context from the given item")},initialize:function(){var e=this;return Ot.notify(e,"beforeInit"),V.retinaScale(e,e.options.devicePixelRatio),e.bindEvents(),e.options.responsive&&e.resize(!0),e.initToolTip(),Ot.notify(e,"afterInit"),e},clear:function(){return V.canvas.clear(this),this},stop:function(){return X.cancelAnimation(this),this},resize:function(e){var t=this,n=t.options,r=t.canvas,i=n.maintainAspectRatio&&t.aspectRatio||null,a=Math.max(0,Math.floor(V.getMaximumWidth(r))),o=Math.max(0,Math.floor(i?a/i:V.getMaximumHeight(r)));if((t.width!==a||t.height!==o)&&(r.width=t.width=a,r.height=t.height=o,r.style.width=a+"px",r.style.height=o+"px",V.retinaScale(t,n.devicePixelRatio),!e)){var l={width:a,height:o};Ot.notify(t,"resize",[l]),n.onResize&&n.onResize(t,l),t.stop(),t.update({duration:n.responsiveAnimationDuration})}},ensureScalesHaveIDs:function(){var e=this.options,t=e.scales||{},n=e.scale;V.each(t.xAxes,(function(e,n){e.id||(e.id=Qt(t.xAxes,"x-axis-",n))})),V.each(t.yAxes,(function(e,n){e.id||(e.id=Qt(t.yAxes,"y-axis-",n))})),n&&(n.id=n.id||"scale")},buildOrUpdateScales:function(){var e=this,t=e.options,n=e.scales||{},r=[],i=Object.keys(n).reduce((function(e,t){return e[t]=!1,e}),{});t.scales&&(r=r.concat((t.scales.xAxes||[]).map((function(e){return{options:e,dtype:"category",dposition:"bottom"}})),(t.scales.yAxes||[]).map((function(e){return{options:e,dtype:"linear",dposition:"left"}})))),t.scale&&r.push({options:t.scale,dtype:"radialLinear",isDefault:!0,dposition:"chartArea"}),V.each(r,(function(t){var r=t.options,a=r.id,o=Ut(r.type,t.dtype);Yt(r.position)!==Yt(t.dposition)&&(r.position=t.dposition),i[a]=!0;var l=null;if(a in n&&n[a].type===o)(l=n[a]).options=r,l.ctx=e.ctx,l.chart=e;else{var u=It.getScaleConstructor(o);if(!u)return;l=new u({id:a,type:o,options:r,ctx:e.ctx,chart:e}),n[l.id]=l}l.mergeTicksOptions(),t.isDefault&&(e.scale=l)})),V.each(i,(function(e,t){e||delete n[t]})),e.scales=n,It.addScalesToLayout(this)},buildOrUpdateControllers:function(){var e,t,n=this,r=[],i=n.data.datasets;for(e=0,t=i.length;e<t;e++){var a=i[e],o=n.getDatasetMeta(e),l=a.type||n.config.type;if(o.type&&o.type!==l&&(n.destroyDatasetMeta(e),o=n.getDatasetMeta(e)),o.type=l,o.order=a.order||0,o.index=e,o.controller)o.controller.updateIndex(e),o.controller.linkScales();else{var u=Xe[o.type];if(void 0===u)throw new Error('"'+o.type+'" is not a chart type.');o.controller=new u(n,e),r.push(o.controller)}}return r},resetElements:function(){var e=this;V.each(e.data.datasets,(function(t,n){e.getDatasetMeta(n).controller.reset()}),e)},reset:function(){this.resetElements(),this.tooltip.initialize()},update:function(e){var t,n,r=this;if(e&&"object"==typeof e||(e={duration:e,lazy:arguments[1]}),$t(r),Ot._invalidate(r),!1!==Ot.notify(r,"beforeUpdate")){r.tooltip._data=r.data;var i=r.buildOrUpdateControllers();for(t=0,n=r.data.datasets.length;t<n;t++)r.getDatasetMeta(t).controller.buildOrUpdateElements();r.updateLayout(),r.options.animation&&r.options.animation.duration&&V.each(i,(function(e){e.reset()})),r.updateDatasets(),r.tooltip.initialize(),r.lastActive=[],Ot.notify(r,"afterUpdate"),r._layers.sort(Kt("z","_idx")),r._bufferedRender?r._bufferedRequest={duration:e.duration,easing:e.easing,lazy:e.lazy}:r.render(e)}},updateLayout:function(){var e=this;!1!==Ot.notify(e,"beforeLayout")&&(gt.update(this,this.width,this.height),e._layers=[],V.each(e.boxes,(function(t){t._configure&&t._configure(),e._layers.push.apply(e._layers,t._layers())}),e),e._layers.forEach((function(e,t){e._idx=t})),Ot.notify(e,"afterScaleUpdate"),Ot.notify(e,"afterLayout"))},updateDatasets:function(){if(!1!==Ot.notify(this,"beforeDatasetsUpdate")){for(var e=0,t=this.data.datasets.length;e<t;++e)this.updateDataset(e);Ot.notify(this,"afterDatasetsUpdate")}},updateDataset:function(e){var t=this.getDatasetMeta(e),n={meta:t,index:e};!1!==Ot.notify(this,"beforeDatasetUpdate",[n])&&(t.controller._update(),Ot.notify(this,"afterDatasetUpdate",[n]))},render:function(e){var t=this;e&&"object"==typeof e||(e={duration:e,lazy:arguments[1]});var n=t.options.animation,r=Ut(e.duration,n&&n.duration),i=e.lazy;if(!1!==Ot.notify(t,"beforeRender")){var a=function(e){Ot.notify(t,"afterRender"),V.callback(n&&n.onComplete,[e],t)};if(n&&r){var o=new Z({numSteps:r/16.66,easing:e.easing||n.easing,render:function(e,t){var n=V.easing.effects[t.easing],r=t.currentStep,i=r/t.numSteps;e.draw(n(i),i,r)},onAnimationProgress:n.onProgress,onAnimationComplete:a});X.addAnimation(t,o,r,i)}else t.draw(),a(new Z({numSteps:0,chart:t}));return t}},draw:function(e){var t,n,r=this;if(r.clear(),V.isNullOrUndef(e)&&(e=1),r.transition(e),!(r.width<=0||r.height<=0)&&!1!==Ot.notify(r,"beforeDraw",[e])){for(n=r._layers,t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(r.chartArea);for(r.drawDatasets(e);t<n.length;++t)n[t].draw(r.chartArea);r._drawTooltip(e),Ot.notify(r,"afterDraw",[e])}},transition:function(e){for(var t=0,n=(this.data.datasets||[]).length;t<n;++t)this.isDatasetVisible(t)&&this.getDatasetMeta(t).controller.transition(e);this.tooltip.transition(e)},_getSortedDatasetMetas:function(e){var t,n,r=[];for(t=0,n=(this.data.datasets||[]).length;t<n;++t)e&&!this.isDatasetVisible(t)||r.push(this.getDatasetMeta(t));return r.sort(Kt("order","index")),r},_getSortedVisibleDatasetMetas:function(){return this._getSortedDatasetMetas(!0)},drawDatasets:function(e){var t,n;if(!1!==Ot.notify(this,"beforeDatasetsDraw",[e])){for(n=(t=this._getSortedVisibleDatasetMetas()).length-1;n>=0;--n)this.drawDataset(t[n],e);Ot.notify(this,"afterDatasetsDraw",[e])}},drawDataset:function(e,t){var n={meta:e,index:e.index,easingValue:t};!1!==Ot.notify(this,"beforeDatasetDraw",[n])&&(e.controller.draw(t),Ot.notify(this,"afterDatasetDraw",[n]))},_drawTooltip:function(e){var t=this.tooltip,n={tooltip:t,easingValue:e};!1!==Ot.notify(this,"beforeTooltipDraw",[n])&&(t.draw(),Ot.notify(this,"afterTooltipDraw",[n]))},getElementAtEvent:function(e){return at.modes.single(this,e)},getElementsAtEvent:function(e){return at.modes.label(this,e,{intersect:!0})},getElementsAtXAxis:function(e){return at.modes["x-axis"](this,e,{intersect:!0})},getElementsAtEventForMode:function(e,t,n){var r=at.modes[t];return"function"==typeof r?r(this,e,n):[]},getDatasetAtEvent:function(e){return at.modes.dataset(this,e,{intersect:!0})},getDatasetMeta:function(e){var t=this.data.datasets[e];t._meta||(t._meta={});var n=t._meta[this.id];return n||(n=t._meta[this.id]={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:t.order||0,index:e}),n},getVisibleDatasetCount:function(){for(var e=0,t=0,n=this.data.datasets.length;t<n;++t)this.isDatasetVisible(t)&&e++;return e},isDatasetVisible:function(e){var t=this.getDatasetMeta(e);return"boolean"==typeof t.hidden?!t.hidden:!this.data.datasets[e].hidden},generateLegend:function(){return this.options.legendCallback(this)},destroyDatasetMeta:function(e){var t=this.id,n=this.data.datasets[e],r=n._meta&&n._meta[t];r&&(r.controller.destroy(),delete n._meta[t])},destroy:function(){var e,t,n=this,r=n.canvas;for(n.stop(),e=0,t=n.data.datasets.length;e<t;++e)n.destroyDatasetMeta(e);r&&(n.unbindEvents(),V.canvas.clear(n),Mt.releaseContext(n.ctx),n.canvas=null,n.ctx=null),Ot.notify(n,"destroy"),delete Gt.instances[n.id]},toBase64Image:function(){return this.canvas.toDataURL.apply(this.canvas,arguments)},initToolTip:function(){var e=this;e.tooltip=new Vt({_chart:e,_chartInstance:e,_data:e.data,_options:e.options.tooltips},e)},bindEvents:function(){var e=this,t=e._listeners={},n=function(){e.eventHandler.apply(e,arguments)};V.each(e.options.events,(function(r){Mt.addEventListener(e,r,n),t[r]=n})),e.options.responsive&&(n=function(){e.resize()},Mt.addEventListener(e,"resize",n),t.resize=n)},unbindEvents:function(){var e=this,t=e._listeners;t&&(delete e._listeners,V.each(t,(function(t,n){Mt.removeEventListener(e,n,t)})))},updateHoverStyle:function(e,t,n){var r,i,a,o=n?"set":"remove";for(i=0,a=e.length;i<a;++i)(r=e[i])&&this.getDatasetMeta(r._datasetIndex).controller[o+"HoverStyle"](r);"dataset"===t&&this.getDatasetMeta(e[0]._datasetIndex).controller["_"+o+"DatasetHoverStyle"]()},eventHandler:function(e){var t=this,n=t.tooltip;if(!1!==Ot.notify(t,"beforeEvent",[e])){t._bufferedRender=!0,t._bufferedRequest=null;var r=t.handleEvent(e);n&&(r=n._start?n.handleEvent(e):r|n.handleEvent(e)),Ot.notify(t,"afterEvent",[e]);var i=t._bufferedRequest;return i?t.render(i):r&&!t.animating&&(t.stop(),t.render({duration:t.options.hover.animationDuration,lazy:!0})),t._bufferedRender=!1,t._bufferedRequest=null,t}},handleEvent:function(e){var t,n=this,r=n.options||{},i=r.hover;return n.lastActive=n.lastActive||[],"mouseout"===e.type?n.active=[]:n.active=n.getElementsAtEventForMode(e,i.mode,i),V.callback(r.onHover||r.hover.onHover,[e.native,n.active],n),"mouseup"!==e.type&&"click"!==e.type||r.onClick&&r.onClick.call(n,e.native,n.active),n.lastActive.length&&n.updateHoverStyle(n.lastActive,i.mode,!1),n.active.length&&i.mode&&n.updateHoverStyle(n.active,i.mode,!0),t=!V.arrayEquals(n.active,n.lastActive),n.lastActive=n.active,t}}),Gt.instances={};var Zt=Gt;function Xt(){throw new Error("This method is not implemented: either no adapter can be found or an incomplete integration was provided.")}function Jt(e){this.options=e||{}}Gt.Controller=Gt,Gt.types={},V.configMerge=qt,V.scaleMerge=Ht,V.extend(Jt.prototype,{formats:Xt,parse:Xt,format:Xt,add:Xt,diff:Xt,startOf:Xt,endOf:Xt,_create:function(e){return e}}),Jt.override=function(e){V.extend(Jt.prototype,e)};var en={_date:Jt},tn={formatters:{values:function(e){return V.isArray(e)?e:""+e},linear:function(e,t,n){var r=n.length>3?n[2]-n[1]:n[1]-n[0];Math.abs(r)>1&&e!==Math.floor(e)&&(r=e-Math.floor(e));var i=V.log10(Math.abs(r)),a="";if(0!==e)if(Math.max(Math.abs(n[0]),Math.abs(n[n.length-1]))<1e-4){var o=V.log10(Math.abs(e)),l=Math.floor(o)-Math.floor(i);l=Math.max(Math.min(l,20),0),a=e.toExponential(l)}else{var u=-1*Math.floor(i);u=Math.max(Math.min(u,20),0),a=e.toFixed(u)}else a="0";return a},logarithmic:function(e,t,n){var r=e/Math.pow(10,Math.floor(V.log10(e)));return 0===e?"0":1===r||2===r||5===r||0===t||t===n.length-1?e.toExponential():""}}},nn=V.isArray,rn=V.isNullOrUndef,an=V.valueOrDefault,on=V.valueAtIndexOrDefault;function ln(e,t,n){var r,i=e.getTicks().length,a=Math.min(t,i-1),o=e.getPixelForTick(a),l=e._startPixel,u=e._endPixel;if(!(n&&(r=1===i?Math.max(o-l,u-o):0===t?(e.getPixelForTick(1)-o)/2:(o-e.getPixelForTick(a-1))/2,(o+=a<t?r:-r)<l-1e-6||o>u+1e-6)))return o}function un(e,t,n,r){var i,a,o,l,u,s,c,f,d,h,p,g,v,m=n.length,b=[],y=[],x=[],_=0,w=0;for(i=0;i<m;++i){if(l=n[i].label,u=n[i].major?t.major:t.minor,e.font=s=u.string,c=r[s]=r[s]||{data:{},gc:[]},f=u.lineHeight,d=h=0,rn(l)||nn(l)){if(nn(l))for(a=0,o=l.length;a<o;++a)p=l[a],rn(p)||nn(p)||(d=V.measureText(e,c.data,c.gc,d,p),h+=f)}else d=V.measureText(e,c.data,c.gc,d,l),h=f;b.push(d),y.push(h),x.push(f/2),_=Math.max(d,_),w=Math.max(h,w)}function k(e){return{width:b[e]||0,height:y[e]||0,offset:x[e]||0}}return function(e,t){V.each(e,(function(e){var n,r=e.gc,i=r.length/2;if(i>t){for(n=0;n<i;++n)delete e.data[r[n]];r.splice(0,i)}}))}(r,m),g=b.indexOf(_),v=y.indexOf(w),{first:k(0),last:k(m-1),widest:k(g),highest:k(v)}}function sn(e){return e.drawTicks?e.tickMarkLength:0}function cn(e){var t,n;return e.display?(t=V.options._parseFont(e),n=V.options.toPadding(e.padding),t.lineHeight+n.height):0}function fn(e,t){return V.extend(V.options._parseFont({fontFamily:an(t.fontFamily,e.fontFamily),fontSize:an(t.fontSize,e.fontSize),fontStyle:an(t.fontStyle,e.fontStyle),lineHeight:an(t.lineHeight,e.lineHeight)}),{color:V.options.resolve([t.fontColor,e.fontColor,R.global.defaultFontColor])})}function dn(e){var t=fn(e,e.minor);return{minor:t,major:e.major.enabled?fn(e,e.major):t}}function hn(e){var t,n,r,i=[];for(n=0,r=e.length;n<r;++n)void 0!==(t=e[n])._index&&i.push(t);return i}function pn(e,t,n,r){var i,a,o,l,u=an(n,0),s=Math.min(an(r,e.length),e.length),c=0;for(t=Math.ceil(t),r&&(t=(i=r-n)/Math.floor(i/t)),l=u;l<0;)c++,l=Math.round(u+c*t);for(a=Math.max(u,0);a<s;a++)o=e[a],a===l?(o._index=a,c++,l=Math.round(u+c*t)):delete o.label}R._set("scale",{display:!0,position:"left",offset:!1,gridLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickMarkLength:10,zeroLineWidth:1,zeroLineColor:"rgba(0,0,0,0.25)",zeroLineBorderDash:[],zeroLineBorderDashOffset:0,offsetGridLines:!1,borderDash:[],borderDashOffset:0},scaleLabel:{display:!1,labelString:"",padding:{top:4,bottom:4}},ticks:{beginAtZero:!1,minRotation:0,maxRotation:50,mirror:!1,padding:0,reverse:!1,display:!0,autoSkip:!0,autoSkipPadding:0,labelOffset:0,callback:tn.formatters.values,minor:{},major:{}}});var gn=K.extend({zeroLineIndex:0,getPadding:function(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}},getTicks:function(){return this._ticks},_getLabels:function(){var e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]},mergeTicksOptions:function(){},beforeUpdate:function(){V.callback(this.options.beforeUpdate,[this])},update:function(e,t,n){var r,i,a,o,l,u=this,s=u.options.ticks,c=s.sampleSize;if(u.beforeUpdate(),u.maxWidth=e,u.maxHeight=t,u.margins=V.extend({left:0,right:0,top:0,bottom:0},n),u._ticks=null,u.ticks=null,u._labelSizes=null,u._maxLabelLines=0,u.longestLabelWidth=0,u.longestTextCache=u.longestTextCache||{},u._gridLineItems=null,u._labelItems=null,u.beforeSetDimensions(),u.setDimensions(),u.afterSetDimensions(),u.beforeDataLimits(),u.determineDataLimits(),u.afterDataLimits(),u.beforeBuildTicks(),o=u.buildTicks()||[],(!(o=u.afterBuildTicks(o)||o)||!o.length)&&u.ticks)for(o=[],r=0,i=u.ticks.length;r<i;++r)o.push({value:u.ticks[r],major:!1});return u._ticks=o,l=c<o.length,a=u._convertTicksToLabels(l?function(e,t){for(var n=[],r=e.length/t,i=0,a=e.length;i<a;i+=r)n.push(e[Math.floor(i)]);return n}(o,c):o),u._configure(),u.beforeCalculateTickRotation(),u.calculateTickRotation(),u.afterCalculateTickRotation(),u.beforeFit(),u.fit(),u.afterFit(),u._ticksToDraw=s.display&&(s.autoSkip||"auto"===s.source)?u._autoSkip(o):o,l&&(a=u._convertTicksToLabels(u._ticksToDraw)),u.ticks=a,u.afterUpdate(),u.minSize},_configure:function(){var e,t,n=this,r=n.options.ticks.reverse;n.isHorizontal()?(e=n.left,t=n.right):(e=n.top,t=n.bottom,r=!r),n._startPixel=e,n._endPixel=t,n._reversePixels=r,n._length=t-e},afterUpdate:function(){V.callback(this.options.afterUpdate,[this])},beforeSetDimensions:function(){V.callback(this.options.beforeSetDimensions,[this])},setDimensions:function(){var e=this;e.isHorizontal()?(e.width=e.maxWidth,e.left=0,e.right=e.width):(e.height=e.maxHeight,e.top=0,e.bottom=e.height),e.paddingLeft=0,e.paddingTop=0,e.paddingRight=0,e.paddingBottom=0},afterSetDimensions:function(){V.callback(this.options.afterSetDimensions,[this])},beforeDataLimits:function(){V.callback(this.options.beforeDataLimits,[this])},determineDataLimits:V.noop,afterDataLimits:function(){V.callback(this.options.afterDataLimits,[this])},beforeBuildTicks:function(){V.callback(this.options.beforeBuildTicks,[this])},buildTicks:V.noop,afterBuildTicks:function(e){var t=this;return nn(e)&&e.length?V.callback(t.options.afterBuildTicks,[t,e]):(t.ticks=V.callback(t.options.afterBuildTicks,[t,t.ticks])||t.ticks,e)},beforeTickToLabelConversion:function(){V.callback(this.options.beforeTickToLabelConversion,[this])},convertTicksToLabels:function(){var e=this.options.ticks;this.ticks=this.ticks.map(e.userCallback||e.callback,this)},afterTickToLabelConversion:function(){V.callback(this.options.afterTickToLabelConversion,[this])},beforeCalculateTickRotation:function(){V.callback(this.options.beforeCalculateTickRotation,[this])},calculateTickRotation:function(){var e,t,n,r,i,a,o,l=this,u=l.options,s=u.ticks,c=l.getTicks().length,f=s.minRotation||0,d=s.maxRotation,h=f;!l._isVisible()||!s.display||f>=d||c<=1||!l.isHorizontal()?l.labelRotation=f:(t=(e=l._getLabelSizes()).widest.width,n=e.highest.height-e.highest.offset,r=Math.min(l.maxWidth,l.chart.width-t),t+6>(i=u.offset?l.maxWidth/c:r/(c-1))&&(i=r/(c-(u.offset?.5:1)),a=l.maxHeight-sn(u.gridLines)-s.padding-cn(u.scaleLabel),o=Math.sqrt(t*t+n*n),h=V.toDegrees(Math.min(Math.asin(Math.min((e.highest.height+6)/i,1)),Math.asin(Math.min(a/o,1))-Math.asin(n/o))),h=Math.max(f,Math.min(d,h))),l.labelRotation=h)},afterCalculateTickRotation:function(){V.callback(this.options.afterCalculateTickRotation,[this])},beforeFit:function(){V.callback(this.options.beforeFit,[this])},fit:function(){var e=this,t=e.minSize={width:0,height:0},n=e.chart,r=e.options,i=r.ticks,a=r.scaleLabel,o=r.gridLines,l=e._isVisible(),u="bottom"===r.position,s=e.isHorizontal();if(s?t.width=e.maxWidth:l&&(t.width=sn(o)+cn(a)),s?l&&(t.height=sn(o)+cn(a)):t.height=e.maxHeight,i.display&&l){var c=dn(i),f=e._getLabelSizes(),d=f.first,h=f.last,p=f.widest,g=f.highest,v=.4*c.minor.lineHeight,m=i.padding;if(s){var b=0!==e.labelRotation,y=V.toRadians(e.labelRotation),x=Math.cos(y),_=Math.sin(y),w=_*p.width+x*(g.height-(b?g.offset:0))+(b?0:v);t.height=Math.min(e.maxHeight,t.height+w+m);var k,S,T=e.getPixelForTick(0)-e.left,E=e.right-e.getPixelForTick(e.getTicks().length-1);b?(k=u?x*d.width+_*d.offset:_*(d.height-d.offset),S=u?_*(h.height-h.offset):x*h.width+_*h.offset):(k=d.width/2,S=h.width/2),e.paddingLeft=Math.max((k-T)*e.width/(e.width-T),0)+3,e.paddingRight=Math.max((S-E)*e.width/(e.width-E),0)+3}else{var C=i.mirror?0:p.width+m+v;t.width=Math.min(e.maxWidth,t.width+C),e.paddingTop=d.height/2,e.paddingBottom=h.height/2}}e.handleMargins(),s?(e.width=e._length=n.width-e.margins.left-e.margins.right,e.height=t.height):(e.width=t.width,e.height=e._length=n.height-e.margins.top-e.margins.bottom)},handleMargins:function(){var e=this;e.margins&&(e.margins.left=Math.max(e.paddingLeft,e.margins.left),e.margins.top=Math.max(e.paddingTop,e.margins.top),e.margins.right=Math.max(e.paddingRight,e.margins.right),e.margins.bottom=Math.max(e.paddingBottom,e.margins.bottom))},afterFit:function(){V.callback(this.options.afterFit,[this])},isHorizontal:function(){var e=this.options.position;return"top"===e||"bottom"===e},isFullWidth:function(){return this.options.fullWidth},getRightValue:function(e){if(rn(e))return NaN;if(("number"==typeof e||e instanceof Number)&&!isFinite(e))return NaN;if(e)if(this.isHorizontal()){if(void 0!==e.x)return this.getRightValue(e.x)}else if(void 0!==e.y)return this.getRightValue(e.y);return e},_convertTicksToLabels:function(e){var t,n,r,i=this;for(i.ticks=e.map((function(e){return e.value})),i.beforeTickToLabelConversion(),t=i.convertTicksToLabels(e)||i.ticks,i.afterTickToLabelConversion(),n=0,r=e.length;n<r;++n)e[n].label=t[n];return t},_getLabelSizes:function(){var e=this,t=e._labelSizes;return t||(e._labelSizes=t=un(e.ctx,dn(e.options.ticks),e.getTicks(),e.longestTextCache),e.longestLabelWidth=t.widest.width),t},_parseValue:function(e){var t,n,r,i;return nn(e)?(t=+this.getRightValue(e[0]),n=+this.getRightValue(e[1]),r=Math.min(t,n),i=Math.max(t,n)):(t=void 0,n=e=+this.getRightValue(e),r=e,i=e),{min:r,max:i,start:t,end:n}},_getScaleLabel:function(e){var t=this._parseValue(e);return void 0!==t.start?"["+t.start+", "+t.end+"]":+this.getRightValue(e)},getLabelForIndex:V.noop,getPixelForValue:V.noop,getValueForPixel:V.noop,getPixelForTick:function(e){var t=this.options.offset,n=this._ticks.length,r=1/Math.max(n-(t?0:1),1);return e<0||e>n-1?null:this.getPixelForDecimal(e*r+(t?r/2:0))},getPixelForDecimal:function(e){return this._reversePixels&&(e=1-e),this._startPixel+e*this._length},getDecimalForPixel:function(e){var t=(e-this._startPixel)/this._length;return this._reversePixels?1-t:t},getBasePixel:function(){return this.getPixelForValue(this.getBaseValue())},getBaseValue:function(){var e=this.min,t=this.max;return this.beginAtZero?0:e<0&&t<0?t:e>0&&t>0?e:0},_autoSkip:function(e){var t,n,r,i,a=this.options.ticks,o=this._length,l=a.maxTicksLimit||o/this._tickSize()+1,u=a.major.enabled?function(e){var t,n,r=[];for(t=0,n=e.length;t<n;t++)e[t].major&&r.push(t);return r}(e):[],s=u.length,c=u[0],f=u[s-1];if(s>l)return function(e,t,n){var r,i,a=0,o=t[0];for(n=Math.ceil(n),r=0;r<e.length;r++)i=e[r],r===o?(i._index=r,o=t[++a*n]):delete i.label}(e,u,s/l),hn(e);if(r=function(e,t,n,r){var i,a,o,l,u=function(e){var t,n,r=e.length;if(r<2)return!1;for(n=e[0],t=1;t<r;++t)if(e[t]-e[t-1]!==n)return!1;return n}(e),s=(t.length-1)/r;if(!u)return Math.max(s,1);for(o=0,l=(i=V.math._factorize(u)).length-1;o<l;o++)if((a=i[o])>s)return a;return Math.max(s,1)}(u,e,0,l),s>0){for(t=0,n=s-1;t<n;t++)pn(e,r,u[t],u[t+1]);return i=s>1?(f-c)/(s-1):null,pn(e,r,V.isNullOrUndef(i)?0:c-i,c),pn(e,r,f,V.isNullOrUndef(i)?e.length:f+i),hn(e)}return pn(e,r),hn(e)},_tickSize:function(){var e=this.options.ticks,t=V.toRadians(this.labelRotation),n=Math.abs(Math.cos(t)),r=Math.abs(Math.sin(t)),i=this._getLabelSizes(),a=e.autoSkipPadding||0,o=i?i.widest.width+a:0,l=i?i.highest.height+a:0;return this.isHorizontal()?l*n>o*r?o/n:l/r:l*r<o*n?l/n:o/r},_isVisible:function(){var e,t,n,r=this.chart,i=this.options.display;if("auto"!==i)return!!i;for(e=0,t=r.data.datasets.length;e<t;++e)if(r.isDatasetVisible(e)&&((n=r.getDatasetMeta(e)).xAxisID===this.id||n.yAxisID===this.id))return!0;return!1},_computeGridLineItems:function(e){var t,n,r,i,a,o,l,u,s,c,f,d,h,p,g,v,m,b=this,y=b.chart,x=b.options,_=x.gridLines,w=x.position,k=_.offsetGridLines,S=b.isHorizontal(),T=b._ticksToDraw,E=T.length+(k?1:0),C=sn(_),P=[],M=_.drawBorder?on(_.lineWidth,0,0):0,O=M/2,I=V._alignPixel,A=function(e){return I(y,e,M)};for("top"===w?(t=A(b.bottom),l=b.bottom-C,s=t-O,f=A(e.top)+O,h=e.bottom):"bottom"===w?(t=A(b.top),f=e.top,h=A(e.bottom)-O,l=t+O,s=b.top+C):"left"===w?(t=A(b.right),o=b.right-C,u=t-O,c=A(e.left)+O,d=e.right):(t=A(b.left),c=e.left,d=A(e.right)-O,o=t+O,u=b.left+C),n=0;n<E;++n)r=T[n]||{},rn(r.label)&&n<T.length||(n===b.zeroLineIndex&&x.offset===k?(p=_.zeroLineWidth,g=_.zeroLineColor,v=_.zeroLineBorderDash||[],m=_.zeroLineBorderDashOffset||0):(p=on(_.lineWidth,n,1),g=on(_.color,n,"rgba(0,0,0,0.1)"),v=_.borderDash||[],m=_.borderDashOffset||0),void 0!==(i=ln(b,r._index||n,k))&&(a=I(y,i,p),S?o=u=c=d=a:l=s=f=h=a,P.push({tx1:o,ty1:l,tx2:u,ty2:s,x1:c,y1:f,x2:d,y2:h,width:p,color:g,borderDash:v,borderDashOffset:m})));return P.ticksLength=E,P.borderValue=t,P},_computeLabelItems:function(){var e,t,n,r,i,a,o,l,u,s,c,f,d=this,h=d.options,p=h.ticks,g=h.position,v=p.mirror,m=d.isHorizontal(),b=d._ticksToDraw,y=dn(p),x=p.padding,_=sn(h.gridLines),w=-V.toRadians(d.labelRotation),k=[];for("top"===g?(a=d.bottom-_-x,o=w?"left":"center"):"bottom"===g?(a=d.top+_+x,o=w?"right":"center"):"left"===g?(i=d.right-(v?0:_)-x,o=v?"left":"right"):(i=d.left+(v?0:_)+x,o=v?"right":"left"),e=0,t=b.length;e<t;++e)r=(n=b[e]).label,rn(r)||(l=d.getPixelForTick(n._index||e)+p.labelOffset,s=(u=n.major?y.major:y.minor).lineHeight,c=nn(r)?r.length:1,m?(i=l,f="top"===g?((w?1:.5)-c)*s:(w?0:.5)*s):(a=l,f=(1-c)*s/2),k.push({x:i,y:a,rotation:w,label:r,font:u,textOffset:f,textAlign:o}));return k},_drawGrid:function(e){var t=this,n=t.options.gridLines;if(n.display){var r,i,a,o,l,u=t.ctx,s=t.chart,c=V._alignPixel,f=n.drawBorder?on(n.lineWidth,0,0):0,d=t._gridLineItems||(t._gridLineItems=t._computeGridLineItems(e));for(a=0,o=d.length;a<o;++a)r=(l=d[a]).width,i=l.color,r&&i&&(u.save(),u.lineWidth=r,u.strokeStyle=i,u.setLineDash&&(u.setLineDash(l.borderDash),u.lineDashOffset=l.borderDashOffset),u.beginPath(),n.drawTicks&&(u.moveTo(l.tx1,l.ty1),u.lineTo(l.tx2,l.ty2)),n.drawOnChartArea&&(u.moveTo(l.x1,l.y1),u.lineTo(l.x2,l.y2)),u.stroke(),u.restore());if(f){var h,p,g,v,m=f,b=on(n.lineWidth,d.ticksLength-1,1),y=d.borderValue;t.isHorizontal()?(h=c(s,t.left,m)-m/2,p=c(s,t.right,b)+b/2,g=v=y):(g=c(s,t.top,m)-m/2,v=c(s,t.bottom,b)+b/2,h=p=y),u.lineWidth=f,u.strokeStyle=on(n.color,0),u.beginPath(),u.moveTo(h,g),u.lineTo(p,v),u.stroke()}}},_drawLabels:function(){var e=this;if(e.options.ticks.display){var t,n,r,i,a,o,l,u,s=e.ctx,c=e._labelItems||(e._labelItems=e._computeLabelItems());for(t=0,r=c.length;t<r;++t){if(o=(a=c[t]).font,s.save(),s.translate(a.x,a.y),s.rotate(a.rotation),s.font=o.string,s.fillStyle=o.color,s.textBaseline="middle",s.textAlign=a.textAlign,l=a.label,u=a.textOffset,nn(l))for(n=0,i=l.length;n<i;++n)s.fillText(""+l[n],0,u),u+=o.lineHeight;else s.fillText(l,0,u);s.restore()}}},_drawTitle:function(){var e=this,t=e.ctx,n=e.options,r=n.scaleLabel;if(r.display){var i,a,o=an(r.fontColor,R.global.defaultFontColor),l=V.options._parseFont(r),u=V.options.toPadding(r.padding),s=l.lineHeight/2,c=n.position,f=0;if(e.isHorizontal())i=e.left+e.width/2,a="bottom"===c?e.bottom-s-u.bottom:e.top+s+u.top;else{var d="left"===c;i=d?e.left+s+u.top:e.right-s-u.top,a=e.top+e.height/2,f=d?-.5*Math.PI:.5*Math.PI}t.save(),t.translate(i,a),t.rotate(f),t.textAlign="center",t.textBaseline="middle",t.fillStyle=o,t.font=l.string,t.fillText(r.labelString,0,0),t.restore()}},draw:function(e){this._isVisible()&&(this._drawGrid(e),this._drawTitle(),this._drawLabels())},_layers:function(){var e=this,t=e.options,n=t.ticks&&t.ticks.z||0,r=t.gridLines&&t.gridLines.z||0;return e._isVisible()&&n!==r&&e.draw===e._draw?[{z:r,draw:function(){e._drawGrid.apply(e,arguments),e._drawTitle.apply(e,arguments)}},{z:n,draw:function(){e._drawLabels.apply(e,arguments)}}]:[{z:n,draw:function(){e.draw.apply(e,arguments)}}]},_getMatchingVisibleMetas:function(e){var t=this,n=t.isHorizontal();return t.chart._getSortedVisibleDatasetMetas().filter((function(r){return(!e||r.type===e)&&(n?r.xAxisID===t.id:r.yAxisID===t.id)}))}});gn.prototype._draw=gn.prototype.draw;var vn=gn,mn=V.isNullOrUndef,bn=vn.extend({determineDataLimits:function(){var e,t=this,n=t._getLabels(),r=t.options.ticks,i=r.min,a=r.max,o=0,l=n.length-1;void 0!==i&&(e=n.indexOf(i))>=0&&(o=e),void 0!==a&&(e=n.indexOf(a))>=0&&(l=e),t.minIndex=o,t.maxIndex=l,t.min=n[o],t.max=n[l]},buildTicks:function(){var e=this._getLabels(),t=this.minIndex,n=this.maxIndex;this.ticks=0===t&&n===e.length-1?e:e.slice(t,n+1)},getLabelForIndex:function(e,t){var n=this.chart;return n.getDatasetMeta(t).controller._getValueScaleId()===this.id?this.getRightValue(n.data.datasets[t].data[e]):this._getLabels()[e]},_configure:function(){var e=this,t=e.options.offset,n=e.ticks;vn.prototype._configure.call(e),e.isHorizontal()||(e._reversePixels=!e._reversePixels),n&&(e._startValue=e.minIndex-(t?.5:0),e._valueRange=Math.max(n.length-(t?0:1),1))},getPixelForValue:function(e,t,n){var r,i,a,o=this;return mn(t)||mn(n)||(e=o.chart.data.datasets[n].data[t]),mn(e)||(r=o.isHorizontal()?e.x:e.y),(void 0!==r||void 0!==e&&isNaN(t))&&(i=o._getLabels(),e=V.valueOrDefault(r,e),t=-1!==(a=i.indexOf(e))?a:t,isNaN(t)&&(t=e)),o.getPixelForDecimal((t-o._startValue)/o._valueRange)},getPixelForTick:function(e){var t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e],e+this.minIndex)},getValueForPixel:function(e){var t=Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange);return Math.min(Math.max(t,0),this.ticks.length-1)},getBasePixel:function(){return this.bottom}}),yn={position:"bottom"};bn._defaults=yn;var xn=V.noop,_n=V.isNullOrUndef,wn=vn.extend({getRightValue:function(e){return"string"==typeof e?+e:vn.prototype.getRightValue.call(this,e)},handleTickRangeOptions:function(){var e=this,t=e.options.ticks;if(t.beginAtZero){var n=V.sign(e.min),r=V.sign(e.max);n<0&&r<0?e.max=0:n>0&&r>0&&(e.min=0)}var i=void 0!==t.min||void 0!==t.suggestedMin,a=void 0!==t.max||void 0!==t.suggestedMax;void 0!==t.min?e.min=t.min:void 0!==t.suggestedMin&&(null===e.min?e.min=t.suggestedMin:e.min=Math.min(e.min,t.suggestedMin)),void 0!==t.max?e.max=t.max:void 0!==t.suggestedMax&&(null===e.max?e.max=t.suggestedMax:e.max=Math.max(e.max,t.suggestedMax)),i!==a&&e.min>=e.max&&(i?e.max=e.min+1:e.min=e.max-1),e.min===e.max&&(e.max++,t.beginAtZero||e.min--)},getTickLimit:function(){var e,t=this.options.ticks,n=t.stepSize,r=t.maxTicksLimit;return n?e=Math.ceil(this.max/n)-Math.floor(this.min/n)+1:(e=this._computeTickLimit(),r=r||11),r&&(e=Math.min(r,e)),e},_computeTickLimit:function(){return Number.POSITIVE_INFINITY},handleDirectionalChanges:xn,buildTicks:function(){var e=this,t=e.options.ticks,n=e.getTickLimit(),r={maxTicks:n=Math.max(2,n),min:t.min,max:t.max,precision:t.precision,stepSize:V.valueOrDefault(t.fixedStepSize,t.stepSize)},i=e.ticks=function(e,t){var n,r,i,a,o=[],l=e.stepSize,u=l||1,s=e.maxTicks-1,c=e.min,f=e.max,d=e.precision,h=t.min,p=t.max,g=V.niceNum((p-h)/s/u)*u;if(g<1e-14&&_n(c)&&_n(f))return[h,p];(a=Math.ceil(p/g)-Math.floor(h/g))>s&&(g=V.niceNum(a*g/s/u)*u),l||_n(d)?n=Math.pow(10,V._decimalPlaces(g)):(n=Math.pow(10,d),g=Math.ceil(g*n)/n),r=Math.floor(h/g)*g,i=Math.ceil(p/g)*g,l&&(!_n(c)&&V.almostWhole(c/g,g/1e3)&&(r=c),!_n(f)&&V.almostWhole(f/g,g/1e3)&&(i=f)),a=(i-r)/g,a=V.almostEquals(a,Math.round(a),g/1e3)?Math.round(a):Math.ceil(a),r=Math.round(r*n)/n,i=Math.round(i*n)/n,o.push(_n(c)?r:c);for(var v=1;v<a;++v)o.push(Math.round((r+v*g)*n)/n);return o.push(_n(f)?i:f),o}(r,e);e.handleDirectionalChanges(),e.max=V.max(i),e.min=V.min(i),t.reverse?(i.reverse(),e.start=e.max,e.end=e.min):(e.start=e.min,e.end=e.max)},convertTicksToLabels:function(){var e=this;e.ticksAsNumbers=e.ticks.slice(),e.zeroLineIndex=e.ticks.indexOf(0),vn.prototype.convertTicksToLabels.call(e)},_configure:function(){var e,t=this,n=t.getTicks(),r=t.min,i=t.max;vn.prototype._configure.call(t),t.options.offset&&n.length&&(r-=e=(i-r)/Math.max(n.length-1,1)/2,i+=e),t._startValue=r,t._endValue=i,t._valueRange=i-r}}),kn={position:"left",ticks:{callback:tn.formatters.linear}};function Sn(e,t,n,r){var i,a,o=e.options,l=function(e,t,n){var r=[n.type,void 0===t&&void 0===n.stack?n.index:"",n.stack].join(".");return void 0===e[r]&&(e[r]={pos:[],neg:[]}),e[r]}(t,o.stacked,n),u=l.pos,s=l.neg,c=r.length;for(i=0;i<c;++i)a=e._parseValue(r[i]),isNaN(a.min)||isNaN(a.max)||n.data[i].hidden||(u[i]=u[i]||0,s[i]=s[i]||0,o.relativePoints?u[i]=100:a.min<0||a.max<0?s[i]+=a.min:u[i]+=a.max)}function Tn(e,t,n){var r,i,a=n.length;for(r=0;r<a;++r)i=e._parseValue(n[r]),isNaN(i.min)||isNaN(i.max)||t.data[r].hidden||(e.min=Math.min(e.min,i.min),e.max=Math.max(e.max,i.max))}var En=wn.extend({determineDataLimits:function(){var e,t,n,r,i=this,a=i.options,o=i.chart.data.datasets,l=i._getMatchingVisibleMetas(),u=a.stacked,s={},c=l.length;if(i.min=Number.POSITIVE_INFINITY,i.max=Number.NEGATIVE_INFINITY,void 0===u)for(e=0;!u&&e<c;++e)u=void 0!==(t=l[e]).stack;for(e=0;e<c;++e)n=o[(t=l[e]).index].data,u?Sn(i,s,t,n):Tn(i,t,n);V.each(s,(function(e){r=e.pos.concat(e.neg),i.min=Math.min(i.min,V.min(r)),i.max=Math.max(i.max,V.max(r))})),i.min=V.isFinite(i.min)&&!isNaN(i.min)?i.min:0,i.max=V.isFinite(i.max)&&!isNaN(i.max)?i.max:1,i.handleTickRangeOptions()},_computeTickLimit:function(){var e;return this.isHorizontal()?Math.ceil(this.width/40):(e=V.options._parseFont(this.options.ticks),Math.ceil(this.height/e.lineHeight))},handleDirectionalChanges:function(){this.isHorizontal()||this.ticks.reverse()},getLabelForIndex:function(e,t){return this._getScaleLabel(this.chart.data.datasets[t].data[e])},getPixelForValue:function(e){return this.getPixelForDecimal((+this.getRightValue(e)-this._startValue)/this._valueRange)},getValueForPixel:function(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange},getPixelForTick:function(e){var t=this.ticksAsNumbers;return e<0||e>t.length-1?null:this.getPixelForValue(t[e])}}),Cn=kn;En._defaults=Cn;var Pn=V.valueOrDefault,Mn=V.math.log10,On={position:"left",ticks:{callback:tn.formatters.logarithmic}};function In(e,t){return V.isFinite(e)&&e>=0?e:t}var An=vn.extend({determineDataLimits:function(){var e,t,n,r,i,a,o=this,l=o.options,u=o.chart,s=u.data.datasets,c=o.isHorizontal();function f(e){return c?e.xAxisID===o.id:e.yAxisID===o.id}o.min=Number.POSITIVE_INFINITY,o.max=Number.NEGATIVE_INFINITY,o.minNotZero=Number.POSITIVE_INFINITY;var d=l.stacked;if(void 0===d)for(e=0;e<s.length;e++)if(t=u.getDatasetMeta(e),u.isDatasetVisible(e)&&f(t)&&void 0!==t.stack){d=!0;break}if(l.stacked||d){var h={};for(e=0;e<s.length;e++){var p=[(t=u.getDatasetMeta(e)).type,void 0===l.stacked&&void 0===t.stack?e:"",t.stack].join(".");if(u.isDatasetVisible(e)&&f(t))for(void 0===h[p]&&(h[p]=[]),i=0,a=(r=s[e].data).length;i<a;i++){var g=h[p];n=o._parseValue(r[i]),isNaN(n.min)||isNaN(n.max)||t.data[i].hidden||n.min<0||n.max<0||(g[i]=g[i]||0,g[i]+=n.max)}}V.each(h,(function(e){if(e.length>0){var t=V.min(e),n=V.max(e);o.min=Math.min(o.min,t),o.max=Math.max(o.max,n)}}))}else for(e=0;e<s.length;e++)if(t=u.getDatasetMeta(e),u.isDatasetVisible(e)&&f(t))for(i=0,a=(r=s[e].data).length;i<a;i++)n=o._parseValue(r[i]),isNaN(n.min)||isNaN(n.max)||t.data[i].hidden||n.min<0||n.max<0||(o.min=Math.min(n.min,o.min),o.max=Math.max(n.max,o.max),0!==n.min&&(o.minNotZero=Math.min(n.min,o.minNotZero)));o.min=V.isFinite(o.min)?o.min:null,o.max=V.isFinite(o.max)?o.max:null,o.minNotZero=V.isFinite(o.minNotZero)?o.minNotZero:null,this.handleTickRangeOptions()},handleTickRangeOptions:function(){var e=this,t=e.options.ticks;e.min=In(t.min,e.min),e.max=In(t.max,e.max),e.min===e.max&&(0!==e.min&&null!==e.min?(e.min=Math.pow(10,Math.floor(Mn(e.min))-1),e.max=Math.pow(10,Math.floor(Mn(e.max))+1)):(e.min=1,e.max=10)),null===e.min&&(e.min=Math.pow(10,Math.floor(Mn(e.max))-1)),null===e.max&&(e.max=0!==e.min?Math.pow(10,Math.floor(Mn(e.min))+1):10),null===e.minNotZero&&(e.min>0?e.minNotZero=e.min:e.max<1?e.minNotZero=Math.pow(10,Math.floor(Mn(e.max))):e.minNotZero=1)},buildTicks:function(){var e=this,t=e.options.ticks,n=!e.isHorizontal(),r={min:In(t.min),max:In(t.max)},i=e.ticks=function(e,t){var n,r,i=[],a=Pn(e.min,Math.pow(10,Math.floor(Mn(t.min)))),o=Math.floor(Mn(t.max)),l=Math.ceil(t.max/Math.pow(10,o));0===a?(n=Math.floor(Mn(t.minNotZero)),r=Math.floor(t.minNotZero/Math.pow(10,n)),i.push(a),a=r*Math.pow(10,n)):(n=Math.floor(Mn(a)),r=Math.floor(a/Math.pow(10,n)));var u=n<0?Math.pow(10,Math.abs(n)):1;do{i.push(a),10==++r&&(r=1,u=++n>=0?1:u),a=Math.round(r*Math.pow(10,n)*u)/u}while(n<o||n===o&&r<l);var s=Pn(e.max,a);return i.push(s),i}(r,e);e.max=V.max(i),e.min=V.min(i),t.reverse?(n=!n,e.start=e.max,e.end=e.min):(e.start=e.min,e.end=e.max),n&&i.reverse()},convertTicksToLabels:function(){this.tickValues=this.ticks.slice(),vn.prototype.convertTicksToLabels.call(this)},getLabelForIndex:function(e,t){return this._getScaleLabel(this.chart.data.datasets[t].data[e])},getPixelForTick:function(e){var t=this.tickValues;return e<0||e>t.length-1?null:this.getPixelForValue(t[e])},_getFirstTickValue:function(e){var t=Math.floor(Mn(e));return Math.floor(e/Math.pow(10,t))*Math.pow(10,t)},_configure:function(){var e=this,t=e.min,n=0;vn.prototype._configure.call(e),0===t&&(t=e._getFirstTickValue(e.minNotZero),n=Pn(e.options.ticks.fontSize,R.global.defaultFontSize)/e._length),e._startValue=Mn(t),e._valueOffset=n,e._valueRange=(Mn(e.max)-Mn(t))/(1-n)},getPixelForValue:function(e){var t=this,n=0;return(e=+t.getRightValue(e))>t.min&&e>0&&(n=(Mn(e)-t._startValue)/t._valueRange+t._valueOffset),t.getPixelForDecimal(n)},getValueForPixel:function(e){var t=this,n=t.getDecimalForPixel(e);return 0===n&&0===t.min?0:Math.pow(10,t._startValue+(n-t._valueOffset)*t._valueRange)}}),Dn=On;An._defaults=Dn;var Nn=V.valueOrDefault,zn=V.valueAtIndexOrDefault,jn=V.options.resolve,Rn={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,borderDash:[],borderDashOffset:0},gridLines:{circular:!1},ticks:{showLabelBackdrop:!0,backdropColor:"rgba(255,255,255,0.75)",backdropPaddingY:2,backdropPaddingX:2,callback:tn.formatters.linear},pointLabels:{display:!0,fontSize:10,callback:function(e){return e}}};function Fn(e){var t=e.ticks;return t.display&&e.display?Nn(t.fontSize,R.global.defaultFontSize)+2*t.backdropPaddingY:0}function Ln(e,t,n,r,i){return e===r||e===i?{start:t-n/2,end:t+n/2}:e<r||e>i?{start:t-n,end:t}:{start:t,end:t+n}}function Bn(e){return 0===e||180===e?"center":e<180?"left":"right"}function Wn(e,t,n,r){var i,a,o=n.y+r/2;if(V.isArray(t))for(i=0,a=t.length;i<a;++i)e.fillText(t[i],n.x,o),o+=r;else e.fillText(t,n.x,o)}function Vn(e,t,n){90===e||270===e?n.y-=t.h/2:(e>270||e<90)&&(n.y-=t.h)}function Un(e){return V.isNumber(e)?e:0}var Hn=wn.extend({setDimensions:function(){var e=this;e.width=e.maxWidth,e.height=e.maxHeight,e.paddingTop=Fn(e.options)/2,e.xCenter=Math.floor(e.width/2),e.yCenter=Math.floor((e.height-e.paddingTop)/2),e.drawingArea=Math.min(e.height-e.paddingTop,e.width)/2},determineDataLimits:function(){var e=this,t=e.chart,n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;V.each(t.data.datasets,(function(i,a){if(t.isDatasetVisible(a)){var o=t.getDatasetMeta(a);V.each(i.data,(function(t,i){var a=+e.getRightValue(t);isNaN(a)||o.data[i].hidden||(n=Math.min(a,n),r=Math.max(a,r))}))}})),e.min=n===Number.POSITIVE_INFINITY?0:n,e.max=r===Number.NEGATIVE_INFINITY?0:r,e.handleTickRangeOptions()},_computeTickLimit:function(){return Math.ceil(this.drawingArea/Fn(this.options))},convertTicksToLabels:function(){var e=this;wn.prototype.convertTicksToLabels.call(e),e.pointLabels=e.chart.data.labels.map((function(){var t=V.callback(e.options.pointLabels.callback,arguments,e);return t||0===t?t:""}))},getLabelForIndex:function(e,t){return+this.getRightValue(this.chart.data.datasets[t].data[e])},fit:function(){var e=this.options;e.display&&e.pointLabels.display?function(e){var t,n,r,i=V.options._parseFont(e.options.pointLabels),a={l:0,r:e.width,t:0,b:e.height-e.paddingTop},o={};e.ctx.font=i.string,e._pointLabelSizes=[];var l,u,s,c=e.chart.data.labels.length;for(t=0;t<c;t++){r=e.getPointPosition(t,e.drawingArea+5),l=e.ctx,u=i.lineHeight,s=e.pointLabels[t],n=V.isArray(s)?{w:V.longestText(l,l.font,s),h:s.length*u}:{w:l.measureText(s).width,h:u},e._pointLabelSizes[t]=n;var f=e.getIndexAngle(t),d=V.toDegrees(f)%360,h=Ln(d,r.x,n.w,0,180),p=Ln(d,r.y,n.h,90,270);h.start<a.l&&(a.l=h.start,o.l=f),h.end>a.r&&(a.r=h.end,o.r=f),p.start<a.t&&(a.t=p.start,o.t=f),p.end>a.b&&(a.b=p.end,o.b=f)}e.setReductions(e.drawingArea,a,o)}(this):this.setCenterPoint(0,0,0,0)},setReductions:function(e,t,n){var r=this,i=t.l/Math.sin(n.l),a=Math.max(t.r-r.width,0)/Math.sin(n.r),o=-t.t/Math.cos(n.t),l=-Math.max(t.b-(r.height-r.paddingTop),0)/Math.cos(n.b);i=Un(i),a=Un(a),o=Un(o),l=Un(l),r.drawingArea=Math.min(Math.floor(e-(i+a)/2),Math.floor(e-(o+l)/2)),r.setCenterPoint(i,a,o,l)},setCenterPoint:function(e,t,n,r){var i=this,a=i.width-t-i.drawingArea,o=e+i.drawingArea,l=n+i.drawingArea,u=i.height-i.paddingTop-r-i.drawingArea;i.xCenter=Math.floor((o+a)/2+i.left),i.yCenter=Math.floor((l+u)/2+i.top+i.paddingTop)},getIndexAngle:function(e){var t=this.chart,n=(e*(360/t.data.labels.length)+((t.options||{}).startAngle||0))%360;return(n<0?n+360:n)*Math.PI*2/360},getDistanceFromCenterForValue:function(e){var t=this;if(V.isNullOrUndef(e))return NaN;var n=t.drawingArea/(t.max-t.min);return t.options.ticks.reverse?(t.max-e)*n:(e-t.min)*n},getPointPosition:function(e,t){var n=this.getIndexAngle(e)-Math.PI/2;return{x:Math.cos(n)*t+this.xCenter,y:Math.sin(n)*t+this.yCenter}},getPointPositionForValue:function(e,t){return this.getPointPosition(e,this.getDistanceFromCenterForValue(t))},getBasePosition:function(e){var t=this.min,n=this.max;return this.getPointPositionForValue(e||0,this.beginAtZero?0:t<0&&n<0?n:t>0&&n>0?t:0)},_drawGrid:function(){var e,t,n,r=this,i=r.ctx,a=r.options,o=a.gridLines,l=a.angleLines,u=Nn(l.lineWidth,o.lineWidth),s=Nn(l.color,o.color);if(a.pointLabels.display&&function(e){var t=e.ctx,n=e.options,r=n.pointLabels,i=Fn(n),a=e.getDistanceFromCenterForValue(n.ticks.reverse?e.min:e.max),o=V.options._parseFont(r);t.save(),t.font=o.string,t.textBaseline="middle";for(var l=e.chart.data.labels.length-1;l>=0;l--){var u=0===l?i/2:0,s=e.getPointPosition(l,a+u+5),c=zn(r.fontColor,l,R.global.defaultFontColor);t.fillStyle=c;var f=e.getIndexAngle(l),d=V.toDegrees(f);t.textAlign=Bn(d),Vn(d,e._pointLabelSizes[l],s),Wn(t,e.pointLabels[l],s,o.lineHeight)}t.restore()}(r),o.display&&V.each(r.ticks,(function(e,n){0!==n&&(t=r.getDistanceFromCenterForValue(r.ticksAsNumbers[n]),function(e,t,n,r){var i,a=e.ctx,o=t.circular,l=e.chart.data.labels.length,u=zn(t.color,r-1),s=zn(t.lineWidth,r-1);if((o||l)&&u&&s){if(a.save(),a.strokeStyle=u,a.lineWidth=s,a.setLineDash&&(a.setLineDash(t.borderDash||[]),a.lineDashOffset=t.borderDashOffset||0),a.beginPath(),o)a.arc(e.xCenter,e.yCenter,n,0,2*Math.PI);else{i=e.getPointPosition(0,n),a.moveTo(i.x,i.y);for(var c=1;c<l;c++)i=e.getPointPosition(c,n),a.lineTo(i.x,i.y)}a.closePath(),a.stroke(),a.restore()}}(r,o,t,n))})),l.display&&u&&s){for(i.save(),i.lineWidth=u,i.strokeStyle=s,i.setLineDash&&(i.setLineDash(jn([l.borderDash,o.borderDash,[]])),i.lineDashOffset=jn([l.borderDashOffset,o.borderDashOffset,0])),e=r.chart.data.labels.length-1;e>=0;e--)t=r.getDistanceFromCenterForValue(a.ticks.reverse?r.min:r.max),n=r.getPointPosition(e,t),i.beginPath(),i.moveTo(r.xCenter,r.yCenter),i.lineTo(n.x,n.y),i.stroke();i.restore()}},_drawLabels:function(){var e=this,t=e.ctx,n=e.options.ticks;if(n.display){var r,i,a=e.getIndexAngle(0),o=V.options._parseFont(n),l=Nn(n.fontColor,R.global.defaultFontColor);t.save(),t.font=o.string,t.translate(e.xCenter,e.yCenter),t.rotate(a),t.textAlign="center",t.textBaseline="middle",V.each(e.ticks,(function(a,u){(0!==u||n.reverse)&&(r=e.getDistanceFromCenterForValue(e.ticksAsNumbers[u]),n.showLabelBackdrop&&(i=t.measureText(a).width,t.fillStyle=n.backdropColor,t.fillRect(-i/2-n.backdropPaddingX,-r-o.size/2-n.backdropPaddingY,i+2*n.backdropPaddingX,o.size+2*n.backdropPaddingY)),t.fillStyle=l,t.fillText(a,0,-r))})),t.restore()}},_drawTitle:V.noop}),qn=Rn;Hn._defaults=qn;var $n=V._deprecated,Qn=V.options.resolve,Yn=V.valueOrDefault,Kn=Number.MIN_SAFE_INTEGER||-9007199254740991,Gn=Number.MAX_SAFE_INTEGER||9007199254740991,Zn={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Xn=Object.keys(Zn);function Jn(e,t){return e-t}function er(e){return V.valueOrDefault(e.time.min,e.ticks.min)}function tr(e){return V.valueOrDefault(e.time.max,e.ticks.max)}function nr(e,t,n,r){var i=function(e,t,n){for(var r,i,a,o=0,l=e.length-1;o>=0&&o<=l;){if(i=e[(r=o+l>>1)-1]||null,a=e[r],!i)return{lo:null,hi:a};if(a[t]<n)o=r+1;else{if(!(i[t]>n))return{lo:i,hi:a};l=r-1}}return{lo:a,hi:null}}(e,t,n),a=i.lo?i.hi?i.lo:e[e.length-2]:e[0],o=i.lo?i.hi?i.hi:e[e.length-1]:e[1],l=o[t]-a[t],u=l?(n-a[t])/l:0,s=(o[r]-a[r])*u;return a[r]+s}function rr(e,t){var n=e._adapter,r=e.options.time,i=r.parser,a=i||r.format,o=t;return"function"==typeof i&&(o=i(o)),V.isFinite(o)||(o="string"==typeof a?n.parse(o,a):n.parse(o)),null!==o?+o:(i||"function"!=typeof a||(o=a(t),V.isFinite(o)||(o=n.parse(o))),o)}function ir(e,t){if(V.isNullOrUndef(t))return null;var n=e.options.time,r=rr(e,e.getRightValue(t));return null===r||n.round&&(r=+e._adapter.startOf(r,n.round)),r}function ar(e,t,n,r){var i,a,o,l=Xn.length;for(i=Xn.indexOf(e);i<l-1;++i)if(o=(a=Zn[Xn[i]]).steps?a.steps:Gn,a.common&&Math.ceil((n-t)/(o*a.size))<=r)return Xn[i];return Xn[l-1]}function or(e,t,n){var r,i,a=[],o={},l=t.length;for(r=0;r<l;++r)o[i=t[r]]=r,a.push({value:i,major:!1});return 0!==l&&n?function(e,t,n,r){var i,a,o=e._adapter,l=+o.startOf(t[0].value,r),u=t[t.length-1].value;for(i=l;i<=u;i=+o.add(i,1,r))(a=n[i])>=0&&(t[a].major=!0);return t}(e,a,o,n):a}var lr=vn.extend({initialize:function(){this.mergeTicksOptions(),vn.prototype.initialize.call(this)},update:function(){var e=this,t=e.options,n=t.time||(t.time={}),r=e._adapter=new en._date(t.adapters.date);return $n("time scale",n.format,"time.format","time.parser"),$n("time scale",n.min,"time.min","ticks.min"),$n("time scale",n.max,"time.max","ticks.max"),V.mergeIf(n.displayFormats,r.formats()),vn.prototype.update.apply(e,arguments)},getRightValue:function(e){return e&&void 0!==e.t&&(e=e.t),vn.prototype.getRightValue.call(this,e)},determineDataLimits:function(){var e,t,n,r,i,a,o,l=this,u=l.chart,s=l._adapter,c=l.options,f=c.time.unit||"day",d=Gn,h=Kn,p=[],g=[],v=[],m=l._getLabels();for(e=0,n=m.length;e<n;++e)v.push(ir(l,m[e]));for(e=0,n=(u.data.datasets||[]).length;e<n;++e)if(u.isDatasetVisible(e))if(i=u.data.datasets[e].data,V.isObject(i[0]))for(g[e]=[],t=0,r=i.length;t<r;++t)a=ir(l,i[t]),p.push(a),g[e][t]=a;else g[e]=v.slice(0),o||(p=p.concat(v),o=!0);else g[e]=[];v.length&&(d=Math.min(d,v[0]),h=Math.max(h,v[v.length-1])),p.length&&(p=n>1?function(e){var t,n,r,i={},a=[];for(t=0,n=e.length;t<n;++t)i[r=e[t]]||(i[r]=!0,a.push(r));return a}(p).sort(Jn):p.sort(Jn),d=Math.min(d,p[0]),h=Math.max(h,p[p.length-1])),d=ir(l,er(c))||d,h=ir(l,tr(c))||h,d=d===Gn?+s.startOf(Date.now(),f):d,h=h===Kn?+s.endOf(Date.now(),f)+1:h,l.min=Math.min(d,h),l.max=Math.max(d+1,h),l._table=[],l._timestamps={data:p,datasets:g,labels:v}},buildTicks:function(){var e,t,n,r=this,i=r.min,a=r.max,o=r.options,l=o.ticks,u=o.time,s=r._timestamps,c=[],f=r.getLabelCapacity(i),d=l.source,h=o.distribution;for(s="data"===d||"auto"===d&&"series"===h?s.data:"labels"===d?s.labels:function(e,t,n,r){var i,a=e._adapter,o=e.options,l=o.time,u=l.unit||ar(l.minUnit,t,n,r),s=Qn([l.stepSize,l.unitStepSize,1]),c="week"===u&&l.isoWeekday,f=t,d=[];if(c&&(f=+a.startOf(f,"isoWeek",c)),f=+a.startOf(f,c?"day":u),a.diff(n,t,u)>1e5*s)throw t+" and "+n+" are too far apart with stepSize of "+s+" "+u;for(i=f;i<n;i=+a.add(i,s,u))d.push(i);return i!==n&&"ticks"!==o.bounds||d.push(i),d}(r,i,a,f),"ticks"===o.bounds&&s.length&&(i=s[0],a=s[s.length-1]),i=ir(r,er(o))||i,a=ir(r,tr(o))||a,e=0,t=s.length;e<t;++e)(n=s[e])>=i&&n<=a&&c.push(n);return r.min=i,r.max=a,r._unit=u.unit||(l.autoSkip?ar(u.minUnit,r.min,r.max,f):function(e,t,n,r,i){var a,o;for(a=Xn.length-1;a>=Xn.indexOf(n);a--)if(o=Xn[a],Zn[o].common&&e._adapter.diff(i,r,o)>=t-1)return o;return Xn[n?Xn.indexOf(n):0]}(r,c.length,u.minUnit,r.min,r.max)),r._majorUnit=l.major.enabled&&"year"!==r._unit?function(e){for(var t=Xn.indexOf(e)+1,n=Xn.length;t<n;++t)if(Zn[Xn[t]].common)return Xn[t]}(r._unit):void 0,r._table=function(e,t,n,r){if("linear"===r||!e.length)return[{time:t,pos:0},{time:n,pos:1}];var i,a,o,l,u,s=[],c=[t];for(i=0,a=e.length;i<a;++i)(l=e[i])>t&&l<n&&c.push(l);for(c.push(n),i=0,a=c.length;i<a;++i)u=c[i+1],o=c[i-1],l=c[i],void 0!==o&&void 0!==u&&Math.round((u+o)/2)===l||s.push({time:l,pos:i/(a-1)});return s}(r._timestamps.data,i,a,h),r._offsets=function(e,t,n,r,i){var a,o,l=0,u=0;return i.offset&&t.length&&(a=nr(e,"time",t[0],"pos"),l=1===t.length?1-a:(nr(e,"time",t[1],"pos")-a)/2,o=nr(e,"time",t[t.length-1],"pos"),u=1===t.length?o:(o-nr(e,"time",t[t.length-2],"pos"))/2),{start:l,end:u,factor:1/(l+1+u)}}(r._table,c,0,0,o),l.reverse&&c.reverse(),or(r,c,r._majorUnit)},getLabelForIndex:function(e,t){var n=this,r=n._adapter,i=n.chart.data,a=n.options.time,o=i.labels&&e<i.labels.length?i.labels[e]:"",l=i.datasets[t].data[e];return V.isObject(l)&&(o=n.getRightValue(l)),a.tooltipFormat?r.format(rr(n,o),a.tooltipFormat):"string"==typeof o?o:r.format(rr(n,o),a.displayFormats.datetime)},tickFormatFunction:function(e,t,n,r){var i=this._adapter,a=this.options,o=a.time.displayFormats,l=o[this._unit],u=this._majorUnit,s=o[u],c=n[t],f=a.ticks,d=u&&s&&c&&c.major,h=i.format(e,r||(d?s:l)),p=d?f.major:f.minor,g=Qn([p.callback,p.userCallback,f.callback,f.userCallback]);return g?g(h,t,n):h},convertTicksToLabels:function(e){var t,n,r=[];for(t=0,n=e.length;t<n;++t)r.push(this.tickFormatFunction(e[t].value,t,e));return r},getPixelForOffset:function(e){var t=this._offsets,n=nr(this._table,"time",e,"pos");return this.getPixelForDecimal((t.start+n)*t.factor)},getPixelForValue:function(e,t,n){var r=null;if(void 0!==t&&void 0!==n&&(r=this._timestamps.datasets[n][t]),null===r&&(r=ir(this,e)),null!==r)return this.getPixelForOffset(r)},getPixelForTick:function(e){var t=this.getTicks();return e>=0&&e<t.length?this.getPixelForOffset(t[e].value):null},getValueForPixel:function(e){var t=this._offsets,n=this.getDecimalForPixel(e)/t.factor-t.end,r=nr(this._table,"pos",n,"time");return this._adapter._create(r)},_getLabelSize:function(e){var t=this.options.ticks,n=this.ctx.measureText(e).width,r=V.toRadians(this.isHorizontal()?t.maxRotation:t.minRotation),i=Math.cos(r),a=Math.sin(r),o=Yn(t.fontSize,R.global.defaultFontSize);return{w:n*i+o*a,h:n*a+o*i}},getLabelWidth:function(e){return this._getLabelSize(e).w},getLabelCapacity:function(e){var t=this,n=t.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,a=t.tickFormatFunction(e,0,or(t,[e],t._majorUnit),i),o=t._getLabelSize(a),l=Math.floor(t.isHorizontal()?t.width/o.w:t.height/o.h);return t.options.offset&&l--,l>0?l:1}}),ur={position:"bottom",distribution:"linear",bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,displayFormat:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{autoSkip:!1,source:"auto",major:{enabled:!1}}};lr._defaults=ur;var sr={category:bn,linear:En,logarithmic:An,radialLinear:Hn,time:lr},cr={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};en._date.override("function"==typeof e?{_id:"moment",formats:function(){return cr},parse:function(t,n){return"string"==typeof t&&"string"==typeof n?t=e(t,n):t instanceof e||(t=e(t)),t.isValid()?t.valueOf():null},format:function(t,n){return e(t).format(n)},add:function(t,n,r){return e(t).add(n,r).valueOf()},diff:function(t,n,r){return e(t).diff(e(n),r)},startOf:function(t,n,r){return t=e(t),"isoWeek"===n?t.isoWeekday(r).valueOf():t.startOf(n).valueOf()},endOf:function(t,n){return e(t).endOf(n).valueOf()},_create:function(t){return e(t)}}:{}),R._set("global",{plugins:{filler:{propagate:!0}}});var fr={dataset:function(e){var t=e.fill,n=e.chart,r=n.getDatasetMeta(t),i=r&&n.isDatasetVisible(t)&&r.dataset._children||[],a=i.length||0;return a?function(e,t){return t<a&&i[t]._view||null}:null},boundary:function(e){var t=e.boundary,n=t?t.x:null,r=t?t.y:null;return V.isArray(t)?function(e,n){return t[n]}:function(e){return{x:null===n?e.x:n,y:null===r?e.y:r}}}};function dr(e,t,n){var r,i=e._model||{},a=i.fill;if(void 0===a&&(a=!!i.backgroundColor),!1===a||null===a)return!1;if(!0===a)return"origin";if(r=parseFloat(a,10),isFinite(r)&&Math.floor(r)===r)return"-"!==a[0]&&"+"!==a[0]||(r=t+r),!(r===t||r<0||r>=n)&&r;switch(a){case"bottom":return"start";case"top":return"end";case"zero":return"origin";case"origin":case"start":case"end":return a;default:return!1}}function hr(e){return(e.el._scale||{}).getPointPositionForValue?function(e){var t,n,r,i,a,o=e.el._scale,l=o.options,u=o.chart.data.labels.length,s=e.fill,c=[];if(!u)return null;for(t=l.ticks.reverse?o.max:o.min,n=l.ticks.reverse?o.min:o.max,r=o.getPointPositionForValue(0,t),i=0;i<u;++i)a="start"===s||"end"===s?o.getPointPositionForValue(i,"start"===s?t:n):o.getBasePosition(i),l.gridLines.circular&&(a.cx=r.x,a.cy=r.y,a.angle=o.getIndexAngle(i)-Math.PI/2),c.push(a);return c}(e):function(e){var t,n=e.el._model||{},r=e.el._scale||{},i=e.fill,a=null;if(isFinite(i))return null;if("start"===i?a=void 0===n.scaleBottom?r.bottom:n.scaleBottom:"end"===i?a=void 0===n.scaleTop?r.top:n.scaleTop:void 0!==n.scaleZero?a=n.scaleZero:r.getBasePixel&&(a=r.getBasePixel()),null!=a){if(void 0!==a.x&&void 0!==a.y)return a;if(V.isFinite(a))return{x:(t=r.isHorizontal())?a:null,y:t?null:a}}return null}(e)}function pr(e,t,n){var r,i=e[t].fill,a=[t];if(!n)return i;for(;!1!==i&&-1===a.indexOf(i);){if(!isFinite(i))return i;if(!(r=e[i]))return!1;if(r.visible)return i;a.push(i),i=r.fill}return!1}function gr(e){var t=e.fill,n="dataset";return!1===t?null:(isFinite(t)||(n="boundary"),fr[n](e))}function vr(e){return e&&!e.skip}function mr(e,t,n,r,i){var a,o,l,u;if(r&&i){for(e.moveTo(t[0].x,t[0].y),a=1;a<r;++a)V.canvas.lineTo(e,t[a-1],t[a]);if(void 0===n[0].angle)for(e.lineTo(n[i-1].x,n[i-1].y),a=i-1;a>0;--a)V.canvas.lineTo(e,n[a],n[a-1],!0);else for(o=n[0].cx,l=n[0].cy,u=Math.sqrt(Math.pow(n[0].x-o,2)+Math.pow(n[0].y-l,2)),a=i-1;a>0;--a)e.arc(o,l,u,n[a].angle,n[a-1].angle,!0)}}function br(e,t,n,r,i,a){var o,l,u,s,c,f,d,h,p=t.length,g=r.spanGaps,v=[],m=[],b=0,y=0;for(e.beginPath(),o=0,l=p;o<l;++o)c=n(s=t[u=o%p]._view,u,r),f=vr(s),d=vr(c),a&&void 0===h&&f&&(l=p+(h=o+1)),f&&d?(b=v.push(s),y=m.push(c)):b&&y&&(g?(f&&v.push(s),d&&m.push(c)):(mr(e,v,m,b,y),b=y=0,v=[],m=[]));mr(e,v,m,b,y),e.closePath(),e.fillStyle=i,e.fill()}var yr={id:"filler",afterDatasetsUpdate:function(e,t){var n,r,i,a,o=(e.data.datasets||[]).length,l=t.propagate,u=[];for(r=0;r<o;++r)a=null,(i=(n=e.getDatasetMeta(r)).dataset)&&i._model&&i instanceof we.Line&&(a={visible:e.isDatasetVisible(r),fill:dr(i,r,o),chart:e,el:i}),n.$filler=a,u.push(a);for(r=0;r<o;++r)(a=u[r])&&(a.fill=pr(u,r,l),a.boundary=hr(a),a.mapper=gr(a))},beforeDatasetsDraw:function(e){var t,n,r,i,a,o,l,u=e._getSortedVisibleDatasetMetas(),s=e.ctx;for(n=u.length-1;n>=0;--n)(t=u[n].$filler)&&t.visible&&(i=(r=t.el)._view,a=r._children||[],o=t.mapper,l=i.backgroundColor||R.global.defaultColor,o&&l&&a.length&&(V.canvas.clipArea(s,e.chartArea),br(s,a,o,i,l,r._loop),V.canvas.unclipArea(s)))}},xr=V.rtl.getRtlAdapter,_r=V.noop,wr=V.valueOrDefault;function kr(e,t){return e.usePointStyle&&e.boxWidth>t?t:e.boxWidth}R._set("global",{legend:{display:!0,position:"top",align:"center",fullWidth:!0,reverse:!1,weight:1e3,onClick:function(e,t){var n=t.datasetIndex,r=this.chart,i=r.getDatasetMeta(n);i.hidden=null===i.hidden?!r.data.datasets[n].hidden:null,r.update()},onHover:null,onLeave:null,labels:{boxWidth:40,padding:10,generateLabels:function(e){var t=e.data.datasets,n=e.options.legend||{},r=n.labels&&n.labels.usePointStyle;return e._getSortedDatasetMetas().map((function(n){var i=n.controller.getStyle(r?0:void 0);return{text:t[n.index].label,fillStyle:i.backgroundColor,hidden:!e.isDatasetVisible(n.index),lineCap:i.borderCapStyle,lineDash:i.borderDash,lineDashOffset:i.borderDashOffset,lineJoin:i.borderJoinStyle,lineWidth:i.borderWidth,strokeStyle:i.borderColor,pointStyle:i.pointStyle,rotation:i.rotation,datasetIndex:n.index}}),this)}}},legendCallback:function(e){var t,n,r,i=document.createElement("ul"),a=e.data.datasets;for(i.setAttribute("class",e.id+"-legend"),t=0,n=a.length;t<n;t++)(r=i.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=a[t].backgroundColor,a[t].label&&r.appendChild(document.createTextNode(a[t].label));return i.outerHTML}});var Sr=K.extend({initialize:function(e){V.extend(this,e),this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1},beforeUpdate:_r,update:function(e,t,n){var r=this;return r.beforeUpdate(),r.maxWidth=e,r.maxHeight=t,r.margins=n,r.beforeSetDimensions(),r.setDimensions(),r.afterSetDimensions(),r.beforeBuildLabels(),r.buildLabels(),r.afterBuildLabels(),r.beforeFit(),r.fit(),r.afterFit(),r.afterUpdate(),r.minSize},afterUpdate:_r,beforeSetDimensions:_r,setDimensions:function(){var e=this;e.isHorizontal()?(e.width=e.maxWidth,e.left=0,e.right=e.width):(e.height=e.maxHeight,e.top=0,e.bottom=e.height),e.paddingLeft=0,e.paddingTop=0,e.paddingRight=0,e.paddingBottom=0,e.minSize={width:0,height:0}},afterSetDimensions:_r,beforeBuildLabels:_r,buildLabels:function(){var e=this,t=e.options.labels||{},n=V.callback(t.generateLabels,[e.chart],e)||[];t.filter&&(n=n.filter((function(n){return t.filter(n,e.chart.data)}))),e.options.reverse&&n.reverse(),e.legendItems=n},afterBuildLabels:_r,beforeFit:_r,fit:function(){var e=this,t=e.options,n=t.labels,r=t.display,i=e.ctx,a=V.options._parseFont(n),o=a.size,l=e.legendHitBoxes=[],u=e.minSize,s=e.isHorizontal();if(s?(u.width=e.maxWidth,u.height=r?10:0):(u.width=r?10:0,u.height=e.maxHeight),r){if(i.font=a.string,s){var c=e.lineWidths=[0],f=0;i.textAlign="left",i.textBaseline="middle",V.each(e.legendItems,(function(e,t){var r=kr(n,o)+o/2+i.measureText(e.text).width;(0===t||c[c.length-1]+r+2*n.padding>u.width)&&(f+=o+n.padding,c[c.length-(t>0?0:1)]=0),l[t]={left:0,top:0,width:r,height:o},c[c.length-1]+=r+n.padding})),u.height+=f}else{var d=n.padding,h=e.columnWidths=[],p=e.columnHeights=[],g=n.padding,v=0,m=0;V.each(e.legendItems,(function(e,t){var r=kr(n,o)+o/2+i.measureText(e.text).width;t>0&&m+o+2*d>u.height&&(g+=v+n.padding,h.push(v),p.push(m),v=0,m=0),v=Math.max(v,r),m+=o+d,l[t]={left:0,top:0,width:r,height:o}})),g+=v,h.push(v),p.push(m),u.width+=g}e.width=u.width,e.height=u.height}else e.width=u.width=e.height=u.height=0},afterFit:_r,isHorizontal:function(){return"top"===this.options.position||"bottom"===this.options.position},draw:function(){var e=this,t=e.options,n=t.labels,r=R.global,i=r.defaultColor,a=r.elements.line,o=e.height,l=e.columnHeights,u=e.width,s=e.lineWidths;if(t.display){var c,f=xr(t.rtl,e.left,e.minSize.width),d=e.ctx,h=wr(n.fontColor,r.defaultFontColor),p=V.options._parseFont(n),g=p.size;d.textAlign=f.textAlign("left"),d.textBaseline="middle",d.lineWidth=.5,d.strokeStyle=h,d.fillStyle=h,d.font=p.string;var v=kr(n,g),m=e.legendHitBoxes,b=function(e,r){switch(t.align){case"start":return n.padding;case"end":return e-r;default:return(e-r+n.padding)/2}},y=e.isHorizontal();c=y?{x:e.left+b(u,s[0]),y:e.top+n.padding,line:0}:{x:e.left+n.padding,y:e.top+b(o,l[0]),line:0},V.rtl.overrideTextDirection(e.ctx,t.textDirection);var x=g+n.padding;V.each(e.legendItems,(function(t,r){var h=d.measureText(t.text).width,p=v+g/2+h,_=c.x,w=c.y;f.setWidth(e.minSize.width),y?r>0&&_+p+n.padding>e.left+e.minSize.width&&(w=c.y+=x,c.line++,_=c.x=e.left+b(u,s[c.line])):r>0&&w+x>e.top+e.minSize.height&&(_=c.x=_+e.columnWidths[c.line]+n.padding,c.line++,w=c.y=e.top+b(o,l[c.line]));var k=f.x(_);!function(e,t,r){if(!(isNaN(v)||v<=0)){d.save();var o=wr(r.lineWidth,a.borderWidth);if(d.fillStyle=wr(r.fillStyle,i),d.lineCap=wr(r.lineCap,a.borderCapStyle),d.lineDashOffset=wr(r.lineDashOffset,a.borderDashOffset),d.lineJoin=wr(r.lineJoin,a.borderJoinStyle),d.lineWidth=o,d.strokeStyle=wr(r.strokeStyle,i),d.setLineDash&&d.setLineDash(wr(r.lineDash,a.borderDash)),n&&n.usePointStyle){var l=v*Math.SQRT2/2,u=f.xPlus(e,v/2),s=t+g/2;V.canvas.drawPoint(d,r.pointStyle,l,u,s,r.rotation)}else d.fillRect(f.leftForLtr(e,v),t,v,g),0!==o&&d.strokeRect(f.leftForLtr(e,v),t,v,g);d.restore()}}(k,w,t),m[r].left=f.leftForLtr(k,m[r].width),m[r].top=w,function(e,t,n,r){var i=g/2,a=f.xPlus(e,v+i),o=t+i;d.fillText(n.text,a,o),n.hidden&&(d.beginPath(),d.lineWidth=2,d.moveTo(a,o),d.lineTo(f.xPlus(a,r),o),d.stroke())}(k,w,t,h),y?c.x+=p+n.padding:c.y+=x})),V.rtl.restoreTextDirection(e.ctx,t.textDirection)}},_getLegendItemAt:function(e,t){var n,r,i,a=this;if(e>=a.left&&e<=a.right&&t>=a.top&&t<=a.bottom)for(i=a.legendHitBoxes,n=0;n<i.length;++n)if(e>=(r=i[n]).left&&e<=r.left+r.width&&t>=r.top&&t<=r.top+r.height)return a.legendItems[n];return null},handleEvent:function(e){var t,n=this,r=n.options,i="mouseup"===e.type?"click":e.type;if("mousemove"===i){if(!r.onHover&&!r.onLeave)return}else{if("click"!==i)return;if(!r.onClick)return}t=n._getLegendItemAt(e.x,e.y),"click"===i?t&&r.onClick&&r.onClick.call(n,e.native,t):(r.onLeave&&t!==n._hoveredItem&&(n._hoveredItem&&r.onLeave.call(n,e.native,n._hoveredItem),n._hoveredItem=t),r.onHover&&t&&r.onHover.call(n,e.native,t))}});function Tr(e,t){var n=new Sr({ctx:e.ctx,options:t,chart:e});gt.configure(e,n,t),gt.addBox(e,n),e.legend=n}var Er={id:"legend",_element:Sr,beforeInit:function(e){var t=e.options.legend;t&&Tr(e,t)},beforeUpdate:function(e){var t=e.options.legend,n=e.legend;t?(V.mergeIf(t,R.global.legend),n?(gt.configure(e,n,t),n.options=t):Tr(e,t)):n&&(gt.removeBox(e,n),delete e.legend)},afterEvent:function(e,t){var n=e.legend;n&&n.handleEvent(t)}},Cr=V.noop;R._set("global",{title:{display:!1,fontStyle:"bold",fullWidth:!0,padding:10,position:"top",text:"",weight:2e3}});var Pr=K.extend({initialize:function(e){V.extend(this,e),this.legendHitBoxes=[]},beforeUpdate:Cr,update:function(e,t,n){var r=this;return r.beforeUpdate(),r.maxWidth=e,r.maxHeight=t,r.margins=n,r.beforeSetDimensions(),r.setDimensions(),r.afterSetDimensions(),r.beforeBuildLabels(),r.buildLabels(),r.afterBuildLabels(),r.beforeFit(),r.fit(),r.afterFit(),r.afterUpdate(),r.minSize},afterUpdate:Cr,beforeSetDimensions:Cr,setDimensions:function(){var e=this;e.isHorizontal()?(e.width=e.maxWidth,e.left=0,e.right=e.width):(e.height=e.maxHeight,e.top=0,e.bottom=e.height),e.paddingLeft=0,e.paddingTop=0,e.paddingRight=0,e.paddingBottom=0,e.minSize={width:0,height:0}},afterSetDimensions:Cr,beforeBuildLabels:Cr,buildLabels:Cr,afterBuildLabels:Cr,beforeFit:Cr,fit:function(){var e,t=this,n=t.options,r=t.minSize={},i=t.isHorizontal();n.display?(e=(V.isArray(n.text)?n.text.length:1)*V.options._parseFont(n).lineHeight+2*n.padding,t.width=r.width=i?t.maxWidth:e,t.height=r.height=i?e:t.maxHeight):t.width=r.width=t.height=r.height=0},afterFit:Cr,isHorizontal:function(){var e=this.options.position;return"top"===e||"bottom"===e},draw:function(){var e=this,t=e.ctx,n=e.options;if(n.display){var r,i,a,o=V.options._parseFont(n),l=o.lineHeight,u=l/2+n.padding,s=0,c=e.top,f=e.left,d=e.bottom,h=e.right;t.fillStyle=V.valueOrDefault(n.fontColor,R.global.defaultFontColor),t.font=o.string,e.isHorizontal()?(i=f+(h-f)/2,a=c+u,r=h-f):(i="left"===n.position?f+u:h-u,a=c+(d-c)/2,r=d-c,s=Math.PI*("left"===n.position?-.5:.5)),t.save(),t.translate(i,a),t.rotate(s),t.textAlign="center",t.textBaseline="middle";var p=n.text;if(V.isArray(p))for(var g=0,v=0;v<p.length;++v)t.fillText(p[v],0,g,r),g+=l;else t.fillText(p,0,0,r);t.restore()}}});function Mr(e,t){var n=new Pr({ctx:e.ctx,options:t,chart:e});gt.configure(e,n,t),gt.addBox(e,n),e.titleBlock=n}var Or={},Ir=yr,Ar=Er,Dr={id:"title",_element:Pr,beforeInit:function(e){var t=e.options.title;t&&Mr(e,t)},beforeUpdate:function(e){var t=e.options.title,n=e.titleBlock;t?(V.mergeIf(t,R.global.title),n?(gt.configure(e,n,t),n.options=t):Mr(e,t)):n&&(gt.removeBox(e,n),delete e.titleBlock)}};for(var Nr in Or.filler=Ir,Or.legend=Ar,Or.title=Dr,Zt.helpers=V,function(){function e(e,t,n){var r;return"string"==typeof e?(r=parseInt(e,10),-1!==e.indexOf("%")&&(r=r/100*t.parentNode[n])):r=e,r}function t(e){return null!=e&&"none"!==e}function n(n,r,i){var a=document.defaultView,o=V._getParentNode(n),l=a.getComputedStyle(n)[r],u=a.getComputedStyle(o)[r],s=t(l),c=t(u),f=Number.POSITIVE_INFINITY;return s||c?Math.min(s?e(l,n,i):f,c?e(u,o,i):f):"none"}V.where=function(e,t){if(V.isArray(e)&&Array.prototype.filter)return e.filter(t);var n=[];return V.each(e,(function(e){t(e)&&n.push(e)})),n},V.findIndex=Array.prototype.findIndex?function(e,t,n){return e.findIndex(t,n)}:function(e,t,n){n=void 0===n?e:n;for(var r=0,i=e.length;r<i;++r)if(t.call(n,e[r],r,e))return r;return-1},V.findNextWhere=function(e,t,n){V.isNullOrUndef(n)&&(n=-1);for(var r=n+1;r<e.length;r++){var i=e[r];if(t(i))return i}},V.findPreviousWhere=function(e,t,n){V.isNullOrUndef(n)&&(n=e.length);for(var r=n-1;r>=0;r--){var i=e[r];if(t(i))return i}},V.isNumber=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},V.almostEquals=function(e,t,n){return Math.abs(e-t)<n},V.almostWhole=function(e,t){var n=Math.round(e);return n-t<=e&&n+t>=e},V.max=function(e){return e.reduce((function(e,t){return isNaN(t)?e:Math.max(e,t)}),Number.NEGATIVE_INFINITY)},V.min=function(e){return e.reduce((function(e,t){return isNaN(t)?e:Math.min(e,t)}),Number.POSITIVE_INFINITY)},V.sign=Math.sign?function(e){return Math.sign(e)}:function(e){return 0==(e=+e)||isNaN(e)?e:e>0?1:-1},V.toRadians=function(e){return e*(Math.PI/180)},V.toDegrees=function(e){return e*(180/Math.PI)},V._decimalPlaces=function(e){if(V.isFinite(e)){for(var t=1,n=0;Math.round(e*t)/t!==e;)t*=10,n++;return n}},V.getAngleFromPoint=function(e,t){var n=t.x-e.x,r=t.y-e.y,i=Math.sqrt(n*n+r*r),a=Math.atan2(r,n);return a<-.5*Math.PI&&(a+=2*Math.PI),{angle:a,distance:i}},V.distanceBetweenPoints=function(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},V.aliasPixel=function(e){return e%2==0?0:.5},V._alignPixel=function(e,t,n){var r=e.currentDevicePixelRatio,i=n/2;return Math.round((t-i)*r)/r+i},V.splineCurve=function(e,t,n,r){var i=e.skip?t:e,a=t,o=n.skip?t:n,l=Math.sqrt(Math.pow(a.x-i.x,2)+Math.pow(a.y-i.y,2)),u=Math.sqrt(Math.pow(o.x-a.x,2)+Math.pow(o.y-a.y,2)),s=l/(l+u),c=u/(l+u),f=r*(s=isNaN(s)?0:s),d=r*(c=isNaN(c)?0:c);return{previous:{x:a.x-f*(o.x-i.x),y:a.y-f*(o.y-i.y)},next:{x:a.x+d*(o.x-i.x),y:a.y+d*(o.y-i.y)}}},V.EPSILON=Number.EPSILON||1e-14,V.splineCurveMonotone=function(e){var t,n,r,i,a,o,l,u,s,c=(e||[]).map((function(e){return{model:e._model,deltaK:0,mK:0}})),f=c.length;for(t=0;t<f;++t)if(!(r=c[t]).model.skip){if(n=t>0?c[t-1]:null,(i=t<f-1?c[t+1]:null)&&!i.model.skip){var d=i.model.x-r.model.x;r.deltaK=0!==d?(i.model.y-r.model.y)/d:0}!n||n.model.skip?r.mK=r.deltaK:!i||i.model.skip?r.mK=n.deltaK:this.sign(n.deltaK)!==this.sign(r.deltaK)?r.mK=0:r.mK=(n.deltaK+r.deltaK)/2}for(t=0;t<f-1;++t)r=c[t],i=c[t+1],r.model.skip||i.model.skip||(V.almostEquals(r.deltaK,0,this.EPSILON)?r.mK=i.mK=0:(a=r.mK/r.deltaK,o=i.mK/r.deltaK,(u=Math.pow(a,2)+Math.pow(o,2))<=9||(l=3/Math.sqrt(u),r.mK=a*l*r.deltaK,i.mK=o*l*r.deltaK)));for(t=0;t<f;++t)(r=c[t]).model.skip||(n=t>0?c[t-1]:null,i=t<f-1?c[t+1]:null,n&&!n.model.skip&&(s=(r.model.x-n.model.x)/3,r.model.controlPointPreviousX=r.model.x-s,r.model.controlPointPreviousY=r.model.y-s*r.mK),i&&!i.model.skip&&(s=(i.model.x-r.model.x)/3,r.model.controlPointNextX=r.model.x+s,r.model.controlPointNextY=r.model.y+s*r.mK))},V.nextItem=function(e,t,n){return n?t>=e.length-1?e[0]:e[t+1]:t>=e.length-1?e[e.length-1]:e[t+1]},V.previousItem=function(e,t,n){return n?t<=0?e[e.length-1]:e[t-1]:t<=0?e[0]:e[t-1]},V.niceNum=function(e,t){var n=Math.floor(V.log10(e)),r=e/Math.pow(10,n);return(t?r<1.5?1:r<3?2:r<7?5:10:r<=1?1:r<=2?2:r<=5?5:10)*Math.pow(10,n)},V.requestAnimFrame="undefined"==typeof window?function(e){e()}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){return window.setTimeout(e,1e3/60)},V.getRelativePosition=function(e,t){var n,r,i=e.originalEvent||e,a=e.target||e.srcElement,o=a.getBoundingClientRect(),l=i.touches;l&&l.length>0?(n=l[0].clientX,r=l[0].clientY):(n=i.clientX,r=i.clientY);var u=parseFloat(V.getStyle(a,"padding-left")),s=parseFloat(V.getStyle(a,"padding-top")),c=parseFloat(V.getStyle(a,"padding-right")),f=parseFloat(V.getStyle(a,"padding-bottom")),d=o.right-o.left-u-c,h=o.bottom-o.top-s-f;return{x:n=Math.round((n-o.left-u)/d*a.width/t.currentDevicePixelRatio),y:r=Math.round((r-o.top-s)/h*a.height/t.currentDevicePixelRatio)}},V.getConstraintWidth=function(e){return n(e,"max-width","clientWidth")},V.getConstraintHeight=function(e){return n(e,"max-height","clientHeight")},V._calculatePadding=function(e,t,n){return(t=V.getStyle(e,t)).indexOf("%")>-1?n*parseInt(t,10)/100:parseInt(t,10)},V._getParentNode=function(e){var t=e.parentNode;return t&&"[object ShadowRoot]"===t.toString()&&(t=t.host),t},V.getMaximumWidth=function(e){var t=V._getParentNode(e);if(!t)return e.clientWidth;var n=t.clientWidth,r=n-V._calculatePadding(t,"padding-left",n)-V._calculatePadding(t,"padding-right",n),i=V.getConstraintWidth(e);return isNaN(i)?r:Math.min(r,i)},V.getMaximumHeight=function(e){var t=V._getParentNode(e);if(!t)return e.clientHeight;var n=t.clientHeight,r=n-V._calculatePadding(t,"padding-top",n)-V._calculatePadding(t,"padding-bottom",n),i=V.getConstraintHeight(e);return isNaN(i)?r:Math.min(r,i)},V.getStyle=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,null).getPropertyValue(t)},V.retinaScale=function(e,t){var n=e.currentDevicePixelRatio=t||"undefined"!=typeof window&&window.devicePixelRatio||1;if(1!==n){var r=e.canvas,i=e.height,a=e.width;r.height=i*n,r.width=a*n,e.ctx.scale(n,n),r.style.height||r.style.width||(r.style.height=i+"px",r.style.width=a+"px")}},V.fontString=function(e,t,n){return t+" "+e+"px "+n},V.longestText=function(e,t,n,r){var i=(r=r||{}).data=r.data||{},a=r.garbageCollect=r.garbageCollect||[];r.font!==t&&(i=r.data={},a=r.garbageCollect=[],r.font=t),e.font=t;var o,l,u,s,c,f=0,d=n.length;for(o=0;o<d;o++)if(null!=(s=n[o])&&!0!==V.isArray(s))f=V.measureText(e,i,a,f,s);else if(V.isArray(s))for(l=0,u=s.length;l<u;l++)null==(c=s[l])||V.isArray(c)||(f=V.measureText(e,i,a,f,c));var h=a.length/2;if(h>n.length){for(o=0;o<h;o++)delete i[a[o]];a.splice(0,h)}return f},V.measureText=function(e,t,n,r,i){var a=t[i];return a||(a=t[i]=e.measureText(i).width,n.push(i)),a>r&&(r=a),r},V.numberOfLabelLines=function(e){var t=1;return V.each(e,(function(e){V.isArray(e)&&e.length>t&&(t=e.length)})),t},V.color=_?function(e){return e instanceof CanvasGradient&&(e=R.global.defaultColor),_(e)}:function(e){return console.error("Color.js not found!"),e},V.getHoverColor=function(e){return e instanceof CanvasPattern||e instanceof CanvasGradient?e:V.color(e).saturate(.5).darken(.1).rgbString()}}(),Zt._adapters=en,Zt.Animation=Z,Zt.animationService=X,Zt.controllers=Xe,Zt.DatasetController=re,Zt.defaults=R,Zt.Element=K,Zt.elements=we,Zt.Interaction=at,Zt.layouts=gt,Zt.platform=Mt,Zt.plugins=Ot,Zt.Scale=vn,Zt.scaleService=It,Zt.Ticks=tn,Zt.Tooltip=Vt,Zt.helpers.each(sr,(function(e,t){Zt.scaleService.registerScaleType(t,e,e._defaults)})),Or)Or.hasOwnProperty(Nr)&&Zt.plugins.register(Or[Nr]);Zt.platform.initialize();var zr=Zt;return"undefined"!=typeof window&&(window.Chart=Zt),Zt.Chart=Zt,Zt.Legend=Or.legend._element,Zt.Title=Or.title._element,Zt.pluginService=Zt.plugins,Zt.PluginBase=Zt.Element.extend({}),Zt.canvasHelpers=Zt.helpers.canvas,Zt.layoutService=Zt.layouts,Zt.LinearScaleBase=wn,Zt.helpers.each(["Bar","Bubble","Doughnut","Line","PolarArea","Radar","Scatter"],(function(e){Zt[e]=function(t,n){return new Zt(t,Zt.helpers.merge(n||{},{type:e.charAt(0).toLowerCase()+e.slice(1)}))}})),zr}(function(){try{return n(61)}catch(e){}}())},function(e,n){if(void 0===t){var r=new Error("Cannot find module 'moment'");throw r.code="MODULE_NOT_FOUND",r}e.exports=t},function(e,t,n){var r=n(15);e.exports=function(e,t){return r(e,t)}},function(e,t,n){var r=n(28),i=n(34),a=n(97),o=n(101),l=n(119),u=n(3),s=n(36),c=n(38),f="[object Object]",d=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,h,p,g){var v=u(e),m=u(t),b=v?"[object Array]":l(e),y=m?"[object Array]":l(t),x=(b="[object Arguments]"==b?f:b)==f,_=(y="[object Arguments]"==y?f:y)==f,w=b==y;if(w&&s(e)){if(!s(t))return!1;v=!0,x=!1}if(w&&!x)return g||(g=new r),v||c(e)?i(e,t,n,h,p,g):a(e,t,b,n,h,p,g);if(!(1&n)){var k=x&&d.call(e,"__wrapped__"),S=_&&d.call(t,"__wrapped__");if(k||S){var T=k?e.value():e,E=S?t.value():t;return g||(g=new r),p(T,E,n,h,g)}}return!!w&&(g||(g=new r),o(e,t,n,h,p,g))}},function(e,t){e.exports=function(){this.__data__=[],this.size=0}},function(e,t,n){var r=n(8),i=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0)&&(n==t.length-1?t.pop():i.call(t,n,1),--this.size,!0)}},function(e,t,n){var r=n(8);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},function(e,t,n){var r=n(8);e.exports=function(e){return r(this.__data__,e)>-1}},function(e,t,n){var r=n(8);e.exports=function(e,t){var n=this.__data__,i=r(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this}},function(e,t,n){var r=n(7);e.exports=function(){this.__data__=new r,this.size=0}},function(e,t){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},function(e,t){e.exports=function(e){return this.__data__.get(e)}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t,n){var r=n(7),i=n(16),a=n(18);e.exports=function(e,t){var n=this.__data__;if(n instanceof r){var o=n.__data__;if(!i||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new a(o)}return n.set(e,t),this.size=n.size,this}},function(e,t,n){var r=n(30),i=n(77),a=n(17),o=n(33),l=/^\[object .+?Constructor\]$/,u=Function.prototype,s=Object.prototype,c=u.toString,f=s.hasOwnProperty,d=RegExp("^"+c.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||i(e))&&(r(e)?d:l).test(o(e))}},function(e,t,n){var r=n(9),i=Object.prototype,a=i.hasOwnProperty,o=i.toString,l=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,l),n=e[l];try{e[l]=void 0;var r=!0}catch(e){}var i=o.call(e);return r&&(t?e[l]=n:delete e[l]),i}},function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},function(e,t,n){var r,i=n(78),a=(r=/[^.]+$/.exec(i&&i.keys&&i.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!a&&a in e}},function(e,t,n){var r=n(2)["__core-js_shared__"];e.exports=r},function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},function(e,t,n){var r=n(81),i=n(7),a=n(16);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(a||i),string:new r}}},function(e,t,n){var r=n(82),i=n(83),a=n(84),o=n(85),l=n(86);function u(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}u.prototype.clear=r,u.prototype.delete=i,u.prototype.get=a,u.prototype.has=o,u.prototype.set=l,e.exports=u},function(e,t,n){var r=n(10);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},function(e,t,n){var r=n(10),i=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return i.call(t,e)?t[e]:void 0}},function(e,t,n){var r=n(10),i=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:i.call(t,e)}},function(e,t,n){var r=n(10);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},function(e,t,n){var r=n(11);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},function(e,t,n){var r=n(11);e.exports=function(e){return r(this,e).get(e)}},function(e,t,n){var r=n(11);e.exports=function(e){return r(this,e).has(e)}},function(e,t,n){var r=n(11);e.exports=function(e,t){var n=r(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this}},function(e,t,n){var r=n(18),i=n(93),a=n(94);function o(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}o.prototype.add=o.prototype.push=i,o.prototype.has=a,e.exports=o},function(e,t){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}},function(e,t){e.exports=function(e,t){return e.has(t)}},function(e,t,n){var r=n(9),i=n(98),a=n(29),o=n(34),l=n(99),u=n(100),s=r?r.prototype:void 0,c=s?s.valueOf:void 0;e.exports=function(e,t,n,r,s,f,d){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!f(new i(e),new i(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return a(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var h=l;case"[object Set]":var p=1&r;if(h||(h=u),e.size!=t.size&&!p)return!1;var g=d.get(e);if(g)return g==t;r|=2,d.set(e,t);var v=o(h(e),h(t),r,s,f,d);return d.delete(e),v;case"[object Symbol]":if(c)return c.call(e)==c.call(t)}return!1}},function(e,t,n){var r=n(2).Uint8Array;e.exports=r},function(e,t){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}},function(e,t){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}},function(e,t,n){var r=n(102),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,a,o,l){var u=1&n,s=r(e),c=s.length;if(c!=r(t).length&&!u)return!1;for(var f=c;f--;){var d=s[f];if(!(u?d in t:i.call(t,d)))return!1}var h=l.get(e),p=l.get(t);if(h&&p)return h==t&&p==e;var g=!0;l.set(e,t),l.set(t,e);for(var v=u;++f<c;){var m=e[d=s[f]],b=t[d];if(a)var y=u?a(b,m,d,t,e,l):a(m,b,d,e,t,l);if(!(void 0===y?m===b||o(m,b,n,a,l):y)){g=!1;break}v||(v="constructor"==d)}if(g&&!v){var x=e.constructor,_=t.constructor;x==_||!("constructor"in e)||!("constructor"in t)||"function"==typeof x&&x instanceof x&&"function"==typeof _&&_ instanceof _||(g=!1)}return l.delete(e),l.delete(t),g}},function(e,t,n){var r=n(103),i=n(105),a=n(19);e.exports=function(e){return r(e,a,i)}},function(e,t,n){var r=n(104),i=n(3);e.exports=function(e,t,n){var a=t(e);return i(e)?a:r(a,n(e))}},function(e,t){e.exports=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}},function(e,t,n){var r=n(106),i=n(107),a=Object.prototype.propertyIsEnumerable,o=Object.getOwnPropertySymbols,l=o?function(e){return null==e?[]:(e=Object(e),r(o(e),(function(t){return a.call(e,t)})))}:i;e.exports=l},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,a=[];++n<r;){var o=e[n];t(o,n,e)&&(a[i++]=o)}return a}},function(e,t){e.exports=function(){return[]}},function(e,t,n){var r=n(109),i=n(35),a=n(3),o=n(36),l=n(37),u=n(38),s=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=a(e),c=!n&&i(e),f=!n&&!c&&o(e),d=!n&&!c&&!f&&u(e),h=n||c||f||d,p=h?r(e.length,String):[],g=p.length;for(var v in e)!t&&!s.call(e,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||d&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||l(v,g))||p.push(v);return p}},function(e,t){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},function(e,t,n){var r=n(5),i=n(6);e.exports=function(e){return i(e)&&"[object Arguments]"==r(e)}},function(e,t){e.exports=function(){return!1}},function(e,t,n){var r=n(5),i=n(21),a=n(6),o={};o["[object Float32Array]"]=o["[object Float64Array]"]=o["[object Int8Array]"]=o["[object Int16Array]"]=o["[object Int32Array]"]=o["[object Uint8Array]"]=o["[object Uint8ClampedArray]"]=o["[object Uint16Array]"]=o["[object Uint32Array]"]=!0,o["[object Arguments]"]=o["[object Array]"]=o["[object ArrayBuffer]"]=o["[object Boolean]"]=o["[object DataView]"]=o["[object Date]"]=o["[object Error]"]=o["[object Function]"]=o["[object Map]"]=o["[object Number]"]=o["[object Object]"]=o["[object RegExp]"]=o["[object Set]"]=o["[object String]"]=o["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&i(e.length)&&!!o[r(e)]}},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t,n){(function(e){var r=n(31),i=t&&!t.nodeType&&t,a=i&&"object"==typeof e&&e&&!e.nodeType&&e,o=a&&a.exports===i&&r.process,l=function(){try{var e=a&&a.require&&a.require("util").types;return e||o&&o.binding&&o.binding("util")}catch(e){}}();e.exports=l}).call(this,n(20)(e))},function(e,t,n){var r=n(116),i=n(117),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return i(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},function(e,t,n){var r=n(118)(Object.keys,Object);e.exports=r},function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},function(e,t,n){var r=n(120),i=n(16),a=n(121),o=n(122),l=n(123),u=n(5),s=n(33),c=s(r),f=s(i),d=s(a),h=s(o),p=s(l),g=u;(r&&"[object DataView]"!=g(new r(new ArrayBuffer(1)))||i&&"[object Map]"!=g(new i)||a&&"[object Promise]"!=g(a.resolve())||o&&"[object Set]"!=g(new o)||l&&"[object WeakMap]"!=g(new l))&&(g=function(e){var t=u(e),n="[object Object]"==t?e.constructor:void 0,r=n?s(n):"";if(r)switch(r){case c:return"[object DataView]";case f:return"[object Map]";case d:return"[object Promise]";case h:return"[object Set]";case p:return"[object WeakMap]"}return t}),e.exports=g},function(e,t,n){var r=n(4)(n(2),"DataView");e.exports=r},function(e,t,n){var r=n(4)(n(2),"Promise");e.exports=r},function(e,t,n){var r=n(4)(n(2),"Set");e.exports=r},function(e,t,n){var r=n(4)(n(2),"WeakMap");e.exports=r},function(e,t,n){var r=n(125),i=n(127)((function(e,t,n){r(e,n,t)}));e.exports=i},function(e,t,n){var r=n(126);e.exports=function(e,t,n){"__proto__"==t&&r?r(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},function(e,t,n){var r=n(4),i=function(){try{var e=r(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=i},function(e,t,n){var r=n(128),i=n(129),a=n(135),o=n(3);e.exports=function(e,t){return function(n,l){var u=o(n)?r:i,s=t?t():{};return u(n,e,a(l,2),s)}}},function(e,t){e.exports=function(e,t,n,r){for(var i=-1,a=null==e?0:e.length;++i<a;){var o=e[i];t(r,o,n(o),e)}return r}},function(e,t,n){var r=n(130);e.exports=function(e,t,n,i){return r(e,(function(e,r,a){t(i,e,n(e),a)})),i}},function(e,t,n){var r=n(131),i=n(134)(r);e.exports=i},function(e,t,n){var r=n(132),i=n(19);e.exports=function(e,t){return e&&r(e,t,i)}},function(e,t,n){var r=n(133)();e.exports=r},function(e,t){e.exports=function(e){return function(t,n,r){for(var i=-1,a=Object(t),o=r(t),l=o.length;l--;){var u=o[e?l:++i];if(!1===n(a[u],u,a))break}return t}}},function(e,t,n){var r=n(39);e.exports=function(e,t){return function(n,i){if(null==n)return n;if(!r(n))return e(n,i);for(var a=n.length,o=t?a:-1,l=Object(n);(t?o--:++o<a)&&!1!==i(l[o],o,l););return n}}},function(e,t,n){var r=n(136),i=n(139),a=n(150),o=n(3),l=n(151);e.exports=function(e){return"function"==typeof e?e:null==e?a:"object"==typeof e?o(e)?i(e[0],e[1]):r(e):l(e)}},function(e,t,n){var r=n(137),i=n(138),a=n(41);e.exports=function(e){var t=i(e);return 1==t.length&&t[0][2]?a(t[0][0],t[0][1]):function(n){return n===e||r(n,e,t)}}},function(e,t,n){var r=n(28),i=n(15);e.exports=function(e,t,n,a){var o=n.length,l=o,u=!a;if(null==e)return!l;for(e=Object(e);o--;){var s=n[o];if(u&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++o<l;){var c=(s=n[o])[0],f=e[c],d=s[1];if(u&&s[2]){if(void 0===f&&!(c in e))return!1}else{var h=new r;if(a)var p=a(f,d,c,e,t,h);if(!(void 0===p?i(d,f,3,a,h):p))return!1}}return!0}},function(e,t,n){var r=n(40),i=n(19);e.exports=function(e){for(var t=i(e),n=t.length;n--;){var a=t[n],o=e[a];t[n]=[a,o,r(o)]}return t}},function(e,t,n){var r=n(15),i=n(140),a=n(147),o=n(22),l=n(40),u=n(41),s=n(12);e.exports=function(e,t){return o(e)&&l(t)?u(s(e),t):function(n){var o=i(n,e);return void 0===o&&o===t?a(n,e):r(t,o,3)}}},function(e,t,n){var r=n(42);e.exports=function(e,t,n){var i=null==e?void 0:r(e,t);return void 0===i?n:i}},function(e,t,n){var r=n(142),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,a=/\\(\\)?/g,o=r((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(i,(function(e,n,r,i){t.push(r?i.replace(a,"$1"):n||e)})),t}));e.exports=o},function(e,t,n){var r=n(143);e.exports=function(e){var t=r(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}},function(e,t,n){var r=n(18);function i(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(i.Cache||r),n}i.Cache=r,e.exports=i},function(e,t,n){var r=n(145);e.exports=function(e){return null==e?"":r(e)}},function(e,t,n){var r=n(9),i=n(146),a=n(3),o=n(23),l=r?r.prototype:void 0,u=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return i(t,e)+"";if(o(t))return u?u.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n}},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}},function(e,t,n){var r=n(148),i=n(149);e.exports=function(e,t){return null!=e&&i(e,t,r)}},function(e,t){e.exports=function(e,t){return null!=e&&t in Object(e)}},function(e,t,n){var r=n(43),i=n(35),a=n(3),o=n(37),l=n(21),u=n(12);e.exports=function(e,t,n){for(var s=-1,c=(t=r(t,e)).length,f=!1;++s<c;){var d=u(t[s]);if(!(f=null!=e&&n(e,d)))break;e=e[d]}return f||++s!=c?f:!!(c=null==e?0:e.length)&&l(c)&&o(d,c)&&(a(e)||i(e))}},function(e,t){e.exports=function(e){return e}},function(e,t,n){var r=n(152),i=n(153),a=n(22),o=n(12);e.exports=function(e){return a(e)?r(o(e)):i(e)}},function(e,t){e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},function(e,t,n){var r=n(42);e.exports=function(e){return function(t){return r(t,e)}}},function(e,t,n){var r=n(155);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(26)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(25)(!1)).push([e.i,'.react-tags {\n  position: relative;\n  padding: 6px 0 0 6px;\n  border: 1px solid #d1d1d1;\n  border-radius: 1px;\n\n  /* shared font styles */\n  font-size: 12px;\n  line-height: 1.2;\n\n  /* clicking anywhere will focus the input */\n  cursor: text;\n}\n\n.react-tags.is-focused {\n  border-color: #b1b1b1;\n}\n\n.react-tags__selected {\n  display: inline;\n}\n\n.react-tags__selected-tag {\n  display: inline-block;\n  box-sizing: border-box;\n  margin: 0 6px 6px 0;\n  padding: 6px 8px;\n  border: 1px solid #d1d1d1;\n  border-radius: 2px;\n  background: #f1f1f1;\n\n  /* match the font styles */\n  font-size: inherit;\n  line-height: inherit;\n}\n\n.react-tags__selected-tag:after {\n  content: "\\2715";\n  color: #aaa;\n  margin-left: 8px;\n}\n\n.react-tags__selected-tag:hover,\n.react-tags__selected-tag:focus {\n  border-color: #b1b1b1;\n}\n\n.react-tags__search {\n  display: inline-block;\n\n  /* match tag layout */\n  padding: 7px 2px;\n  margin-bottom: 6px;\n\n  /* prevent autoresize overflowing the container */\n  max-width: 100%;\n}\n\n@media screen and (min-width: 30em) {\n  .react-tags__search {\n    /* this will become the offsetParent for suggestions */\n    position: relative;\n  }\n}\n\n.react-tags__search input {\n  /* prevent autoresize overflowing the container */\n  max-width: 100%;\n\n  /* remove styles and layout from this element */\n  margin: 0;\n  padding: 0;\n  border: 0;\n  outline: none;\n\n  /* match the font styles */\n  font-size: inherit;\n  line-height: inherit;\n}\n\n.react-tags__search input::-ms-clear {\n  display: none;\n}\n\n.react-tags__suggestions {\n  z-index: 9999999;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n}\n\n@media screen and (min-width: 30em) {\n  .react-tags__suggestions {\n    width: 100px;\n  }\n}\n\n.react-tags__suggestions ul {\n  margin: 4px -1px;\n  padding: 0;\n  list-style: none;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid #d1d1d1;\n  border-radius: 2px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n}\n\n.react-tags__suggestions li {\n  border-bottom: 1px solid #ddd;\n  padding: 6px 8px;\n}\n\n.react-tags__suggestions li mark {\n  text-decoration: underline;\n  background: none;\n  font-weight: 600;\n}\n\n.react-tags__suggestions li:hover {\n  cursor: pointer;\n  background: #eee;\n}\n\n.react-tags__suggestions li.is-active {\n  background: #b7cfe0;\n}\n\n.react-tags__suggestions li.is-disabled {\n  opacity: 0.5;\n  cursor: auto;\n}\n',""])},function(e){e.exports=JSON.parse('{"name":"frontend","version":"0.8.0","private":true,"homepage":".","dependencies":{"@babel/helper-call-delegate":"^7.8.7","@babel/plugin-proposal-class-properties":"^7.5.5","@babel/preset-typescript":"^7.10.1","@types/jest":"^26.0.0","@types/node":"^14.0.13","@types/react":"^16.9.38","@types/react-dom":"^16.9.8","@types/react-tag-autocomplete":"^5.12.0","babel-loader":"^8.0.6","chart.js":"^2.9.4","css-loader":"3.3.0","js-levenshtein":"^1.1.6","react":"^16.9.0","react-chartjs-2":"^2.11.1","react-dom":"^16.9.0","react-scripts":"3.4.1","react-tag-autocomplete":"^5.11.1","typescript":"^3.9.5","webpack-cli":"^3.3.9"},"scripts":{"start":"react-scripts start","build":"react-scripts build","test":"react-scripts test","eject":"react-scripts eject"},"eslintConfig":{"extends":"react-app"},"browserslist":{"production":[">0.2%","not dead","not op_mini all"],"development":["last 1 chrome version","last 1 firefox version","last 1 safari version"]},"devDependencies":{"@types/chart.js":"^2.9.29"}}')},function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return me})),n.d(t,"CaptumInsightsModel",(function(){return be})),n.d(t,"CaptumInsightsView",(function(){return ye}));var r=n(0),i=n.n(r),a=n(44),o=n.n(a),l=n(1),u=n.n(l);var s=function(e){return Array.isArray(e)?e.join(" "):Object.keys(e).filter((function(t){return!!e[t]})).join(" ")};var c=function(){return i.a.createElement("header",{className:u.a.header},i.a.createElement("div",{className:u.a.header__name},"Captum Insights"),i.a.createElement("nav",{className:u.a.header__nav},i.a.createElement("ul",null,i.a.createElement("li",{className:s([u.a.header__nav__item,u.a["header__nav__item--active"]])},"Instance Attribution"))))};var f=function(){return i.a.createElement("div",{className:u.a.spinner})};function d(e){var t=e.limit[0],n=e.limit[1];return i.a.createElement("div",null,e.name,":",i.a.createElement("input",{className:s([u.a.input,u.a["input--narrow"]]),name:e.name,type:"number",value:e.value,min:t,max:n,onChange:e.handleInputChange}))}function h(e){var t=e.limit.map((function(e,t){return i.a.createElement("option",{value:e},e)}));return i.a.createElement("div",null,e.name,":",i.a.createElement("select",{className:u.a.select,name:e.name,value:e.value,onChange:e.handleInputChange},t))}function p(e){return i.a.createElement("div",null,e.name,":",i.a.createElement("input",{className:s([u.a.input,u.a["input--narrow"]]),name:e.name,type:"text",value:e.value,onChange:e.handleInputChange}))}var g=n(45),v=n.n(g);var m,b=function(e){return i.a.createElement(v.a,{tags:e.classes,autofocus:!1,suggestions:e.suggestedClasses,handleDelete:e.handleClassDelete,handleAddition:function(t){if("string"==typeof t.id)throw Error("Invalid tag id received from ReactTags");e.handleClassAdd({id:t.id,name:t.name})},minQueryLength:0,placeholder:"add new class..."})};!function(e){e.Number="number",e.Enum="enum",e.String="string",e.Boolean="boolean"}(m||(m={}));var y=function(e){var t=e.methods.map((function(e,t){return i.a.createElement("option",{key:t,value:e},e)})),n=null;if(e.selectedMethod in e.methodArguments){var r=e.methodArguments[e.selectedMethod];n=Object.keys(r).map((function(t,n){return function(t,n){switch(n.type){case m.Number:return i.a.createElement(d,{key:t,name:t,limit:n.limit,value:n.value,handleInputChange:e.handleArgumentChange});case m.Enum:return i.a.createElement(h,{key:t,name:t,limit:n.limit,value:n.value,handleInputChange:e.handleArgumentChange});case m.String:return i.a.createElement(p,{key:t,name:t,value:n.value,handleInputChange:e.handleArgumentChange});default:throw new Error("Unsupported config type: "+n.type)}}(t,r[t])}))}return i.a.createElement("form",{onSubmit:e.handleSubmit},i.a.createElement("div",{className:u.a["filter-panel"]},i.a.createElement("div",{className:u.a["filter-panel__column"]},i.a.createElement("div",{className:u.a["filter-panel__column__title"]},"Filter by Classes"),i.a.createElement("div",{className:u.a["filter-panel__column__body"]},i.a.createElement(b,{handleClassDelete:e.handleClassDelete,handleClassAdd:e.handleClassAdd,suggestedClasses:e.suggestedClasses,classes:e.classes}))),i.a.createElement("div",{className:u.a["filter-panel__column"]},i.a.createElement("div",{className:u.a["filter-panel__column__title"]},"Filter by Instances"),i.a.createElement("div",{className:u.a["filter-panel__column__body"]},"Prediction:"," ",i.a.createElement("select",{className:u.a.select,name:"prediction",onChange:e.handleInputChange,value:e.prediction},i.a.createElement("option",{value:"all"},"All"),i.a.createElement("option",{value:"correct"},"Correct"),i.a.createElement("option",{value:"incorrect"},"Incorrect")))),i.a.createElement("div",{className:u.a["filter-panel__column"]},i.a.createElement("div",{className:u.a["filter-panel__column__title"]},"Choose Attribution Method"),i.a.createElement("div",{className:u.a["filter-panel__column__body"]},"Attribution Method:"," ",i.a.createElement("select",{className:u.a.select,name:"selected_method",onChange:e.handleInputChange,value:e.selectedMethod},t))),i.a.createElement("div",{className:u.a["filter-panel__column"]},i.a.createElement("div",{className:u.a["filter-panel__column__title"]},"Attribution Method Arguments"),i.a.createElement("div",{className:u.a["filter-panel__column__body"]},n)),i.a.createElement("div",{className:s([u.a["filter-panel__column"],u.a["filter-panel__column--end"]])},i.a.createElement("button",{className:s([u.a.btn,u.a["btn--outline"],u.a["btn--large"]])},"Fetch"))))};function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e){return function(e){if(Array.isArray(e))return w(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return w(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return w(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function k(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function S(e,t){return!t||"object"!==x(t)&&"function"!=typeof t?T(e):t}function T(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function E(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return(C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function P(e,t){return(P=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function M(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e){switch(e.type){case"checkbox":return e.checked;case"number":return parseInt(e.value);default:return e.value}}var I=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&P(e,t)}(l,e);var t,n,r,a,o=(t=l,function(){var e,n=C(t);if(E()){var r=C(this).constructor;e=Reflect.construct(n,arguments,r)}else e=n.apply(this,arguments);return S(this,e)});function l(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),M(T(t=o.call(this,e)),"handleClassDelete",(function(e){var n=t.state.classes.slice(0),r=n.splice(e,1),i=[].concat(_(t.state.suggested_classes),_(r));t.setState({classes:n,suggested_classes:i})})),M(T(t),"handleClassAdd",(function(e){var n=[].concat(_(t.state.classes),[e]),r=t.state.suggested_classes.filter((function(t){return t.id!==e.id}));t.setState({classes:n,suggested_classes:r})})),M(T(t),"handleInputChange",(function(e){var n=e.target,r=O(e.target),i=n.name;t.setState(M({},i,r))})),M(T(t),"handleArgumentChange",(function(e){var n=e.target,r=n.name,i=O(n),a=t.state.method_arguments;a[t.state.selected_method][r].value=i,t.setState({method_arguments:a})})),M(T(t),"handleSubmit",(function(e){var n=t.state.selected_method,r=t.state.method_arguments,i=n in r?r[n]:{},a={};Object.keys(i).forEach((function(e){a[e]=i[e].value}));var o={prediction:t.state.prediction,classes:t.state.classes.map((function(e){return e.name})),attribution_method:n,arguments:a};t.props.fetchData(o),e.preventDefault()}));var n=e.config.classes.map((function(e,t){return{id:t,name:e}}));return t.state={prediction:"all",classes:[],suggested_classes:n,selected_method:e.config.selected_method,method_arguments:e.config.method_arguments},t}return n=l,(r=[{key:"render",value:function(){return i.a.createElement(y,{prediction:this.state.prediction,classes:this.state.classes,suggestedClasses:this.state.suggested_classes,selectedMethod:this.state.selected_method,methodArguments:this.state.method_arguments,methods:this.props.config.methods,handleClassAdd:this.handleClassAdd,handleClassDelete:this.handleClassDelete,handleInputChange:this.handleInputChange,handleArgumentChange:this.handleArgumentChange,handleSubmit:this.handleSubmit})}}])&&k(n.prototype,r),a&&k(n,a),l}(i.a.Component);function A(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=[220,100,80],r=[10,100,67],i=null;i=e>0?n:r;var a=[0,40,t?100:90],o=Math.abs(.01*e);if(o<.02)return"hsl(".concat(a[0],", ").concat(a[1],"%, ").concat(a[2],"%)");var l=[i[0],(i[1]-a[1])*o+a[1],(i[2]-a[2])*o+a[2]];return"hsl(".concat(l[0],", ").concat(l[1],"%, ").concat(l[2],"%)")}var D=function(e){return i.a.createElement("div",{className:u.a.tooltip},i.a.createElement("div",{className:u.a.tooltip__label},e.label))},N=n(46);function z(e){return i.a.createElement(i.a.Fragment,null,e.hideHeaders&&i.a.createElement("div",{className:u.a.panel__column__title},e.data.name," (Image)"),i.a.createElement("div",{className:u.a.panel__column__body},i.a.createElement("div",{className:u.a["model-number-spacer"]}),i.a.createElement("div",{className:u.a.gallery},i.a.createElement("div",{className:u.a.gallery__item},i.a.createElement("div",{className:u.a.gallery__item__image},i.a.createElement("img",{src:"data:image/png;base64,"+e.data.base,alt:"original"})),i.a.createElement("div",{className:u.a.gallery__item__description},"Original")),i.a.createElement("div",{className:u.a.gallery__item},i.a.createElement("div",{className:u.a.gallery__item__image},i.a.createElement("img",{src:"data:image/png;base64,"+e.data.modified,alt:"attribution"})),i.a.createElement("div",{className:u.a.gallery__item__description},"Attribution Magnitude")))))}function j(e){var t=e.data.base.map((function(t,n){var r;return i.a.createElement(i.a.Fragment,null,i.a.createElement("span",{style:{backgroundColor:A(e.data.modified[n],!1)},className:u.a["text-feature-word"]},t,i.a.createElement(D,{label:null===(r=e.data.modified[n])||void 0===r?void 0:r.toFixed(3)}))," ")}));return i.a.createElement(i.a.Fragment,null,e.hideHeaders&&i.a.createElement("div",{className:u.a.panel__column__title},e.data.name," (Text)"),i.a.createElement("div",{className:u.a.panel__column__body},i.a.createElement("div",{className:u.a["model-number-spacer"]}),t))}function R(e){var t={labels:e.data.base,datasets:[{barPercentage:.5,data:e.data.modified,backgroundColor:function(e){return e.dataset&&e.dataset.data&&void 0!==e.datasetIndex?(e.dataset.data[e.dataIndex]||0)<0?"#d45c43":"#80aaff":"#d45c43"}}]};return i.a.createElement(N.Bar,{data:t,width:300,height:50,legend:{display:!1},options:{maintainAspectRatio:!1,scales:{xAxes:[{gridLines:{display:!1}}],yAxes:[{gridLines:{lineWidth:0,zeroLineWidth:1}}]}}})}var F=function(e){var t=e.data;switch(t.type){case"image":return i.a.createElement(z,{data:t,hideHeaders:e.hideHeaders});case"text":return i.a.createElement(j,{data:t,hideHeaders:e.hideHeaders});case"general":return i.a.createElement(R,{data:t});case"empty":return i.a.createElement(i.a.Fragment,null);default:throw new Error("Unsupported feature visualization type: "+t.type)}};function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var B=function(e){var t;return i.a.createElement("button",{onClick:function(t){t.preventDefault(),e.onTargetClick(e.labelIndex,e.inputIndex,e.modelIndex)},className:s((t={},L(t,u.a.btn,!0),L(t,u.a["btn--solid"],e.active),L(t,u.a["btn--outline"],!e.active),t))},e.children)};var W=function(e){return i.a.createElement(i.a.Fragment,null,e.feature_outputs.map((function(e){var t=100*e.contribution,n=t>10?t:t+10;return i.a.createElement("div",{className:u.a["bar-chart__group"]},i.a.createElement("div",{className:u.a["bar-chart__group__bar"],style:{height:n+"px",backgroundColor:A(t)}}),i.a.createElement("div",{className:u.a["bar-chart__group__title"]},e.name))})))};function V(e){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function U(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function H(e,t){return!t||"object"!==V(t)&&"function"!=typeof t?q(e):t}function q(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function Q(e){return(Q=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Y(e,t){return(Y=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var K=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Y(e,t)}(l,e);var t,n,r,a,o=(t=l,function(){var e,n=Q(t);if($()){var r=Q(this).constructor;e=Reflect.construct(n,arguments,r)}else e=n.apply(this,arguments);return H(this,e)});function l(e){var t,n,r,i;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),t=o.call(this,e),n=q(t),i=function(e,n,r){t.setState({loading:!0}),t.props.onTargetClick(e,n,r,(function(){return t.setState({loading:!1})}))},(r="onTargetClick")in n?Object.defineProperty(n,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[r]=i,t.state={loading:!1},t}return n=l,(r=[{key:"render",value:function(){var e=this,t=this.props.data,n=0===this.props.data.model_index,r=t.feature_outputs.map((function(e){return i.a.createElement(F,{data:e,hideHeaders:n})}));return i.a.createElement(i.a.Fragment,null,this.state.loading&&i.a.createElement("div",{className:u.a.loading},i.a.createElement(f,null)),!n&&i.a.createElement("div",{className:u.a["model-separator"]}),i.a.createElement("div",{className:u.a["visualization-container"]},i.a.createElement("div",{className:u.a.panel__column},n&&i.a.createElement("div",{className:u.a.panel__column__title},"Predicted"),i.a.createElement("div",{className:u.a.panel__column__body},i.a.createElement("div",{className:u.a["model-number"]},"Model ",t.model_index+1),t.predicted.map((function(n){return i.a.createElement("div",{className:s([u.a.row,u.a["row--padding"]])},i.a.createElement(B,{onTargetClick:e.onTargetClick,labelIndex:n.index,inputIndex:e.props.instance,modelIndex:e.props.data.model_index,active:n.index===t.active_index},n.label," (",n.score.toFixed(3),")"))})))),i.a.createElement("div",{className:u.a.panel__column},n&&i.a.createElement("div",{className:u.a.panel__column__title},"Label"),i.a.createElement("div",{className:u.a.panel__column__body},i.a.createElement("div",{className:u.a["model-number-spacer"]}),i.a.createElement("div",{className:s([u.a.row,u.a["row--padding"]])},i.a.createElement(B,{onTargetClick:this.onTargetClick,labelIndex:t.actual.index,inputIndex:this.props.instance,modelIndex:this.props.data.model_index,active:t.actual.index===t.active_index},t.actual.label)))),i.a.createElement("div",{className:u.a.panel__column},n&&i.a.createElement("div",{className:u.a.panel__column__title},"Contribution"),i.a.createElement("div",{className:u.a.panel__column__body},i.a.createElement("div",{className:u.a["model-number-spacer"]}),i.a.createElement("div",{className:u.a["bar-chart"]},i.a.createElement(W,{feature_outputs:t.feature_outputs})))),i.a.createElement("div",{className:s([u.a.panel__column,u.a["panel__column--stretch"]])},r)))}}])&&U(n.prototype,r),a&&U(n,a),l}(i.a.Component);function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Z=function(e){var t;return i.a.createElement("div",{className:s((t={},G(t,u.a.panel,!0),G(t,u.a["panel--long"],!0),t))},e.data.map((function(t,n){return i.a.createElement(K,{data:t,instance:e.inputIndex,onTargetClick:e.onTargetClick,key:n})})))};n(154);function X(e){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function J(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ee(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function te(e,t){return!t||"object"!==X(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function ne(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function re(e){return(re=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ie(e,t){return(ie=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ae(e){return e.loading?i.a.createElement("div",{className:"viz"},i.a.createElement("div",{className:s([u.a.panel,u.a["panel--center"]])},i.a.createElement(f,null))):e.data&&0!==e.data.length?i.a.createElement("div",{className:u.a.viz},e.data.map((function(t,n){return i.a.createElement(Z,{data:t,key:n,inputIndex:n,onTargetClick:e.onTargetClick})}))):i.a.createElement("div",{className:u.a.viz},i.a.createElement("div",{className:u.a.panel},i.a.createElement("div",{className:u.a.panel__column},"Please press"," ",i.a.createElement("strong",{className:u.a["text-feature-word"]},"Fetch")," to start loading data.")))}var oe=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ie(e,t)}(l,e);var t,n,r,a,o=(t=l,function(){var e,n=re(t);if(ne()){var r=re(this).constructor;e=Reflect.construct(n,arguments,r)}else e=n.apply(this,arguments);return te(this,e)});function l(){return J(this,l),o.apply(this,arguments)}return n=l,(r=[{key:"componentDidMount",value:function(){this.props.fetchInit()}},{key:"render",value:function(){return i.a.createElement("div",{className:u.a.app},i.a.createElement(c,null),i.a.createElement(I,{fetchData:this.props.fetchData,config:this.props.config,key:this.props.config.classes}),i.a.createElement(ae,{data:this.props.data,loading:this.props.loading,onTargetClick:this.props.onTargetClick}))}}])&&ee(n.prototype,r),a&&ee(n,a),l}(i.a.Component),le=n(13),ue=n(14);function se(e){return(se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ce(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function fe(e,t){return!t||"object"!==se(t)&&"function"!=typeof t?de(e):t}function de(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function he(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function pe(e){return(pe=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ge(e,t){return(ge=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var me=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ge(e,t)}(l,e);var t,n,r,a,o=(t=l,function(){var e,n=pe(t);if(he()){var r=pe(this).constructor;e=Reflect.construct(n,arguments,r)}else e=n.apply(this,arguments);return fe(this,e)});function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),ve(de(t=o.call(this,e)),"_fetchInit",(function(){t.setState({config:t.backbone.model.get("insights_config")})})),ve(de(t),"fetchData",(function(e){t.setState({loading:!0},(function(){t.backbone.model.save({config:e,output:[]})}))})),ve(de(t),"onTargetClick",(function(e,n,r,i){t.setState({callback:i},(function(){t.backbone.model.save({label_details:{labelIndex:e,inputIndex:n,modelIndex:r},attribution:{}})}))})),t.state={data:[],config:{classes:[],methods:[],method_arguments:{}},loading:!1,callback:null},t.backbone=t.props.backbone,t}return n=l,(r=[{key:"componentDidMount",value:function(){this.backbone.model.on("change:output",this._outputChanged,this),this.backbone.model.on("change:attribution",this._attributionChanged,this)}},{key:"_outputChanged",value:function(e,t,n){ue.isEmpty(t)||this.setState({data:t,loading:!1})}},{key:"_attributionChanged",value:function(e,t,n){if(!ue.isEmpty(t)){var r=Object.assign([],this.state.data),i=this.state.callback,a=e.attributes.label_details;r[a.inputIndex][a.modelIndex]=t,this.setState({data:r,callback:null},(function(){i()}))}}},{key:"render",value:function(){return i.a.createElement(oe,{fetchData:this.fetchData,fetchInit:this._fetchInit,onTargetClick:this.onTargetClick,data:this.state.data,config:this.state.config,loading:this.state.loading})}}])&&ce(n.prototype,r),a&&ce(n,a),l}(i.a.Component),be=le.DOMWidgetModel.extend({defaults:ue.extend(le.DOMWidgetModel.prototype.defaults(),{_model_name:"CaptumInsightsModel",_view_name:"CaptumInsightsView",_model_module:"jupyter-captum-insights",_view_module:"jupyter-captum-insights",_model_module_version:"0.1.0",_view_module_version:"0.1.0"})}),ye=le.DOMWidgetView.extend({initialize:function(){var e=document.createElement("div");o.a.render(i.a.createElement(me,{backbone:this}),e),this.el.append(e)}})}])}));