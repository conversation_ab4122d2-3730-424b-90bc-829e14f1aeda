#!/bin/bash

echo "🏗️  Setting up Home Credit Deployment Environment"

# Create directory structure
echo "📁 Creating directories..."
mkdir -p {models,logs,ssl}

# Set permissions
echo "🔒 Setting permissions..."
chmod +x scripts/*.sh
chmod 644 configs/* 2>/dev/null || true
chmod 600 .env.* 2>/dev/null || true

# Create sample model files (if not exist)
if [ ! -f "models/feature_names.json" ]; then
    echo "📝 Creating sample feature names..."
    echo '["feature_1", "feature_2", "feature_3"]' > models/feature_names.json
fi

# Initialize git (if not already)
if [ ! -d ".git" ]; then
    echo "📦 Initializing git repository..."
    git init
    echo "logs/" >> .gitignore
    echo ".env.*" >> .gitignore
    echo "models/*.pth" >> .gitignore
    echo "__pycache__/" >> .gitignore
fi

echo "✅ Setup completed!"
echo ""
echo "📋 Next steps:"
echo "  1. Copy your trained model to models/"
echo "  2. Configure .env.dev"
echo "  3. Run: ./scripts/deploy_dev.sh"
echo ""
echo "📖 See README.md for detailed instructions"
