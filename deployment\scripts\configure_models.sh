#!/bin/bash

echo "🔧 Configuring Home Credit Model Paths"

# Create models directory if it doesn't exist
mkdir -p models

# Check for existing model files and create symlinks
echo "📁 Setting up model file links..."

# Check for the production model
if [ -f "../production_models/home_credit_final_20250719_131630/best_model.pt" ]; then
    echo "✅ Found production model"
    ln -sf "../../production_models/home_credit_final_20250719_131630/best_model.pt" models/best_home_credit_model.pth
else
    echo "⚠️  Production model not found"
fi

# Check for the advanced model
if [ -f "../best_advanced_model.pth" ]; then
    echo "✅ Found advanced model"
    ln -sf "../../best_advanced_model.pth" models/best_advanced_model.pth
else
    echo "⚠️  Advanced model not found"
fi

# Check for optimal threshold config
if [ -f "../optimal_threshold_config.json" ]; then
    echo "✅ Found threshold config"
    ln -sf "../../optimal_threshold_config.json" configs/optimal_threshold_config.json
else
    echo "⚠️  Threshold config not found"
fi

# Create feature names file if it doesn't exist
if [ ! -f "models/feature_names.json" ]; then
    echo "📝 Creating default feature names..."
    cat > models/feature_names.json << 'EOF'
[
    "EXT_SOURCE_2", "DAYS_BIRTH", "EXT_SOURCE_3", "DAYS_EMPLOYED", "AMT_CREDIT_SUM_DEBT",
    "AMT_CREDIT", "AMT_ANNUITY", "AMT_GOODS_PRICE", "REGION_POPULATION_RELATIVE",
    "DAYS_REGISTRATION", "DAYS_ID_PUBLISH", "AMT_INCOME_TOTAL", "CNT_CHILDREN",
    "REGION_RATING_CLIENT", "REGION_RATING_CLIENT_W_CITY", "HOUR_APPR_PROCESS_START"
]
EOF
fi

# Set up environment file
if [ ! -f ".env" ]; then
    echo "📝 Creating environment file..."
    cp .env.dev .env
    echo "✅ Created .env from .env.dev"
else
    echo "✅ Environment file already exists"
fi

echo ""
echo "📊 Model Configuration Summary:"
echo "================================"
ls -la models/ 2>/dev/null || echo "No models directory"
ls -la configs/ 2>/dev/null || echo "No configs directory"
echo ""
echo "✅ Model configuration completed!"
echo ""
echo "📋 Next steps:"
echo "  1. Verify model files are accessible"
echo "  2. Update .env with your settings"
echo "  3. Run: ./scripts/deploy_dev.sh"
